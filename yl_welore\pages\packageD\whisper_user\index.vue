<template>
    <view>
        <view class="page">
            <view style="width: 100%; margin: 0px auto">
                <cu-custom bgColor="top_color" :isSearch="false" :isBack="true">
                    <view slot="backText"></view>
                    <view slot="content" style="color: #000000; font-weight: 600; font-size: 36rpx">@Ta</view>
                </cu-custom>
            </view>
        </view>
        <scroll-view scroll-x class="bg-white nav text-center">
            <view :class="'cu-item ' + (TabCur == 0 ? 'text-blue cur' : '')" @tap="tabSelect" data-id="0" style="padding: 0 40rpx">搜索Ta</view>
            <view :class="'cu-item ' + (TabCur == 1 ? 'text-blue cur' : '')" @tap="tabSelect" data-id="1" style="padding: 0 40rpx">我关注的</view>
        </scroll-view>

        <view v-if="TabCur == 0" class="cu-bar search bg-white" style="margin-top: 20px">
            <view class="search-form round">
                <text class="cuIcon-search"></text>
                <input type="text" @input="set_search" placeholder="Ta的昵称" confirm-type="search" />
            </view>
            <view class="action">
                <button @tap="get_search" class="cu-btn bg-green shadow-blur round">搜索</button>
            </view>
        </view>

        <view class="cu-list menu-avatar">
            <view class="cu-item" @tap="set_user" :data-id="index" v-for="(item, index) in search_list" :key="index">
                <view class="cu-avatar round lg" :style="'background-image:url(' + item.user_head_sculpture + ');'"></view>

                <view class="content">
                    <view class="text-grey">{{ item.user_nick_name }}</view>
                    <view class="text-gray text-sm flex">
                        <text class="text-cut">{{ item.autograph == '' ? '签名是一种态度，我想我可以更酷...' : item.autograph }}</text>
                    </view>
                </view>

                <view class="action">
                    <button class="bg-blue cu-btn icon">
                        @
                        <!-- <text class="cuIcon-roundcheckfill"></text> -->
                    </button>
                </view>
            </view>
        </view>
        <view v-if="list_moen" style="text-align: center; margin: 20px">没有搜索到</view>
        <view v-if="search_list.length > 0" :class="'cu-load ' + (!di_msg ? 'loading' : 'over')"></view>
    </view>
</template>

<script>
const app = getApp();
var http = require('../../../util/http.js');

export default {
    /**
     * 页面的初始数据
     */
    data() {
        return {
            TabCur: 0,
            search: '',
            search_list: [],
            page: 1,
            list_moen: false,
            di_msg: false
        }
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        uni.hideShareMenu();
    },
    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {},
    methods: {
        tabSelect(e) {
            var index = e.currentTarget.dataset.id;
            this.TabCur = index;
            this.search_list = [];
            this.list_moen = false;
            this.page = 1;
            if (index == 1) {
                this.set_search_my_list();
            }
        },
        set_search(item) {
            console.log(item);
            var search = item.detail.value;
            this.search = search;
        },
        //我关注的
        set_search_my_list() {
            var b = app.globalData.api_root + 'Whisper/get_my_search_list';
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.page = this.page;
            var allMsg = that.search_list;
            http.POST(b, {
                params: params,
                success(res) {
                    console.log(res.data);
                    for (var i = 0; i < res.data.length; i++) {
                        allMsg.push(res.data[i]);
                    }
                    that.search_list = allMsg;
                    if (res.data.length == 0 || allMsg.length < 10) {
                        that.di_msg = true;
                    }
                    if (res.data.length == 0 && that.page == 1) {
                        that.list_moen = true;
                    }
                },
                fail() {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success(res) {}
                    });
                }
            });
        },
        //搜索
        get_search() {
            this.search_list = [];
            this.list_moen = false;
            if (this.search == '') {
                uni.showToast({
                    title: '昵称不能为空',
                    icon: 'none',
                    duration: 2000
                });
                return;
            }
            var b = app.globalData.api_root + 'Whisper/get_search_list';
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.search = this.search;
            params.page = this.page;
            var allMsg = that.search_list;
            http.POST(b, {
                params: params,
                success(res) {
                    console.log(res.data);
                    for (var i = 0; i < res.data.length; i++) {
                        allMsg.push(res.data[i]);
                    }
                    that.search_list = allMsg;
                    if (res.data.length == 0 || allMsg.length < 10) {
                        that.di_msg = true;
                    }
                    if (res.data.length == 0 && that.page == 1) {
                        that.list_moen = true;
                    }
                },
                fail() {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success(res) {}
                    });
                }
            });
        },
        /**
         * 选择当前用户
         */
        set_user(d) {
            console.log(d);
            var index = d.currentTarget.dataset.id;
            var info = this.search_list[index];
            var pages = getCurrentPages();
            var prevPage = pages[pages.length - 2]; //上一个页面
            console.log(info);
            prevPage.setData({
                // 将我们想要传递的参数在这里直接setData。上个页面就会执行这里的操作。
                user_info: info
            });
            uni.navigateBack();
        }
    }
};
</script>
<style>
page {
    background-color: #ffffff;
}
</style>
