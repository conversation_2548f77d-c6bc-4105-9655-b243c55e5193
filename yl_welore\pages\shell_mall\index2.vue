<template>
    <view>
        <cu-custom bgColor="bg-white" :isSearch="false" :isBack="$state.diy.elect_sheathe != 0">
            <view slot="backText">返回</view>
            <view slot="content" style="color: #2c2b2b; font-weight: 600; font-size: 36rpx">{{ title }}</view>
        </cu-custom>
        <view class="page" :style="'height: ' + (height * 2 + 20) + 'px'"></view>
        <!-- <view style='margin-left:20px;'>
  <view style='font-size:13px;margin-top:7px;margin-left:5px;font-weight:300;'>
      <text>钱包</text>
      <text style="margin-left:5px;">￥{{user.conch}}</text>
    </view>
</view> -->
        <scroll-view scroll-x class="nav"
            style="padding: 20px 0px 0px 20px; height: 65px; position: fixed; z-index: 100; background-color: #ffffff">
            <view :data-key="t_index" @tap="handleChangeScroll" class="cu-item"
                style="height: 60rpx; line-height: 60rpx" v-for="(item, t_index) in type_list" :key="t_index">
                <text :class="current_scroll == t_index ? '_this_index5' : '_no_index5'">{{ item.name }}</text>

                <view v-if="current_scroll == t_index"
                    style="width: 50%; height: 2px; background-color: #f37b1d; margin-top: 18px; border-radius: 10px; margin: 0 auto">
                </view>
            </view>
        </scroll-view>
        <view class="bg-white" style="padding-bottom: 40px; margin-top: 70px">
            <view class="grid col-2 margin-bottom">
                <block v-for="(item, index) in shop_list" :key="index">
                    <view @tap="get_url" :data-id="item.id"
                        style="position: relative; border: 1px solid rgb(250, 250, 250)">
                        <view style="text-align: center">
                            <image class="now_level" :src="item.product_img[0]" mode="widthFix"
                                style="width: 100%; padding: 5px"></image>
                        </view>
                        <view class="text_num" style="font-size: 15px; margin: 10px 10px; font-weight: 400">{{
                            item.product_name }}</view>
                        <view class="text_num"
                            style="font-size: 12px; margin: 10px 10px; font-weight: 300; color: #9b9da8">{{
                            item.product_synopsis }}</view>
                        <view style="margin: 10px 10px; font-weight: 300; color: #000000">
                            <image v-if="item.pay_type == 0 || item.pay_type == 1" class="now_level" mode="widthFix"
                                style="width: 30rpx; vertical-align: middle; margin-right: 10rpx"
                                :src="$state.diy.currency_icon"></image>
                            <view style="font-size: 14px; display: inline-block; vertical-align: middle">
                                <text :class="item.pay_type == 2 ? 'text-price' : ''">{{ item.product_price }}</text>
                                <text v-if="item.pay_type == 0 || item.pay_type == 1"
                                    style="vertical-align: bottom; font-size: 20rpx">
                                    ({{ item.pay_type == 0 ? $state.diy.currency : $state.diy.confer }})
                                </text>
                                <text v-if="item.noble_exclusive == 1"
                                    style="margin-left: 5px; font-size: 11px; color: #9b9da8">[会员专属]</text>
                            </view>
                            <view v-if="item.open_discount == 1"
                                style="margin-top: 10px; color: #9b9da8; font-size: 11px">
                                <text>会员价</text>
                                <text style="margin-left: 5px">{{ toFix(item.product_price *
                                    item.noble_discount) }}</text>
                            </view>
                        </view>
                    </view>
                </block>
            </view>
            <view :class="'cu-load ' + (!di_msg ? 'loading' : 'over')"></view>
        </view>
    </view>
</template>

<script>
export default {
    props: ['data', 'compName'],
    computed: {
        $state() {
            return this.$parent.$data.$state;
        },
        title() {
            return this.$parent.$data.title;
        },
        type_list() {
            return this.$parent.$data.type_list;
        },
        current_scroll() {
            return this.$parent.$data.current_scroll;
        },
        shop_list() {
            return this.$parent.$data.shop_list;
        },
        di_msg() {
            return this.$parent.$data.di_msg;
        }

    },
    methods: {
        toFix(value) {
            var na = (parseInt(value * 100) / 100).toFixed(2);
            return na;
        },
        handleChangeScroll(e) {
            this.$emit('handleChangeScroll', e);
        },
        get_url(e) {
            this.$emit('get_url', e);
        },
    }
};
</script>
<style></style>
