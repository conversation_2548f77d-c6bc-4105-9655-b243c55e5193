<template>
    <view class="page-container">
        <cu-custom bgColor="none" :isSearch="false" :isBack="true">
            <view slot="backText">返回</view>
            <view slot="content" style="font-weight: 600; font-size: 36rpx">禁言申诉</view>
        </cu-custom>

        <view class="content-container">
            <view class="ban-card" v-for="(item, i_index) in info" :key="i_index">
                <view class="card-content">
                    <view class="ban-header">
                        <text class="ban-icon">🚫</text>
                        <text class="ban-title">您已被禁言，理由如下</text>
                    </view>
                    <view class="ban-reason">{{ item.beget }}</view>

                    <view class="status-container">
                        <view v-if="item.refer_time != 0" class="status-item status-active">
                            <text class="status-icon">⏰</text>
                            <text class="status-text">禁言解除时间：{{ item.refer_time }}</text>
                        </view>
                        <view v-if="item.refer_time == 0" class="status-item status-resolved">
                            <text class="status-icon">✅</text>
                            <text class="status-text">禁言已解除</text>
                        </view>
                    </view>

                    <view class="action-container">
                        <text class="realm-name">{{ item.realm_name }}</text>
                        <text class="divider">　|　</text>
                        <view
                            :data-id="i_index"
                            @tap="user_mutter"
                            v-if="item.user_is_mutter == 0 && item.refer_time != 0"
                            class="appeal-btn appeal-btn-active"
                        >
                            <text class="appeal-icon">📝</text>
                            <text>申诉</text>
                        </view>
                        <view v-if="item.user_is_mutter == 1 && item.refer_time != 0" class="appeal-btn appeal-btn-pending">
                            <text class="appeal-icon">⏳</text>
                            <text>已申诉</text>
                        </view>
                    </view>

                    <block v-for="(m, index) in item.user_mutter_list" :key="index">
                        <view class="appeal-info">
                            <view class="appeal-reason">
                                <text class="appeal-label">📝 申诉理由：</text>
                                <text class="appeal-content">{{ m.beget }}</text>
                            </view>
                            <view v-if="m.mute_type == 2" class="review-reply">
                                <text class="review-label">💬 审核回复：</text>
                                <text class="review-content">{{ m.reason_refusal }}</text>
                            </view>
                        </view>
                    </block>
                </view>
            </view>
        </view>

        <view :class="'cu-load ' + (info.length == 0 ? 'over' : '')"></view>

        <view :class="'cu-modal ' + (sc_msg ? 'show' : '')">
            <view class="cu-dialog appeal-modal">
                <view class="modal-header">
                    <view class="modal-title">
                        <text class="modal-icon">📝</text>
                        <text class="modal-title-text">申诉理由</text>
                    </view>
                    <view class="modal-close" @tap="hideModal">
                        <text class="cuIcon-close"></text>
                    </view>
                </view>
                <view class="modal-content">
                    <view class="textarea-container">
                        <textarea
                            @input="is_sc_text"
                            class="appeal-textarea"
                            placeholder="请详细说明您的申诉理由，我们会认真审核..."
                            maxlength="500"
                        />
                        <view class="textarea-tip">💡 请详细描述您认为禁言不当的原因</view>
                    </view>
                </view>
                <view class="modal-footer">
                    <button class="modal-btn cancel-btn" @tap="hideModal">
                        <text class="btn-icon">❌</text>
                        <text>取消</text>
                    </button>
                    <button class="modal-btn confirm-btn" @tap="do_user_mutter">
                        <text class="btn-icon">✅</text>
                        <text>提交申诉</text>
                    </button>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
var app = getApp();
var http = require('../../../util/http.js');
export default {
    data() {
        return {
            info: [],
            page: 1,
            sc_text: '',
            sc_msg: false,
            id: '',
            i_index: 0,

            m: {
                beget: '',
                mute_type: 0,
                reason_refusal: ''
            }
        };
    }
    /**
     * 生命周期函数--监听页面加载
     */,
    onLoad(options) {
        //this.get_help_info();
    },
    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {
        this.get_user_banned();
        //this.get_user_amount();
    },
    /**
     * 用户点击右上角分享
     */
    onShareAppMessage() {
        var forward = app.globalData.forward;
        console.log(forward);
        if (forward) {
            return {
                title: forward.title,
                path: '/yl_welore/pages/index/index',
                imageUrl: forward.reis_img
            };
        } else {
            return {
                title: '您的好友给您发了一条信息',
                path: '/yl_welore/pages/index/index'
            };
        }
    },
    methods: {
        /**
         * 获取禁言
         */
        get_user_banned() {
            var b = app.globalData.api_root + 'User/get_user_banned';
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.uid = e.uid;
            http.POST(b, {
                params: params,
                success: function (res) {
                    console.log(res);
                    if (res.data.status == 'success') {
                        that.info = res.data.info;
                    } else {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                },
                fail: function () {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        },

        /**
         * 申诉
         */
        user_mutter(e) {
            this.sc_msg = true;
            this.id = e.currentTarget.dataset.id;
        },

        /**
         *
         */
        is_sc_text(e) {
            this.sc_text = e.detail.value;
        },

        /**
         * 隐藏窗口
         */
        hideModal() {
            this.sc_msg = false;
        },

        /**
         * 申诉
         */
        do_user_mutter() {
            var b = app.globalData.api_root + 'User/do_user_mutter';
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.uid = e.uid;
            params.id = this.info[this.id]['id'];
            params.tory_id = this.info[this.id]['tory_id'];
            params.beget = this.sc_text;
            if(this.sc_text==''){
                uni.showToast({
                    title: '请填写申诉理由！',
                    icon: 'none',
                    duration: 2000
                });
                return;
            }
            http.POST(b, {
                params: params,
                success: function (res) {
                    console.log(res);
                    if (res.data.status == 'success') {
                        that.sc_msg = false;
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                        that.get_user_banned();
                    } else {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                },
                fail: function () {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        }
    }
};
</script>
<style>
.page-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.content-container {
    padding: 30rpx 20rpx;
}

.ban-card {
    background: #ffffff;
    border-radius: 20rpx;
    margin-bottom: 30rpx;
    box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: all 0.3s ease;
}

.ban-card:hover {
    transform: translateY(-5rpx);
    box-shadow: 0 15rpx 40rpx rgba(0, 0, 0, 0.15);
}

.card-content {
    padding: 30rpx;
}

.ban-header {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;
    padding-bottom: 15rpx;
    border-bottom: 2rpx solid #f0f0f0;
}

.ban-icon {
    font-size: 40rpx;
    margin-right: 15rpx;
}

.ban-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
}

.ban-reason {
    background: #fff5f5;
    border-left: 6rpx solid #ff6b6b;
    padding: 20rpx;
    margin: 20rpx 0;
    border-radius: 10rpx;
    color: #666;
    font-size: 28rpx;
    line-height: 1.6;
}

.status-container {
    margin: 25rpx 0;
}

.status-item {
    display: flex;
    align-items: center;
    padding: 15rpx 20rpx;
    border-radius: 50rpx;
    margin-bottom: 10rpx;
}

.status-active {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
}

.status-resolved {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.status-icon {
    font-size: 30rpx;
    margin-right: 10rpx;
}

.status-text {
    font-size: 28rpx;
    font-weight: 500;
    color: #333;
}

.action-container {
    display: flex;
    align-items: center;
    margin: 25rpx 0;
    padding: 20rpx;
    background: #f8f9fa;
    border-radius: 15rpx;
}

.realm-name {
    font-size: 28rpx;
    color: #666;
    font-weight: 500;
}

.divider {
    color: #ccc;
    margin: 0 10rpx;
}

.appeal-btn {
    display: flex;
    align-items: center;
    padding: 10rpx 20rpx;
    border-radius: 25rpx;
    font-size: 26rpx;
    font-weight: 500;
    transition: all 0.3s ease;
}

.appeal-btn-active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #ffffff;
    cursor: pointer;
}

.appeal-btn-active:active {
    transform: scale(0.95);
}

.appeal-btn-pending {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    color: #d68910;
}

.appeal-icon {
    font-size: 24rpx;
    margin-right: 8rpx;
}

.appeal-info {
    margin-top: 25rpx;
    padding: 20rpx;
    background: #f8f9fa;
    border-radius: 15rpx;
    border-left: 6rpx solid #667eea;
}

.appeal-reason, .review-reply {
    margin-bottom: 15rpx;
}

.appeal-label, .review-label {
    font-size: 26rpx;
    font-weight: 600;
    color: #333;
    display: block;
    margin-bottom: 8rpx;
}

.appeal-content, .review-content {
    font-size: 26rpx;
    color: #666;
    line-height: 1.6;
    word-break: break-all;
}

/* 申诉弹窗样式 */
.appeal-modal {
    border-radius: 25rpx !important;
    overflow: hidden;
    box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
}

.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 30rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-title {
    display: flex;
    align-items: center;
}

.modal-icon {
    font-size: 40rpx;
    margin-right: 15rpx;
}

.modal-title-text {
    font-size: 32rpx;
    font-weight: 600;
    color: #ffffff;
}

.modal-close {
    padding: 10rpx;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    color: #ffffff;
    font-size: 30rpx;
}

.modal-content {
    padding: 30rpx;
    background: #ffffff;
}

.textarea-container {
    position: relative;
}

.appeal-textarea {
    width: 100%;
    min-height: 200rpx;
    padding: 20rpx;
    border: 2rpx solid #e0e0e0;
    border-radius: 15rpx;
    font-size: 28rpx;
    line-height: 1.6;
    background: #fafafa;
    transition: all 0.3s ease;
}

.appeal-textarea:focus {
    border-color: #667eea;
    background: #ffffff;
    box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
}

.textarea-tip {
    margin-top: 15rpx;
    font-size: 24rpx;
    color: #999;
    display: flex;
    align-items: center;
}

.modal-footer {
    padding: 20rpx 30rpx 30rpx;
    background: #ffffff;
    display: flex;
    justify-content: space-between;
    gap: 20rpx;
}

.modal-btn {
    flex: 1;
    height: 80rpx;
    border-radius: 40rpx;
    font-size: 28rpx;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    transition: all 0.3s ease;
}

.cancel-btn {
    background: #f5f5f5;
    color: #666;
}

.cancel-btn:active {
    background: #e0e0e0;
    transform: scale(0.95);
}

.confirm-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #ffffff;
}

.confirm-btn:active {
    transform: scale(0.95);
    box-shadow: 0 5rpx 15rpx rgba(102, 126, 234, 0.4);
}

.btn-icon {
    font-size: 24rpx;
    margin-right: 8rpx;
}

/* 加载状态优化 */
.cu-load.over::before {
    content: "🎉 暂无更多内容";
    color: #999;
    font-size: 28rpx;
}
</style>
