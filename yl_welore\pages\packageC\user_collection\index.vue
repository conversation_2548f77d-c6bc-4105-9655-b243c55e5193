<template>
    <view>
        <cu-custom bgColor="bg-white" :isSearch="false" :isBack="true">
            <view slot="backText">返回</view>
            <view slot="content" style="color: #2c2b2b; font-weight: 600; font-size: 36rpx">我的收藏</view>
        </cu-custom>

        <Index3 :data="pageData"></Index3>
        <!-- <view wx:if="{{di_msg}}" class="classify" style='left:34%;'>
  <view class='class_border' style='border:0rpx;'>
    <image src='../../../style/icon/zanwu.png' class='class_img'></image>
  </view>
  <view style='font-size:13px;margin-top:5px;color:#000;'>没有任何信息</view>
</view> -->
    </view>
</template>

<script>
import Index3 from '../../index/index3.vue';
var app = getApp();
import http from '../../../util/http.js';
const innerAudioContext = uni.getBackgroundAudioManager();
export default {
    components: {
        Index3
    },
    data() {
        return {
            http_root: app.globalData.http_root,
            show: true,
            delBtnWidth: 180,
            new_list: [],
            page: 1,
            di_msg: false,
            mod: [],
            current: 'tab1',
            images: [],
            height: '',
            uid: '',
            new_list_index: '',
            starttime: '',
            offset: 0,
            pageData: []
        };
    },
    onLoad: function (options) {
        var e = app.globalData.getCache('userinfo');
        this.height = app.globalData.height;
        this.uid = e.uid;
        this.page = 1;
        this.get_user_collection();
        //this.get_diy();
        var dd = uni.getStorageSync('is_diy');
        if (dd) {
            this.mod = dd.mod;
            app.globalData.editTabbar();
        } else {
            this.get_diy();
        }
    },
    onShow: function () {},
    /**
     * 加载下一页
     */
    onReachBottom: function () {
        this.page = this.page + 1;
        this.get_user_collection();
    },
    /**
     * 用户点击右上角分享
     */
    onShareAppMessage: function (d) {
        var forward = app.globalData.forward;
        var key = d.target.dataset.key;
        var info = this.new_list[key];
        if (forward) {
            return {
                title: forward.title,
                path: '/yl_welore/pages/packageA/article/index?id=' + info.id + '&type=' + info.info_type,
                imageUrl: forward.reis_img
            };
        } else {
            let img;
            if (info.image_part) {
                if (info.image_part.length > 0) {
                    img = info.image_part[0];
                }
            }
            return {
                title: info.study_title == '' ? info.study_content : info.study_title,
                path: '/yl_welore/pages/packageA/article/index?id=' + info.id + '&type=' + info.info_type,
                imageUrl: img
            };
        }
    },
    methods: {
        /**
         * 首页跳转链接
         */
        home_url(dd) {
            var key = dd.currentTarget.dataset.k; //跳转类型
            var e = app.globalData.getCache('userinfo');
            if (key == 1) {
                //头像跳转
                if (dd.currentTarget.dataset.user_id == 0) {
                    uni.showToast({
                        title: '身份已隐藏',
                        icon: 'none',
                        duration: 2000
                    });
                    return;
                }
                uni.navigateTo({
                    url: '/yl_welore/pages/packageB/my_home/index?id=' + dd.currentTarget.dataset.user_id
                });
                return;
            }
            if (key == 2) {
                //圈子跳转
                uni.navigateTo({
                    url: '/yl_welore/pages/packageA/circle_info/index?id=' + dd.currentTarget.dataset.id
                });
                return;
            }
            if (key == 3) {
                //内容跳转
                var douyin = app.globalData.__PlugUnitScreen('5fb4baf1f25fe251685b526dc8c30b8f');
                var info = this.new_list[dd.currentTarget.dataset.index];
                if (dd.currentTarget.dataset.type == 2 && info.is_buy == 0 && e.user_phone && douyin) {
                    uni.navigateTo({
                        url: '/yl_welore/pages/packageF/full_video/index?id=' + dd.currentTarget.dataset.id
                    });
                    return;
                }
                uni.navigateTo({
                    url: '/yl_welore/pages/packageA/article/index?id=' + dd.currentTarget.dataset.id + '&type=' + dd.currentTarget.dataset.type
                });
                return;
            }
        },

        /**
         * 点击话题
         */
        gambit_list(d) {
            var id = d.currentTarget.dataset.id;
            uni.navigateTo({
                url: '/yl_welore/pages/gambit/index?id=' + id
            });
        },

        get_diy() {
            var b = app.globalData.api_root + 'User/get_diy';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.uid = e.uid;
            params.openid = e.openid;
            params.uid = e.uid;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    this.mod = res.data.mod;
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        },

        /**
         * 获取收藏
         */
        get_user_collection() {
            var b = app.globalData.api_root + 'User/get_user_collection';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.uid = e.uid;
            params.page = this.page;
            var allMsg = this.new_list;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.status == 'success') {
                        if (res.data.info.length == 0) {
                            this.di_msg = true;
                        }
                        for (var i = 0; i < res.data.info.length; i++) {
                            allMsg.push(res.data.info[i]);
                        }
                        this.new_list = allMsg;
                    } else {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        },

        //点击删除按钮事件
        delItem(e) {
            //获取列表中要删除项的下标
            var index = e.currentTarget.dataset.index;
            var new_list = this.new_list;
            //移除列表中下标为index的项
            new_list.splice(index, 1);
            //更新列表的状态
            this.new_list = new_list;
        },

        //播放声音
        play(e) {
            var index = e.currentTarget.dataset.key;
            var nuw = this.new_list;
            var key = 1;
            uni.getBackgroundAudioPlayerState({
                success(res) {
                    console.log(res);
                    const status = res.status;
                    key = res.status;
                }
            });
            for (var i = 0; i < nuw.length; i++) {
                nuw[i]['is_voice'] = false;
            }
            this.new_list = nuw;
            console.log('播放');
            innerAudioContext.src = e.currentTarget.dataset.vo;
            innerAudioContext.title = nuw[index]['study_title'] ? nuw[index]['study_title'] : '暂无标题';
            innerAudioContext.onTimeUpdate(() => {
                //console.log(innerAudioContext.currentTime)
                var duration = innerAudioContext.duration;
                var offset = innerAudioContext.currentTime;
                var currentTime = parseInt(innerAudioContext.currentTime);
                var min = '0' + parseInt(currentTime / 60);
                var sec = currentTime % 60;
                if (sec < 10) {
                    sec = '0' + sec;
                }
                var starttime = min + ':' + sec; /*  00:00  */

                nuw[index]['starttime'] = starttime;
                nuw[index]['offset'] = offset;
                this.new_list = nuw;
            });
            // innerAudioContext.play();

            nuw[index]['is_voice'] = true;
            this.new_list = nuw;
            this.new_list_index = index;
            //播放结束
            innerAudioContext.onEnded(() => {
                var nuw = this.new_list;
                nuw[index]['is_voice'] = false;
                this.starttime = '00:00';
                this.offset = 0;
                this.new_list = nuw;
                console.log('音乐播放结束');
            });
            innerAudioContext.play();
        },

        /**
         * 停止
         */
        stop(e) {
            innerAudioContext.pause();
            console.log('暂停');
            var index = e.currentTarget.dataset.key;
            var nuw = this.new_list;
            nuw[index]['is_voice'] = false;
            this.new_list = nuw;
        },

        // 进度条拖拽
        sliderChange(e) {
            var index = e.currentTarget.dataset.key;
            var offset = parseInt(e.detail.value);
            innerAudioContext.play();
            innerAudioContext.seek(offset);
            var nuw = this.new_list;
            nuw[index]['is_voice'] = true;
            this.new_list = nuw;
        }
    }
};
</script>
<style>
button::after {
    line-height: normal;
    font-size: 30rpx;
    width: 0;
    height: 0;
    top: 0;
    left: 0;
}

button {
    line-height: normal;
    display: block;
    padding-left: 0px;
    padding-right: 0px;
    background-color: rgba(255, 255, 255, 0);
    font-size: 30rpx;
    overflow: inherit;
}

/**index.wxss**/
.audiosBox {
    width: 92%;
    margin: auto;
    height: 130rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f6f7f7;
    border-radius: 10rpx;
}
/*按钮大小  */
.audioOpen {
  width: 50rpx;
  height: 50rpx;
  border: 1px solid #ffffff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  margin-top: 57rpx;
}

.image2 {
    margin-left: 10%;
}
/*进度条长度  */
.slid {
    flex: 1;
    position: relative;
}
.slid view {
    display: flex;
    justify-content: space-between;
}
.slid view > text:nth-child(1) {
    color: #4c9dee;
    margin-left: 6rpx;
}
.slid view > text:nth-child(2) {
    margin-right: 6rpx;
}
slider {
    width: 520rpx;
    margin: 0;
    margin-left: 35rpx;
}
/*横向布局  */
.times {
    width: 100rpx;
    text-align: center;
    display: inline-block;
    font-size: 24rpx;
    color: #999999;
    margin-top: 5rpx;
}
.title view {
    text-indent: 2em;
}
</style>
