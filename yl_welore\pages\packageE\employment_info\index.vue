<template>
    <view class="page-container">
        <cu-custom bgColor="none" :isBack="true">
            <view slot="backText">返回</view>
            <view slot="content" class="header-title">
                <text class="title-emoji">💼</text>
                <text class="title-text">职位详情</text>
            </view>
        </cu-custom>
        <block v-if="scene != 1154">
            <view class="content-wrapper" v-if="info">
                <!-- 主要信息卡片 -->
                <view class="info-card">
                    <view class="job-header">
                        <view class="job-title-section">
                            <view class="job-title-container">
                                <image v-if="info.top_time != 0" class="top-badge"
                                    :src="http_root + 'addons/yl_welore/web/static/applet_icon/top_time.png'"></image>
                                <text class="job-emoji">💼</text>
                                <text class="job-title">{{ info.job_name }}</text>
                            </view>
                            <view class="salary-container">
                                <text class="salary-text text-price">{{ info.job_salary }}</text>
                            </view>
                        </view>
                        
                        <view class="meta-info">
                            <view class="publish-time">
                                <text class="time-emoji">📅</text>
                                <text class="time-text">{{ info.create_time }}</text>
                            </view>
                            <view class="collect-section" @tap="in_collect">
                                <text v-if="info.is_c == 0" class="star-icon">⭐</text>
                                <text v-if="info.is_c == 1" class="star-icon active">⭐</text>
                                <text class="collect-text" :class="info.is_c == 1 ? 'active' : ''">
                                    {{ info.is_c == 0 ? '收藏' : '已收藏' }}
                                </text>
                            </view>
                        </view>
                    </view>
                </view>

                <!-- 岗位详情卡片 -->
                <view class="detail-card">
                    <view class="section-header">
                        <text class="section-emoji">📋</text>
                        <text class="section-title">岗位详情</text>
                    </view>
                    
                    <view class="detail-item">
                        <text class="detail-label">🏷️ 岗位类型：</text>
                        <view class="tag-container">
                            <view v-if="info.release_type == 0" class="job-tag recruit">🔥 招聘</view>
                            <view v-if="info.release_type == 1" class="job-tag job-seeking">✨ 求职</view>
                            <view v-if="info.release_type == 2" class="job-tag part-time">🌟 兼职</view>
                            <view v-if="info.release_type == 3" class="job-tag partner">⭐ 合伙人</view>
                        </view>
                    </view>
                    
                    <view class="detail-item">
                        <text class="detail-label">📍 工作地点：</text>
                        <text class="detail-value">{{ info.work_address }}</text>
                    </view>
                </view>

                <!-- 描述卡片 -->
                <view class="description-card">
                    <view class="section-header">
                        <text class="section-emoji">📝</text>
                        <text class="section-title">{{ info.release_type == 1 ? '自我介绍' : '职位描述' }}</text>
                    </view>
                    <view class="description-content">
                        <text class="description-text">{{ info.job_description == '' ? '暂无职位描述' : info.job_description }}</text>
                    </view>
                </view>

                <!-- 图片展示卡片 -->
                <view class="images-card">
                    <view class="section-header">
                        <text class="section-emoji">📷</text>
                        <text class="section-title">{{ info.release_type == 1 ? '作品/证书' : '公司环境' }}</text>
                    </view>
                    <view class="images-grid">
                        <view class="image-item" v-for="(item, index) in info.image_part" :key="index">
                            <image :src="item" class="job-image" @tap="ViewImage" :data-url="item" mode="aspectFill"></image>
                        </view>
                    </view>
                </view>

                <!-- 置顶设置卡片 -->
                <view v-if="config.top_twig == 1 && info.user_id == user.id" class="top-setting-card">
                    <view class="top-info">
                        <view class="top-label">
                            <text class="top-emoji">📌</text>
                            <text class="top-text">置顶设置</text>
                            <text class="top-price">({{ config.top_price }}{{ config.price_type == 0 ? config.design.currency : config.design.confer }}/天)</text>
                        </view>
                        <view class="top-picker">
                            <picker :value="index" :range="ToTop" @change="bindTopChange">
                                <view class="picker-container">
                                    <text class="picker-text">{{ info.top_time == 0 ? ToTop[TopIndex] : '已置顶' }}</text>
                                    <text class="picker-arrow">▼</text>
                                </view>
                            </picker>
                        </view>
                    </view>
                    <view v-if="config.top_twig == 1 && info.top_time != 0 && info.user_id == user.id" class="top-expire">
                        <text class="expire-text">⏰ 置顶到期：{{ info.top_time }}</text>
                    </view>
                </view>
            </view>
            
            <!-- 底部联系按钮 -->
            <view class="contact-button-container">
                <button @tap="openBtn" class="contact-button">
                    <text class="contact-emoji">📞</text>
                    <text class="contact-text">联系方式</text>
                </button>
            </view>
        </block>
    </view>
</template>

<script>
const app = getApp();
var http = require('../../../util/http.js');
var md5 = require('../../../util/md5.js');
import regeneratorRuntime from '../../../util/runtime';

export default {
    /**
     * 页面的初始数据
     */
    data() {
        return {
            http_root: app.globalData.http_root,
            user: {},
            reply: [],
            id: 0,
            page: 1,
            is_secrecy: false,
            img_arr: [],
            huifu: false,
            emj_list: [],
            emoji: false,
            text: '',
            config: {},
            TopIndex: 0,
            ToTop: ['未设置', '1天', '2天', '3天', '4天', '5天', '6天', '7天', '8天', '9天', '10天'],
            scene: 0,
            info: null,
            img_botton: false
        }
    },
    // 生命周期方法
    onShow() { },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        console.log(options);
        var op = uni.getLaunchOptionsSync();
        this.scene = op.scene;
        if (op.scene == 1154) {
            uni.showToast({
                title: '前往小程序查看！',
                icon: 'none',
                duration: 666000
            });
            return;
        }
        var lost = app.globalData.__PlugUnitScreen('7d6353619854f00edf2d1e9a44430333');
        if (!lost) {
            uni.showToast({
                title: '未开通插件',
                icon: 'none',
                duration: 2000
            });
            setTimeout(() => {
                var pages = getCurrentPages();
                if (pages.length == 1) {
                    uni.reLaunch({
                        url: '/yl_welore/pages/index/index'
                    });
                    return;
                }
                uni.navigateBack();
            }, 1000);
            return;
        }
        this.id = options.id;
        this.doIt();
        // var e = app.getCache("userinfo");
    },

    onReachBottom() { },

    /**
     * 用户点击右上角分享
     */
    onShareTimeline() {
        var info = this.info;
        var msg = '';
        switch (info.release_type) {
            case 0:
                msg = '[招聘]';
                break;
            case 1:
                msg = '[求职]';
                break;
            case 2:
                msg = '[兼职]';
                break;
            case 3:
                msg = '[合伙人]';
                break;
        }
        var img = info.image_part[0];
        return {
            title: msg + info.job_name,
            path: '/yl_welore/pages/packageE/employment_info/index?id=' + this.id,
            imageUrl: img
        };
    },

    /**
     * 用户点击右上角分享
     */
    onShareAppMessage() {
        var info = this.info;
        var msg = '';
        switch (info.release_type) {
            case 0:
                msg = '[招聘]';
                break;
            case 1:
                msg = '[求职]';
                break;
            case 2:
                msg = '[兼职]';
                break;
            case 3:
                msg = '[合伙人]';
                break;
        }
        var img = info.image_part[0];
        return {
            title: msg + info.job_name,
            path: '/yl_welore/pages/packageE/employment_info/index?id=' + this.id,
            imageUrl: img
        };
    },

    methods: {
        in_collect() {
            var that = this;
            uni.showModal({
                title: '提示',
                content: '确定要收藏吗？',
                success(res) {
                    if (res.confirm) {
                        that.in_collect_do();
                    }
                }
            });
        },
        in_collect_do() {
            var that = this;
            var b = app.globalData.api_root + 'Employment/CollectDo';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.id = this.id;
            http.POST(b, {
                params: params,
                success(res) {
                    uni.showToast({
                        title: res.data.msg,
                        icon: 'none',
                        duration: 2000
                    });
                    var info = that.info;
                    info['is_c'] = info['is_c'] == 1 ? 0 : 1;
                    that.info = info;
                },
                fail() {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success(res) { }
                    });
                }
            });
        },
        bindTopChange(d) {
            this.TopIndex = d.detail.value;
            console.log('确定');
            if (d.detail.value == 0) {
                return;
            }
            var that = this;
            if (this.config.price_type == 0 || this.config.price_type == 1) {
                uni.showModal({
                    title: '提示',
                    content: '确定要置顶' + d.detail.value + '天吗？',
                    success(res) {
                        if (res.confirm) {
                            that.pay_submit(that.id, d.detail.value);
                        }
                    }
                });
            }
            if (this.config.price_type == 2) {
                this.pay_submit_wx(this.id, d.detail.value);
            }
        },
        pay_submit(id, top_day) {
            var b = app.globalData.api_root + 'Employment/PaySubmit';
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.top_day = top_day;
            params.id = id;
            http.POST(b, {
                params: params,
                success(res) {
                    console.log(res);
                    if (res.data.code == 1) {
                        uni.showModal({
                            title: '提示',
                            content: res.data.msg,
                            showCancel: false,
                            success(res) { }
                        });
                    } else {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                    that.getInfo();
                },
                fail() {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success(res) { }
                    });
                }
            });
        },
        /**
         * 充值
         */
        pay_submit_wx(id, top_day) {
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.lost_id = id;
            params.top_day = top_day;
            params.uid = e.id;
            var b = app.globalData.api_root + 'Pay/do_employment_pay';
            http.POST(b, {
                params: params,
                success(res) {
                    console.log(res);
                    if (res.data.return_msg == 'OK') {
                        var timeStamp = (Date.parse(new Date()) / 1000).toString();
                        var pkg = 'prepay_id=' + res.data.prepay_id;
                        var nonceStr = res.data.nonce_str;
                        var paySign = md5
                            .hexMD5(
                                'appId=' +
                                res.data.appid +
                                '&nonceStr=' +
                                nonceStr +
                                '&package=' +
                                pkg +
                                '&signType=MD5&timeStamp=' +
                                timeStamp +
                                '&key=' +
                                res.data.app_info['app_key']
                            )
                            .toUpperCase(); //此处用到hexMD5插件
                        //发起支付
                        uni.requestPayment({
                            timeStamp: timeStamp,
                            nonceStr: nonceStr,
                            package: pkg,
                            signType: 'MD5',
                            paySign: paySign,
                            success(res) {
                                uni.showModal({
                                    title: '提示',
                                    content: '支付成功！',
                                    showCancel: false,
                                    success(res) { }
                                });
                                that.getInfo();
                            },
                            complete() {
                                uni.hideLoading();
                            }
                        });
                    } else {
                        uni.showModal({
                            title: '提示',
                            content: '支付参数错误！',
                            showCancel: false,
                            success(res) { }
                        });
                        uni.hideLoading();
                    }
                    that.TopIndex = 0;
                },
                fail() {
                    uni.hideLoading();
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success(res) { }
                    });
                }
            });
        },
        getLostConfig() {
            var b = app.globalData.api_root + 'Employment/getLostConfig';
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            http.POST(b, {
                params: params,
                success(res) {
                    console.log(res);
                    that.config = res.data;
                },
                fail() {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success(res) { }
                    });
                }
            });
        },
        SetSecrecy() {
            this.is_secrecy = !this.is_secrecy;
        },
        openHuiFu() {
            this.huifu = true;
        },
        hideMode() {
            this.huifu = false;
        },
        openUrl(d) {
            var uid = d.currentTarget.dataset.uid;
            uni.navigateTo({
                url: '/yl_welore/pages/packageB/my_home/index?id=' + uid
            });
        },
        openBtn() {
            var that = this;
            uni.showModal({
                title: '联系方式',
                confirmText: '复制',
                content: that.info.contact_details,
                success(res) {
                    if (res.confirm) {
                        uni.setClipboardData({
                            data: that.info.contact_details,
                            success(res) { }
                        });
                    } else if (res.cancel) {
                        console.log('用户点击取消');
                    }
                }
            });
        },
        doIt() {
            app.globalData.getLogin(
                // 成功回调 returnA 
                (userInfo) => {
                    console.log(' 登录成功:', userInfo);
                    this.getLostConfig();
                    this.getInfo();
                    this.user = userInfo;
                },
                // 失败回调 returnB 
                (err) => {
                    console.error(' 登录失败:', err);
                }
            );
        },

        claerStor() {
            return new Promise((resolve, reject) => {
                var v = app.globalData.getCache('yl_version');
                console.log(v);
                if (v == '') {
                    var v2 = -1;
                } else {
                    var v2 = app.globalData.compareVersion(v, app.globalData.version); //相等=0  小于-1  大于1
                }

                app.globalData.setCache('yl_version', app.globalData.version);
                console.log(v2);
                resolve(v2);
                return v2; //执行登陆
            });
        },

        getInfo() {
            var b = app.globalData.api_root + 'Employment/LostInfo';
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.id = this.id;
            http.POST(b, {
                params: params,
                success(res) {
                    if (res.data.code == 0) {
                        that.info = res.data.info;
                    } else {
                        uni.showModal({
                            title: '提示',
                            content: res.data.msg,
                            showCancel: false,
                            success(res) {
                                uni.navigateBack({
                                    delta: 1
                                });
                            }
                        });
                    }
                },
                fail() {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success(res) { }
                    });
                }
            });
        },
        /**
         * 一键复制
         */
        copyBtn(e) {
            var that = this;
            uni.setClipboardData({
                data: e.currentTarget.dataset.no,
                success(res) { }
            });
        },
        ViewImage(e) {
            console.log(e);
            if (this.info['image_part'].length > 0) {
                uni.previewImage({
                    urls: this.info['image_part'],
                    current: e.currentTarget.dataset.src
                });
            } else {
                uni.previewImage({
                    urls: [e.currentTarget.dataset.src],
                    current: e.currentTarget.dataset.src
                });
            }
        },
        /**
         * 删除图片
         */
        clearOneImage(e) {
            var that = this;
            var index = e.target.dataset['index'];
            var notes = that.img_arr;
            notes.splice(index, 1);
            that.img_arr = notes;
            that.img_botton = true;
        }
    }
};
</script>

<style>
/* 页面整体样式 */
.page-container {
    min-height: 100vh;
    background: linear-gradient(180deg, #e6f3ff 0%, #f0f9ff 50%, #ffffff 100%);
}

/* 头部标题样式 */
.header-title {
    display: flex;
    align-items: center;
    justify-content: center;
}

.title-emoji {
    font-size: 40rpx;
    margin-right: 16rpx;
}

.title-text {
    font-size: 36rpx;
    font-weight: 600;
    color: #2c3e50;
}

/* 内容包装器 */
.content-wrapper {
    padding: 20rpx;
    padding-bottom: 160rpx;
}

/* 卡片基础样式 */
.info-card, .detail-card, .description-card, .images-card, .top-setting-card {
    background: #ffffff;
    border-radius: 24rpx;
    margin-bottom: 24rpx;
    padding: 32rpx;
    box-shadow: 0 8rpx 32rpx rgba(0, 129, 255, 0.1);
    border: 1px solid rgba(0, 129, 255, 0.05);
}

/* 职位头部信息 */
.job-header {
    margin-bottom: 24rpx;
}

.job-title-section {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24rpx;
}

.job-title-container {
    display: flex;
    align-items: center;
    flex: 1;
    position: relative;
}

.top-badge {
    width: 40rpx;
    height: 40rpx;
    margin-right: 16rpx;
}

.job-emoji {
    font-size: 44rpx;
    margin-right: 16rpx;
}

.job-title {
    font-size: 40rpx;
    font-weight: 700;
    color: #2c3e50;
    line-height: 1.3;
    flex: 1;
}

.salary-container {
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
    padding: 16rpx 24rpx;
    border-radius: 20rpx;
    box-shadow: 0 4rpx 16rpx rgba(255, 107, 107, 0.3);
}

.salary-emoji {
    font-size: 32rpx;
    margin-right: 8rpx;
}

.salary-text {
    font-size: 32rpx;
    font-weight: 700;
    color: #ffffff;
}

/* 元信息区域 */
.meta-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.publish-time {
    display: flex;
    align-items: center;
}

.time-emoji {
    font-size: 28rpx;
    margin-right: 12rpx;
}

.time-text {
    font-size: 28rpx;
    color: #7f8c8d;
}

.collect-section {
    display: flex;
    align-items: center;
    padding: 12rpx 20rpx;
    border-radius: 20rpx;
    background: rgba(0, 129, 255, 0.1);
    transition: all 0.3s ease;
}

.collect-section:active {
    transform: scale(0.95);
    background: rgba(0, 129, 255, 0.2);
}

.star-icon {
    font-size: 32rpx;
    margin-right: 8rpx;
    filter: grayscale(100%);
    transition: all 0.3s ease;
}

.star-icon.active {
    filter: grayscale(0%);
    animation: star-bounce 0.6s ease;
}

.collect-text {
    font-size: 28rpx;
    color: #0081ff;
    font-weight: 500;
}

.collect-text.active {
    color: #ff9500;
    font-weight: 600;
}

@keyframes star-bounce {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.2); }
}

/* 章节标题 */
.section-header {
    display: flex;
    align-items: center;
    margin-bottom: 24rpx;
}

.section-emoji {
    font-size: 36rpx;
    margin-right: 16rpx;
}

.section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #2c3e50;
}

/* 岗位详情卡片 */
.detail-card {
    margin-bottom: 24rpx;
}

.detail-item {
    display: flex;
    align-items: center;
    margin-bottom: 16rpx;
}

.detail-label {
    font-size: 28rpx;
    font-weight: 500;
    color: #333333;
    margin-right: 16rpx;
}

.detail-value {
    font-size: 28rpx;
    color: #555555;
}

.tag-container {
    display: flex;
    gap: 16rpx;
}

.job-tag {
    display: inline-block;
    padding: 8rpx 16rpx;
    border-radius: 16rpx;
    font-size: 24rpx;
    font-weight: 500;
    color: #ffffff;
    margin-right: 16rpx;
}

.job-tag.recruit {
    background-color: #e74c3c;
}

.job-tag.job-seeking {
    background-color: #3498db;
}

.job-tag.part-time {
    background-color: #f1c40f;
}

.job-tag.partner {
    background-color: #2ecc71;
}

/* 描述卡片 */
.description-card {
    margin-bottom: 24rpx;
}

.description-content {
    margin-top: 16rpx;
}

.description-text {
    font-size: 28rpx;
    color: #555555;
    line-height: 1.6;
}

/* 图片展示卡片 */
.images-card {
    margin-bottom: 24rpx;
}

.images-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16rpx;
}

.image-item {
    height: 200rpx;
    position: relative;
    overflow: hidden;
    border-radius: 16rpx;
}

.job-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* 置顶设置卡片 */
.top-setting-card {
    margin-bottom: 24rpx;
}

.top-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.top-label {
    display: flex;
    align-items: center;
}

.top-emoji {
    font-size: 32rpx;
    margin-right: 16rpx;
}

.top-text {
    font-size: 28rpx;
    font-weight: 500;
    color: #2c3e50;
}

.top-price {
    font-size: 28rpx;
    color: #7f8c8d;
}

.top-picker {
    display: flex;
    align-items: center;
}

.picker-container {
    display: flex;
    align-items: center;
    padding: 12rpx 20rpx;
    border-radius: 20rpx;
    background: rgba(0, 129, 255, 0.1);
    transition: all 0.3s ease;
}

.picker-container:active {
    transform: scale(0.95);
    background: rgba(0, 129, 255, 0.2);
}

.picker-text {
    font-size: 28rpx;
    color: #0081ff;
    margin-right: 16rpx;
}

.picker-arrow {
    font-size: 28rpx;
    color: #0081ff;
}

.top-expire {
    text-align: center;
    margin-top: 16rpx;
}

.expire-text {
    font-size: 28rpx;
    color: #7f8c8d;
}

/* 底部联系按钮 */
.contact-button-container {
    position: fixed;
    bottom: 4%;
    width: 90%;
    text-align: center;
    z-index: 1000;
    margin: 0 auto;
    left: 0;
    right: 0;
}

.contact-button {
    background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
    border: none;
    border-radius: 48rpx;
    padding: 10rpx 48rpx;
    font-size: 32rpx;
    font-weight: 600;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8rpx 32rpx rgba(255, 107, 107, 0.3);
    transition: all 0.3s ease;
}

.contact-button:active {
    transform: scale(0.95);
}

.contact-emoji {
    font-size: 36rpx;
    margin-right: 16rpx;
}

.contact-text {
    font-size: 32rpx;
    color: #ffffff;
}
</style>









