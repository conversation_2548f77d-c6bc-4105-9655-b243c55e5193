<template>
    <view>
        <cu-custom bgColor="bg-white" :isBack="true" :isSearch="false" :isBackAlert="true">
            <view slot="backText">返回</view>
            <view slot="content" style="color: #2c2b2b; font-weight: 600; font-size: 36rpx">核销</view>
        </cu-custom>
        <block v-if="info != ''">
            <view class="cu-item center_text" style="margin-top: 20px">
                <text v-if="info.code == 0" style="font-size: 150rpx; color: #33cc66" class="_icon-check-round"></text>
                <text v-if="info.code == 1" style="font-size: 150rpx; color: #ff3333" class="_icon-close-round"></text>
            </view>
            <view v-if="info.code == 0" class="center_text hx" style="color: #33cc66">{{ info.msg }}</view>
            <view v-if="info.code == 1" class="center_text hx" style="color: #ff3333">{{ info.msg }}</view>
            <block v-if="info != '' && item.verify != ''">
                <view class="center_text" style="margin-top: 80rpx; color: #999">验证时间</view>
                <view class="flex solid-bottom padding justify-center align-center">
                    <view class="center_text" style="font-size: 18px; font-weight: 300">{{ item.verify }}</view>
                </view>
            </block>
            <block v-if="item != ''">
                <view class="center_text" style="margin-top: 80rpx; color: #999">商家</view>
                <view class="flex solid-bottom padding justify-center align-center">
                    <view class="center_text">
                        <image :src="item.easy_info.merchant_icon_carousel[0]" style="width: 35px; height: 35px; border-radius: 50%"></image>
                    </view>
                    <view class="center_text" style="margin-left: 10px">{{ item.easy_info.merchant_name }}</view>
                </view>
                <view class="center_text" style="margin-top: 80rpx; color: #999">商品</view>
                <view class="flex solid-bottom padding justify-around" style="margin-top: 20rpx">
                    <view class="center_text">
                        <text>{{ item.order_info.product_name }}</text>
                        <text style="margin-left: 10px">{{ item.order_info.vested_attribute[1] }}</text>
                    </view>
                    <view class="center_text">x1</view>
                </view>
            </block>
        </block>
        <canvas style="width: 200px; height: 200px" canvas-id="myQrcode"></canvas>
        <view class="padding gb">
            <button @tap="exit" class="cu-btn bg-gray lg" style="width: 100%">关闭</button>
        </view>
    </view>
</template>

<script>
var app = getApp();
var http = require('../../util/http.js');
import regeneratorRuntime from '../../util/runtime';

export default {
    /**
     * 页面的初始数据
     */
    data() {
        return {
            token: '',
            info: '',
            item: ''
        }
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        console.log(options);
        if (options.hasOwnProperty('q') && options.q) {
            // 通过下面这步解码，可以拿到url的值，obj是获取链接里面的参数
            const url = decodeURIComponent(options.q);
            const obj = this.urlParams(url);
            console.log(obj);
            this.token = obj.token;
            this.doIt();
        } else {
            //未识别出内容
        }
    },
    methods: {
        async doIt() {
            var e = app.globalData.getCache('userinfo');
            if (!e) {
                //缓存为空执行登陆
                const do1 = await this.getLogin();
            } else {
                //字段为空执行登陆
                if (typeof e.token_impede == 'undefined') {
                    const do1 = await this.getLogin();
                } else {
                    //字段0或者小于当前时间执行登陆
                    var t = parseInt(+new Date() / 1000);
                    if (e.token_impede == 0 || t >= e.token_impede) {
                        const do1 = await this.getLogin();
                    }
                }
            }
            const do2 = await this.SetPos();
        },
        SetPos() {
            var that = this;
            return new Promise((resolve, reject) => {
                var e = app.globalData.getCache('userinfo');
                var data = new Object();
                data.token_data = that.token;
                data.token = e.token;
                data.openid = e.openid;
                console.log(data);
                http.POST(app.globalData.api_root + 'Ranking/set_pos', {
                    params: data,
                    success: (d) => {
                        console.log(d);
                        that.info = d.data;
                        that.item = d.data.info;
                        resolve(d);
                    }
                });
            });
        },
        /**
         * 登陆
         */
        getLogin() {
            return new Promise((resolve, reject) => {
                uni.login({
                    success(res) {
                        console.log(res);
                        var params = new Object();
                        params.code = res.code;
                        http.POST(app.globalData.api_root + 'Login/index', {
                            params: params,
                            success: (open) => {
                                console.log(open);
                                if (open.data['code'] != 0) {
                                    uni.showModal({
                                        title: '系统提示',
                                        content: '您设置的小程序参数有误，请自行检查！'
                                    });
                                    return;
                                }
                                var data = new Object();
                                data.openid = open.data.info.openid;
                                data.session_key = open.data.info.session_key;
                                console.log(data);
                                http.POST(app.globalData.api_root + 'Login/add_tourist', {
                                    params: data,
                                    success: (d) => {
                                        console.log(d.data.info);
                                        app.globalData.setCache('userinfo', d.data.info);
                                        resolve(d);
                                    }
                                });
                            }
                        });
                    }
                });
            });
        },
        urlParams(url) {
            let obj = {};
            let str = url.slice(url.indexOf('?') + 1);
            let arr = str.split('&');
            for (let j = arr.length, i = 0; i < j; i++) {
                let arr_temp = arr[i].split('=');
                obj[arr_temp[0]] = arr_temp[1];
            }
            return obj;
        },
        exit() {
            uni.exitMiniProgram();
        }
    }
}
</script>
<style>
page {
    background-color: #ffffff;
}
.hx {
    margin-top: 20rpx;
    font-size: 40rpx;
    font-weight: 700;
}
.gb {
    bottom: 38px;
    position: absolute;
    width: 90%;
    margin: 0 auto;
    left: 0;
    right: 0;
}
</style>
