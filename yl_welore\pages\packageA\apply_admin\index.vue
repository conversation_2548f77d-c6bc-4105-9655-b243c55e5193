<template>
    <view>
        <cu-custom bgColor="bg-white" :isBack="true" :isSearch="false">
            <view slot="backText">返回</view>
            <view slot="content" style="color: #2c2b2b; font-weight: 600; font-size: 36rpx">申请列表</view>
        </cu-custom>
        <view>
            <block v-for="(item, index) in user_list" :key="index">
                <view class="cu-list menu-avatar">
                    <view class="cu-item">
                        <view class="cu-avatar round lg" :style="'background-image:url(' + item.user_info.user_head_sculpture + ');'"></view>
                        <view class="content flex-sub">
                            <view class="text-grey">{{ item.user_info.user_nick_name }}</view>
                            <view class="text-gray text-sm flex justify-between">
                                <text style="color: #000">附加消息：{{ item.upshot }}</text>
                            </view>
                        </view>
                        <view class="flex align-end">
                            <button
                                @tap="add_envite_sulord"
                                :data-user_openid="item.user_info.user_wechat_open_id"
                                class="cu-btn round bg-green"
                                role="button"
                                :aria-disabled="false"
                            >
                                任命
                            </button>
                        </view>
                    </view>
                </view>
            </block>
        </view>
        <view :class="'cu-load ' + (user_list.length == 0 ? 'over' : '')"></view>
    </view>
</template>

<script>
const app = getApp();
const http = require('../../../util/http.js');
export default {
    data() {
        return {
            user_list: [],
            id: ''
        };
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        this.id = options.id;
        this.get_my_rec();
    },
    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {},
    methods: {
        /**
         * 申请列表
         */
        get_my_rec() {
            const e = app.globalData.getCache('userinfo');
            const params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.uid = e.uid;
            params.id = this.id;
            const b = app.globalData.api_root + 'User/get_envite_sulord';
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.status == 'success') {
                        this.user_list = res.data.info;
                    } else {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => {}
                    });
                }
            });
        },

        /**
         * 任命管理员
         */
        add_envite_sulord(data) {
            const e = app.globalData.getCache('userinfo');
            const params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.uid = e.uid;
            params.id = this.id;
            params.user_openid = data.currentTarget.dataset.user_openid;
            const b = app.globalData.api_root + 'User/add_envite_sulord';
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.status == 'success') {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                        this.get_my_rec();
                    } else {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => {}
                    });
                }
            });
        }
    }
};
</script>
<style>
page {
    background-color: #fff;
}
</style>
