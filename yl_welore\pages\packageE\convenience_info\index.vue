<template>
    <view class="page-container">
        <cu-custom bgColor="none" :isSearch="false" :isBack="true" style="color: #000000;">
            <view slot="backText"></view>
            <view slot="content" class="title-text">{{ config.custom_title }}</view>
        </cu-custom>
        <view class="search-container">
            <view class="search-form-custom">
                <text>🔍</text>
                <input type="text" placeholder="输入搜索内容" confirm-type="search" @confirm="search_key" class="search-input" />
            </view>
        </view>
        <view class="category-container">
            <swiper
                class="category-swiper"
                :indicator-dots="true"
                :style="'height: ' + (info[0].length > 5 ? 450 : 255) + 'rpx;'"
                indicator-active-color="#4A90E2"
                indicator-color="rgba(74, 144, 226, 0.3)"
            >
                <block v-for="(one, e_index) in info" :key="e_index">
                    <swiper-item>
                        <view class="grid col-5 text-center category-grid">
                            <view @tap="get_type_list" :data-id="item.id" :data-name="item.name" class="category-item" v-for="(item, index) in one" :key="index">
                                <view class="category-icon-wrapper">
                                    <image :lazy-load="true" :src="item.icon" class="category-icon"></image>
                                </view>
                                <view class="category-text">
                                    {{ item.name }}
                                </view>
                            </view>
                        </view>
                    </swiper-item>
                </block>
            </swiper>
        </view>
        <view @tap="hideModal_list" class="selected-category-container" v-if="type_name != ''">
            <view class="selected-category-tag">📋 {{ type_name }}</view>
        </view>
        <view class="merchant-list-container">
            <view @tap="open_info" :data-id="l_index" class="merchant-card" v-for="(item, l_index) in list" :key="l_index">
                <view class="merchant-avatar" :style="'background-image:url(' + item.merchant_icon_carousel[0] + ');'"></view>

                <view class="merchant-content">
                    <view class="merchant-name">{{ item.merchant_name }}</view>
                    <view class="merchant-address">
                        <text
                            class="address-text"
                            @tap.stop.prevent="get_position"
                            :data-pos_name="item.address_name"
                            :data-latitude="item.address_latitude"
                            :data-longitude="item.address_longitude"
                        >
                        📍 {{ item.address_name }}
                        </text>
                    </view>
                </view>

                <view class="merchant-action">
                    <button @tap.stop.prevent="open_phone" :data-p="item.merchant_phone" class="call-btn">📞 拨号</button>
                </view>
            </view>
        </view>
        <view v-if="config.is_show_btn == 1" @tap="open_img" class="join-btn-container">
            <view class="join-btn">
                <image :src="config.btn_icon" class="join-icon"></image>
                <text class="join-text">入驻</text>
            </view>
        </view>
        <view class="loading-container" :class="'cu-load ' + (!di_msg ? 'loading' : 'over')"></view>

        <view :class="'cu-modal ' + (modalShow ? 'show' : '')" @tap="hideModal">
            <view class="cu-dialog">
                <view @tap.stop.prevent="open_qrimg" class="bg-img" :style="'background-image: url(' + config.waiter_qrcode + ');height:700rpx;'">
                    <view class="cu-bar justify-end text-white"></view>
                </view>
                <view class="bg-white" @tap.stop.prevent="open_xuzhi">
                    <text class="cicon-info-o margin-right-xs"></text>
                    <text>入驻须知</text>
                </view>
                <view class="cu-bar bg-white">
                    <view class="action margin-0 flex-sub solid-left" @tap="hideModal">点击长按二维码</view>
                </view>
            </view>
        </view>
        <view :class="'cu-modal ' + (modalxuzhi ? 'show' : '')">
            <view class="cu-dialog">
                <view class="cu-bar bg-white justify-end">
                    <view class="content">入驻须知</view>
                </view>
                <scroll-view :scroll-y="true" class="bg-white" style="height: 1000rpx; padding: 10px">
                    <rich-text :user-select="true" :nodes="memo"></rich-text>
                </scroll-view>
                <view class="cu-bar bg-white">
                    <view class="action margin-0 flex-sub solid-left" @tap="qieModal">确定</view>
                </view>
            </view>
        </view>
        <view :class="'cu-modal bottom-modal ' + (show ? 'show' : '')" @tap="hideModal">
            <view class="cu-dialog" @tap.stop.prevent="a" style="height: 85%;text-align: left; border-radius: 15px 15px 0px 0px">
                <scroll-view :scroll-y="true" class="detail-page">
                    <swiper
                        class="square-dot"
                        indicator-active-color="#000000"
                        :indicator-dots="true"
                        style="height: 400rpx; width: 100%"
                        :autoplay="true"
                        interval="5000"
                        duration="1000"
                    >
                        <swiper-item style="text-align: center; overflow: hidden; border-radius: 15px" v-for="(item, index) in capy_info.merchant_icon_carousel" :key="index">
                            <image @tap.stop.prevent="open_img_list" :data-src="item" style="width: 100%; border-radius: 15px" mode="widthFix" :src="item" />
                        </swiper-item>
                    </swiper>
                    <view>
                        <view class="detail-header">
                            <view class="detail-title">{{ capy_info.merchant_name }}</view>
                            <view>
                                <button hover-class="none" open-type="share" @tap.stop.prevent="123" class="share-btn">
                                    <!-- <image :src="http_root + 'addons/yl_welore/web/static/applet_icon/zf.png'" class="share-icon"></image> -->
                                    <text class="share-text">📤 分享</text>
                                </button>
                            </view>
                        </view>
                        <view class="detail-address-section">
                            <view class="address-info">
                                <text class="address-label">{{ capy_info.address_name }}</text>
                            </view>
                            <view class="action-buttons">
                                <view
                                    @tap.stop.prevent="get_position"
                                    :data-pos_name="capy_info.address_name"
                                    :data-latitude="capy_info.address_latitude"
                                    :data-longitude="capy_info.address_longitude"
                                    class="action-btn"
                                >
                                    <!-- <image :src="http_root + 'addons/yl_welore/web/static/applet_icon/ditu.png'" class="action-icon"></image> -->
                                    <text class="action-text">📍 到这里</text>
                                </view>
                                <view @tap.stop.prevent="open_phone" :data-p="capy_info.merchant_phone" class="action-btn">
                                    <!-- <image :src="http_root + 'addons/yl_welore/web/static/applet_icon/dianhua.png'" class="action-icon"></image> -->
                                    <text class="action-text">📞 打电话</text>
                                </view>
                            </view>
                        </view>
                    </view>
                    <view class="detail-content-section">
                        <view class="detail-section-title">📋 详情介绍</view>
                        <view class="detail-content">
                            <rich-text :user-select="true" :nodes="capy_info.merchant_introduce"></rich-text>
                        </view>
                    </view>
                </scroll-view>
            </view>
        </view>
    </view>
</template>

<script>
const app = getApp();
const http = require('../../../util/http.js');

export default {
    data() {
        return {
            http_root: app.globalData.http_root,
            show: false,
            modalxuzhi: false,
            modalShow: false,
            list: [],
            page: 1,
            type: 0,
            capy_info: {},
            di_msg: false,
            search_value: '',
            type_name: '',
            memo: '',
            config: {},
            info: []
        }
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        this.get_easy_list();
        this.get_easy_config();
        console.log(options.id);
        if (typeof options.id != 'undefined') {
            //查询ID详情
            this.get_easy_info(options.id);
        }
    },
    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {
        const convenience = app.globalData.__PlugUnitScreen('937c07a443d278405c670892d2fc89b6');
        if (!convenience) {
            this.BackPage();
        }
    },
    /**
     * 加载下一页
     */
    onReachBottom() {
        this.page = this.page + 1;
        this.get_easy_list();
    },
    /**
     * 用户点击右上角分享
     */
    onShareAppMessage(d) {
        const that = this;
        if (d.from == 'button') {
            return {
                title: that.capy_info.merchant_name,
                path: '/yl_welore/pages/packageE/convenience_info/index?id=' + that.capy_info.id,
                imageUrl: that.capy_info.merchant_icon_carousel[0]
            };
        }
    },
    methods: {
        open_xuzhi() {
            let memo = this.config.precautions;
            memo = memo.replace(/\<img/gi, '<img style="width:100%;height:auto"');
            this.memo = memo;
            this.modalShow = false;
            this.modalxuzhi = true;
        },
        qieModal() {
            this.modalShow = true;
            this.modalxuzhi = false;
        },
        search_key(d) {
            const key = d.detail.value;
            this.search_value = key;
            this.page = 1;
            this.list = [];
            this.get_easy_list();
        },
        open_img() {
            this.modalShow = true;
        },
        open_qrimg() {
            const that = this;
            uni.previewImage({
                current: that.config.waiter_qrcode,
                // 当前显示图片的http链接
                urls: [that.config.waiter_qrcode] // 需要预览的图片http链接列表
            });
        },
        open_img_list(d) {
            const that = this;
            uni.previewImage({
                current: d.currentTarget.dataset.src,
                // 当前显示图片的http链接
                urls: that.capy_info.merchant_icon_carousel // 需要预览的图片http链接列表
            });
        },
        hideModal_list() {
            this.search_value = '';
            this.type_name = '';
            this.page = 1;
            this.type = 0;
            this.list = [];
            this.get_easy_list();
        },
        hideModal() {
            this.modalShow = false;
            this.show = false;
        },
        get_type_list(d) {
            const id = d.currentTarget.dataset.id;
            this.type = id;
            this.page = 1;
            this.search_value = '';
            this.list = [];
            this.type_name = d.currentTarget.dataset.name;
            this.get_easy_list();
        },
        BackPage() {
            const pages = getCurrentPages();
            if (pages.length == 1) {
                this.toHome();
                return;
            }
            uni.navigateBack();
        },
        toHome() {
            uni.reLaunch({
                url: '/yl_welore/pages/index/index'
            });
        },
        get_easy_info(id) {
            const b = app.globalData.api_root + 'Ranking/get_easy_info';
            const that = this;
            const e = app.globalData.getCache('userinfo');
            const params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.id = id;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    that.capy_info = res.data.info;
                    that.show = true;
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: () => {}
                    });
                }
            });
        },
        get_easy_config() {
            const b = app.globalData.api_root + 'Ranking/get_easy_config';
            const that = this;
            const e = app.globalData.getCache('userinfo');
            const params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            http.POST(b, {
                params: params,
                success: (res) => {
                    that.config = res.data.config;
                    that.info = res.data.info;
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: () => {}
                    });
                }
            });
        },
        get_easy_list() {
            const b = app.globalData.api_root + 'Ranking/get_easy_list';
            const that = this;
            const e = app.globalData.getCache('userinfo');
            const params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.type = this.type;
            params.page = this.page;
            params.search_key = this.search_value;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.info.length < 5) {
                        that.di_msg = true;
                    }
                    if (res.data.info.length > 0) {
                        const list = that.list;
                        list.push(...res.data.info);
                        that.list = list;
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: () => {}
                    });
                }
            });
        },
        /**
         * 打开地图
         */
        get_position(d) {
            console.log(d);
            const a = Number(d.currentTarget.dataset.latitude);
            const o = Number(d.currentTarget.dataset.longitude);
            const name = d.currentTarget.dataset.pos_name;
            if (a && o) {
                uni.openLocation({
                    latitude: a,
                    longitude: o,
                    name: name
                });
            }
        },
        open_phone(d) {
            console.log(d);
            uni.makePhoneCall({
                phoneNumber: d.currentTarget.dataset.p
            });
        },
        open_info(d) {
            const index = d.currentTarget.dataset.id;
            const info = this.list[index];
            info.merchant_introduce = info.merchant_introduce.replace(/\<img/gi, '<img style="width:100%;height:auto"');
            this.capy_info = info;
            this.show = true;
        }
    }
}
</script>
<style>
page {
    background: linear-gradient(135deg, #E3F2FD 0%, #BBDEFB 50%, #90CAF9 100%);
    min-height: 100vh;
    background-attachment: fixed;
}

.page-container {
    background: transparent;
}

/* 标题栏样式 */
.bg-gradient-blue {
    background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%) !important;
}

.title-text {
    color: #000000 !important;
    font-weight: 600 !important;
    font-size: 36rpx !important;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

/* 搜索栏样式 */
.search-container {
    background: rgba(255, 255, 255, 0.9);
    padding: 20rpx;
    margin: 20rpx;
    border-radius: 20rpx;
    box-shadow: 0 4rpx 12rpx rgba(74, 144, 226, 0.15);
}

.search-form-custom {
    background: #ffffff;
    border-radius: 50rpx;
    padding: 20rpx 30rpx;
    display: flex;
    align-items: center;
    box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.search-icon {
    color: #4A90E2;
    margin-right: 20rpx;
    font-size: 32rpx;
}

.search-input {
    flex: 1;
    font-size: 28rpx;
    color: #333;
}

/* 分类区域样式 */
.category-container {
    margin: 20rpx;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 20rpx;
    box-shadow: 0 4rpx 12rpx rgba(74, 144, 226, 0.15);
    overflow: hidden;
}

.category-swiper {
    background: transparent;
}

.category-grid {
    padding: 30rpx 20rpx;
}

.category-item {
    margin: 10rpx 0;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.category-icon-wrapper {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 20rpx;
    padding: 20rpx;
    margin-bottom: 15rpx;
    box-shadow: 0 4rpx 8rpx rgba(74, 144, 226, 0.1);
    transition: all 0.3s ease;
}

.category-icon-wrapper:active {
    transform: scale(0.95);
    box-shadow: 0 2rpx 4rpx rgba(74, 144, 226, 0.2);
}

.category-icon {
    width: 80rpx;
    height: 80rpx;
    border-radius: 10rpx;
}

.category-text {
    font-size: 24rpx;
    color: #333;
    text-align: center;
    font-weight: 500;
}

/* 选中分类标签样式 */
.selected-category-container {
    text-align: center;
    padding: 20rpx;
    margin: 0 20rpx;
}

.selected-category-tag {
    display: inline-block;
    background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
    color: #ffffff;
    padding: 15rpx 30rpx;
    border-radius: 50rpx;
    font-size: 28rpx;
    font-weight: 500;
    box-shadow: 0 4rpx 12rpx rgba(74, 144, 226, 0.3);
}

/* 商家列表样式 */
.merchant-list-container {
    margin: 20rpx;
}

.merchant-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20rpx;
    margin-bottom: 20rpx;
    padding: 30rpx;
    display: flex;
    align-items: center;
    box-shadow: 0 6rpx 16rpx rgba(74, 144, 226, 0.12);
    transition: all 0.3s ease;
}

.merchant-card:active {
    transform: translateY(2rpx);
    box-shadow: 0 4rpx 12rpx rgba(74, 144, 226, 0.18);
}

.merchant-avatar {
    width: 120rpx;
    height: 120rpx;
    border-radius: 20rpx;
    background-size: cover;
    background-position: center;
    background-color: #f5f5f5;
    margin-right: 30rpx;
    box-shadow: 0 4rpx 8rpx rgba(0,0,0,0.1);
}

.merchant-content {
    flex: 1;
    min-width: 0;
}

.merchant-name {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 15rpx;
}

.merchant-address {
    font-size: 26rpx;
}

.address-text {
    color: #666;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.merchant-action {
    margin-left: 20rpx;
}

.call-btn {
    background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
    color: #ffffff;
    border: none;
    border-radius: 50rpx;
    padding: 20rpx 30rpx;
    font-size: 26rpx;
    font-weight: 500;
    box-shadow: 0 4rpx 12rpx rgba(74, 144, 226, 0.3);
    transition: all 0.3s ease;
}

.call-btn:active {
    transform: scale(0.95);
    box-shadow: 0 2rpx 8rpx rgba(74, 144, 226, 0.4);
}

/* 入驻按钮样式 */
.join-btn-container {
    position: fixed;
    right: 0;
    left: 0;
    bottom: 3%;
    text-align: center;
    z-index: 100;
}

.join-btn {
    display: inline-block;
    background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
    border-radius: 50rpx;
    padding: 20rpx 40rpx;
    box-shadow: 0 8rpx 20rpx rgba(74, 144, 226, 0.4);
    transition: all 0.3s ease;
}

.join-btn:active {
    transform: scale(0.95);
    box-shadow: 0 6rpx 16rpx rgba(74, 144, 226, 0.5);
}

.join-icon {
    width: 50rpx;
    height: 50rpx;
    vertical-align: middle;
    margin-right: 10rpx;
}

.join-text {
    color: #ffffff;
    font-size: 28rpx;
    font-weight: 600;
    vertical-align: middle;
}

/* 加载容器样式 */
.loading-container {
    padding-bottom: 200rpx;
}

.detail-page {
    width: 100%;
    height: 90%;
    padding: 5px;
}

.overlayStyle {
    background-color: rgba(0, 0, 0, 0.1);
    filter: blur(4px);
}

button::after {
    line-height: normal;
    font-size: 30rpx;
    width: 0;
    height: 0;
    top: 0;
    left: 0;
}

button {
    line-height: normal;
    display: block;
    padding-left: 0px;
    padding-right: 0px;
    background-color: rgba(255, 255, 255, 0);
    font-size: 30rpx;
    overflow: inherit;
}

/* 弹窗详情页样式 */
.detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx 20rpx;
    border-bottom: 2rpx solid #f0f0f0;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
}

.detail-title {
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
}

.share-btn {
    background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
    border-radius: 30rpx;
    padding: 15rpx 25rpx;
    display: flex;
    align-items: center;
    box-shadow: 0 4rpx 8rpx rgba(74, 144, 226, 0.2);
}

.share-icon {
    height: 30rpx;
    width: 30rpx;
    margin-right: 10rpx;
}

.share-text {
    color: #ffffff;
    font-size: 24rpx;
    font-weight: 500;
}

.detail-address-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx;
    border-bottom: 2rpx solid #f0f0f0;
    background: #ffffff;
}

.address-info {
    flex: 1;
    min-width: 0;
}

.address-label {
    font-size: 28rpx;
    color: #666;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: block;
}

.action-buttons {
    display: flex;
    gap: 30rpx;
}

.action-btn {
    text-align: center;
    background: rgba(74, 144, 226, 0.1);
    border-radius: 20rpx;
    padding: 20rpx;
    transition: all 0.3s ease;
}

.action-btn:active {
    background: rgba(74, 144, 226, 0.2);
    transform: scale(0.95);
}

.action-icon {
    height: 40rpx;
    width: 40rpx;
    display: block;
    margin: 0 auto 10rpx;
}

.action-text {
    font-size: 24rpx;
    color: #4A90E2;
    font-weight: 500;
}

.detail-content-section {
    background: #ffffff;
    margin-top: 20rpx;
}

.detail-section-title {
    padding: 30rpx 20rpx 20rpx;
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
}

.detail-content {
    padding: 20rpx;
    line-height: 1.6;
    color: #666;
}
</style>
