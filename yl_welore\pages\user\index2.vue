<template>
    <view>
        <view class="nav-wrap container" style="position: relative; height: 250px">
            <cu-custom :isSearch="false" :isBack="false" :ShowUid="false">
                <view v-if="elect_sheathe == 0 && design.shop_arbor == 1" slot="left_z">
                    <view style="display: flex; justify-content: space-around; flex-direction: column; height: 100%">
                        <navigator url="/yl_welore/pages/user_smail/index" hover-class="none">
                            <view class="cu-avatar radius" style="background-color: transparent; width: 35px; height: 35px">
                                <image src="/static/yl_welore/style/icon/bgt.png" style="width: 35px; height: 35px"></image>
                                <view class="cu-tag badge">{{ user_info.user_male }}</view>
                            </view>
                        </navigator>
                    </view>
                </view>
                <view slot="content" style="color: #fff; font-weight: 600; font-size: 36rpx">个人中心</view>
            </cu-custom>
            <view style="position: absolute; top: 40%; font-size: 18px; color: #fff; font-weight: 600; letter-spacing: 1.5px; left: 7%">
                <text :class="'course-name ' + user_info.special">{{ user_info.user_nick_name }}</text>
                <image
                    mode="heightFix"
                    class="now_level"
                    v-if="user_info.wear_merit"
                    :src="user_info.wear_merit"
                    style="height: 20px; vertical-align: sub; margin-left: 3px"
                ></image>
            </view>

            <view @tap="my_level" v-if="user_info.tourist == 0" style="position: absolute; top: 55%; color: #7f8daf; letter-spacing: 1.5px; left: 4%">
                <image class="now_level" mode="widthFix" :src="user_info.level_info.level_icon" style="margin: 0px 0px 0px 13px; width: 50rpx; vertical-align: middle"></image>
                <text style="vertical-align: sub; color: #ffffff; margin: 0px 0px 0px 10px; font-size: 14px; vertical-align: middle">{{ user_info.level_info.level_name }}</text>
            </view>
            <button
                v-if="user_info.tourist == 1"
                style="color: #fff; position: absolute; top: 55%; left: 7%; font-size: 14px; z-index: 200"
                @tap="onGotUserInfo"
                hover-class="none"
            >
                立即登录
            </button>

            <view @tap="my_home" class="cu-avatar round home" :style="'background-image:url(' + user_info.user_head_sculpture + ');position: absolute;top: 38%;right: 10%;'">
                <view style="z-index: 10" :class="'cu-tag badge ' + (user_info.gender == 2 ? 'cuIcon-female bg-pink' : 'cuIcon-male bg-blue')"></view>
                <image v-if="user_info.attest" :src="user_info.attest" style="z-index: 100; width: 40rpx; height: 40rpx; position: absolute; right: 0px; bottom: -5px"></image>
                <image class="now_level" style="height: 140rpx; width: 140rpx; position: absolute; max-width: initial" :src="user_info.avatar_frame"></image>
            </view>
        </view>
        <view style="padding: 10px 0px 0px 0px; border-radius: 5px; width: 93%; background-color: #fff; position: relative; top: -55px; margin: 0 auto">
            <view class="bg-white padding-xs">
                <view class="grid col-4 text-center">
                    <view @tap="user_url" data-index="2">
                        <view class="number_text">{{ user_info.user_track }}</view>
                        <view class="info_text">我的关注</view>
                    </view>
                    <view @tap="user_url" data-index="18">
                        <view class="number_text" style="position: relative">
                            <image :src="http_root + 'addons/yl_welore/web/static/applet_icon/task.png'" style="width: 30px; height: 30px"></image>
                            <view
                                v-if="user_info.task_count > 0"
                                class="cu-tag badge"
                                style="right: 20rpx; border-radius: 50%; font-size: 24rpx; padding: 10rpx; height: 35rpx; width: 35rpx"
                            >
                                {{ user_info.task_count }}
                            </view>
                        </view>
                        <view class="info_text">任务中心</view>
                    </view>
                    <view @tap="user_url" data-index="3">
                        <view class="number_text">{{ user_info.user_fs }}</view>
                        <view class="info_text">我的粉丝</view>
                    </view>
                    <view>
                        <view v-if="user_info.check_chou == 1">
                            <view class="number_text">
                                <image
                                    @tap="user_url"
                                    data-index="19"
                                    :src="http_root + 'addons/yl_welore/web/static/wechat/sign_in.png'"
                                    style="height: 30px; width: 30px"
                                ></image>
                            </view>
                            <view class="info_text">签到</view>
                        </view>
                        <view v-if="user_info.check_chou == 0">
                            <view
                                class="center_text bg-gradual-red shadow-blur"
                                v-if="user_info.is_sign == 0"
                                @tap="bid_qiandao"
                                style="height: 50px; width: 50px; color: #fff; border-radius: 50%; margin: 0 auto"
                            >
                                <text>签到</text>
                            </view>
                            <view
                                class="center_text bg-gray"
                                v-if="user_info.is_sign == 1 && flag == false"
                                style="height: 50px; width: 50px; color: #fff; border-radius: 50%; margin: 0 auto"
                            >
                                <text>已签到</text>
                            </view>
                            <view
                                class="center_text bg-gray"
                                v-if="flag == true && user_info.is_sign == 1"
                                style="height: 50px; width: 50px; color: #fff; border-radius: 50%; margin: 0 auto"
                            >
                                <text>已签到</text>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
            <view class="vip_style_2" @tap="user_url" data-index="1" v-if="copyright.noble_arbor == 1 && version == 0">
                <view class="flex solid-bottom padding align-center" style="height: 100%; margin-top: 10px">
                    <view class="">
                        <image
                            v-if="user_info.is_vip == 0"
                            :src="http_root + 'addons/yl_welore/web/static/applet_icon/novip.png'"
                            style="width: 30px; height: 30px; vertical-align: middle"
                        ></image>
                        <image
                            v-if="user_info.is_vip == 1"
                            :src="http_root + 'addons/yl_welore/web/static/applet_icon/vip.png'"
                            style="width: 30px; height: 30px; vertical-align: middle"
                        ></image>
                    </view>
                    <view style="color: #ffffff; font-size: 16px; font-weight: 500; letter-spacing: 2px; margin-left: 10px">
                        {{ user_info.vip_end_time_tmpl == 0 ? '立即开通会员' : user_info.vip_end_time_tmpl }}
                    </view>
                    <view style="position: absolute; right: 40rpx">
                        <button
                            v-if="user_info.is_vip == 0"
                            style="background-image: linear-gradient(to right, #fcefd7 0%, #eed6a3 70%); font-size: 12px; color: #ffffff"
                            class="cu-btn round sm"
                        >
                            立即开通
                        </button>
                        <button
                            v-if="user_info.is_vip == 1"
                            style="background-image: linear-gradient(to right, #fcefd7 0%, #eed6a3 70%); font-size: 12px; color: #ffffff"
                            class="cu-btn round sm"
                        >
                            查看权限
                        </button>
                    </view>
                </view>
            </view>
        </view>

        <view style="top: -40px; border-radius: 5px; width: 93%; background-color: #fff; position: relative; margin: 0 auto">
            <view class="grid col-4 padding-sm text-center">
                <view @tap="user_url" data-index="5">
                    <view class="placeholder">
                        <image :src="http_root + 'addons/yl_welore/web/static/mineIcon/user2/favorites.png'" style="width: 30px; height: 30px"></image>
                        <view class="info_text">我的收藏</view>
                    </view>
                </view>
                <view @tap="user_url" data-index="6">
                    <view class="placeholder">
                        <image :src="http_root + 'addons/yl_welore/web/static/mineIcon/user2/plane.png'" style="width: 30px; height: 30px"></image>
                        <view class="info_text">我的{{ design.landgrave }}</view>
                    </view>
                </view>
                <view @tap="user_url" data-index="20" v-if="version == 0 && copyright.whisper_arbor == 1 && user_info.conceal == 0">
                    <view class="placeholder" style="position: relative">
                        <view
                            v-if="$state.slogin == 0 && user_info.secret > 0"
                            class="cu-tag badge"
                            style="border-radius: 50%; font-size: 24rpx; padding: 10rpx; height: 35rpx; width: 35rpx"
                        >
                            {{ user_info.secret }}
                        </view>
                        <image :src="http_root + 'addons/yl_welore/web/static/mineIcon/user2/secret.png'" style="width: 30px; height: 30px"></image>
                        <view class="info_text">{{ design.custom_hiss_title ? design.custom_hiss_title : '树洞' }}</view>
                    </view>
                </view>
                <view v-if="version == 0 && copyright.tribute_arbor == 1" @tap="user_url" data-index="7">
                    <view class="placeholder">
                        <image :src="http_root + 'addons/yl_welore/web/static/mineIcon/user2/gift.png'" style="width: 30px; height: 30px"></image>
                        <view class="info_text">我的礼物</view>
                    </view>
                </view>
                <view v-if="version == 0 && copyright.shop_arbor == 1" @tap="user_url" data-index="8">
                    <view class="placeholder">
                        <image :src="http_root + 'addons/yl_welore/web/static/mineIcon/user2/bill.png'" style="width: 30px; height: 30px"></image>
                        <view class="info_text">我的订单</view>
                    </view>
                </view>
                <view v-if="copyright.wallet_arbor == 1 && version == 0 && user_info.conceal == 0" @tap="user_url" data-index="9">
                    <view class="placeholder">
                        <image :src="http_root + 'addons/yl_welore/web/static/mineIcon/user2/payment.png'" style="width: 30px; height: 30px"></image>
                        <view class="info_text">我的钱包</view>
                    </view>
                </view>
                <view v-if="version == 0 && open_wangpan" @tap="user_url" data-index="26">
                    <view class="placeholder">
                        <image :src="http_root + 'addons/yl_welore/web/static/mineIcon/user2/wangpan.png'" style="width: 30px; height: 30px"></image>
                        <view class="info_text">我的网盘</view>
                    </view>
                </view>
                <view v-if="version == 0" @tap="user_url" data-index="10">
                    <view class="placeholder">
                        <image :src="http_root + 'addons/yl_welore/web/static/mineIcon/user2/share.png'" style="width: 30px; height: 30px"></image>
                        <view class="info_text">邀请好友</view>
                    </view>
                </view>

                <view v-if="version == 0 && copyright.shop_arbor == 1 && elect_sheathe == 1" @tap="user_url" data-index="11">
                    <view class="placeholder">
                        <image :src="http_root + 'addons/yl_welore/web/static/mineIcon/user2/ship.png'" style="width: 30px; height: 30px"></image>
                        <view class="info_text">{{ design.mall }}</view>
                    </view>
                </view>

                <view @tap="user_url" data-index="15">
                    <view class="placeholder">
                        <image :src="http_root + 'addons/yl_welore/web/static/mineIcon/user2/warning.png'" style="width: 30px; height: 30px"></image>
                        <view class="info_text">黑名单</view>
                    </view>
                </view>

                <view v-if="version == 0 && copyright.engrave_arbor == 1" @tap="user_url" data-index="21">
                    <view class="placeholder">
                        <image :src="http_root + 'addons/yl_welore/web/static/mineIcon/user2/name_card.png'" style="width: 30px; height: 30px"></image>
                        <view class="info_text">身份铭牌</view>
                    </view>
                </view>

                <view @tap="user_url" data-index="16">
                    <view class="placeholder">
                        <image :src="http_root + 'addons/yl_welore/web/static/mineIcon/user2/date.png'" style="width: 30px; height: 30px"></image>
                        <view class="info_text">卡包</view>
                    </view>
                </view>
                <view @tap="user_url" data-index="17">
                    <view class="placeholder">
                        <image :src="http_root + 'addons/yl_welore/web/static/mineIcon/user2/sweep.png'" style="width: 30px; height: 30px"></image>
                        <view class="info_text">活动验证</view>
                    </view>
                </view>
                <view @tap="user_url" data-index="22" v-if="version == 0 && copyright.travel_arbor == 1">
                    <view class="placeholder">
                        <image :src="http_root + 'addons/yl_welore/web/static/mineIcon/user2/cert.png'" style="width: 30px; height: 30px"></image>
                        <view class="info_text">身份认证</view>
                    </view>
                </view>
                <view @tap="user_url" data-index="25" v-if="open_cord">
                    <view class="placeholder">
                        <image :src="http_root + 'addons/yl_welore/web/static/mineIcon/user2/duihuan.png'" style="width: 30px; height: 30px"></image>
                        <view class="info_text">卡密兑换</view>
                    </view>
                </view>
                <view @tap="user_url" data-index="23" v-if="version == 0 && copyright.feeling_arbor == 1 && copyright.feeling_stipulate == 0 && user_info.conceal == 0">
                    <view class="placeholder">
                        <image :src="http_root + 'addons/yl_welore/web/static/mineIcon/user2/tape.png'" style="width: 30px; height: 30px"></image>
                        <view class="info_text">{{ design.feel_title_em }}</view>
                    </view>
                </view>
                <view @tap="user_url" data-index="27" v-if="open_account">
                    <view class="placeholder">
                        <image :src="http_root + 'addons/yl_welore/web/static/mineIcon/user2/wechat.png'" style="width: 30px; height: 30px"></image>
                        <view class="info_text">公众通知</view>
                    </view>
                </view>
                <view @tap="user_url" data-index="28" v-if="version == 0 && open_lost">
                    <view class="placeholder">
                        <image :src="http_root + 'addons/yl_welore/web/static/mineIcon/user2/lost.png'" style="width: 30px; height: 30px"></image>
                        <view class="info_text">失物招领</view>
                    </view>
                </view>
                <view @tap="user_url" data-index="31" v-if="version == 0 && open_used">
                    <view class="placeholder">
                        <image :src="http_root + 'addons/yl_welore/web/static/mineIcon/user2/ershou.png'" style="width: 30px; height: 30px"></image>
                        <view class="info_text">{{ design.custom_title }}</view>
                    </view>
                </view>
                <view @tap="user_url" data-index="32" v-if="version == 0 && open_employ">
                    <view class="placeholder">
                        <image :src="http_root + 'addons/yl_welore/web/static/mineIcon/user2/pin.png'" style="width: 30px; height: 30px"></image>
                        <view class="info_text">{{ design.custom_title_em }}</view>
                    </view>
                </view>
                <view @tap="user_url" data-index="29" v-if="version == 0 && open_convenience">
                    <view class="placeholder">
                        <image :src="http_root + 'addons/yl_welore/web/static/mineIcon/user2/bianmin.png'" style="width: 30px; height: 30px"></image>
                        <view class="info_text">{{ design.easy_title_em }}</view>
                    </view>
                </view>
                <view @tap="user_url" data-index="30" v-if="version == 0 && open_convenience && user_info.assistant == 1">
                    <view class="placeholder">
                        <image :src="http_root + 'addons/yl_welore/web/static/mineIcon/user2/tihuo.png'" style="width: 30px; height: 30px"></image>
                        <view class="info_text">核销记录</view>
                    </view>
                </view>
                <view @tap="user_url" data-index="33" v-if="version == 0 && open_sweepstake">
                    <view class="placeholder">
                        <image :src="http_root + 'addons/yl_welore/web/static/mineIcon/user2/xingyun.png'" style="width: 30px; height: 30px"></image>
                        <view class="info_text">{{ design.sweepstake_title ? design.sweepstake_title : '幸运抽奖' }}</view>
                    </view>
                </view>
                <view @tap="user_url" data-index="34" v-if="version == 0 && copyright.short_drama_arbor == 1">
                    <view class="placeholder">
                        <image :src="http_root + 'addons/yl_welore/web/static/mineIcon/user2/duanju.png'" style="width: 30px; height: 30px"></image>
                        <view class="info_text">{{ design.micro_title ? design.micro_title : '短剧视频' }}</view>
                    </view>
                </view>
                <view @tap="user_url" data-index="12">
                    <view class="placeholder">
                        <image :src="http_root + 'addons/yl_welore/web/static/mineIcon/user2/security.png'" style="width: 30px; height: 30px"></image>
                        <view class="info_text">服务中心</view>
                    </view>
                </view>
                <view v-if="admin == 1" @tap="user_url" data-index="13">
                    <view class="placeholder">
                        <image :src="http_root + 'addons/yl_welore/web/static/mineIcon/user2/approval.png'" style="width: 30px; height: 30px"></image>
                        <view class="info_text">内容审核</view>
                    </view>
                </view>
                <view @tap="user_url" data-index="14">
                    <view class="placeholder">
                        <image :src="http_root + 'addons/yl_welore/web/static/mineIcon/user2/hotel.png'" style="width: 30px; height: 30px"></image>
                        <view class="info_text">关于我们</view>
                    </view>
                </view>
            </view>
        </view>

        <view class="" style="min-height: 110px;padding-bottom: 250rpx">
            <view class="" style="word-break: break-all; text-align: center; color: var(--blue)">{{ copyright.title }}</view>
            <view class="" style="word-break: break-all; text-align: center; font-size: 12px; margin-top: 10px">
                {{ copyright.copyright }}
            </view>
        </view>
    </view>
</template>

<script>
export default {
    props: ['data', 'compName'],
    computed: {
        elect_sheathe() {
            return this.$parent.$data.elect_sheathe;
        },
        design() {
            return this.$parent.$data.design;
        },
        user_info() {
            return this.$parent.$data.user_info;
        },
        animationData() {
            return this.$parent.$data.animationData;
        },
        flag() {
            return this.$parent.$data.flag;
        },
        animationDataD() {
            return this.$parent.$data.animationDataD;
        },
        http_root() {
            return this.$parent.$data.http_root;
        },
        copyright() {
            return this.$parent.$data.copyright;
        },
        version() {
            return this.$parent.$data.version;
        },
       
        open_wangpan() {
            return this.$parent.$data.open_wangpan;
        },
        open_cord() {
            return this.$parent.$data.open_cord;
        },
        open_lost() {
            return this.$parent.$data.open_lost;
        },
        open_used() {
            return this.$parent.$data.open_used;
        },
        open_employ() {
            return this.$parent.$data.open_employ;
        },
        open_convenience() {
            return this.$parent.$data.open_convenience;
        },
        open_sweepstake() {
            return this.$parent.$data.open_sweepstake;
        },
        open_account() {
            return this.$parent.$data.open_account;
        },
        admin() {
            return this.$parent.$data.admin;
        },
        
    },
    methods:{
        my_home(e) {
            this.$emit('my_home', e);
        },
        my_level(e) {
            this.$emit('my_level', e);
        },
        onGotUserInfo(e) {
            this.$emit('onGotUserInfo', e);
        },
        bid_qiandao(e) {
            this.$emit('bid_qiandao', e);
        },
        user_url(e) {
            this.$emit('user_url', e);
        }
    }
};
</script>
<style></style>
