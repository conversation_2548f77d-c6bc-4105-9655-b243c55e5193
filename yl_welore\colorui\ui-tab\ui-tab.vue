<template>
    <view
        :class="'ui-tab ' + ui + ' ' + tpl + ' ' + bg + ' ' + align + ' ' + (inline ? 'ui-tab-inline' : '') + ' ' + (scroll ? 'ui-tab-scrolls' : '')"
        :id="'ui-tab-' + uid_var"
        style="z-index: 100"
    >
        <view class="ui-tab-scroll-warp" v-if="scroll">
            <scroll-view
                :scroll-x="true"
                class="ui-tab-scroll"
                :scroll-left="curValue > 1 ? tabNodeList[curValue - 1].left : 0"
                scroll-with-animation
                :style="'width:' + content.width + 'px;'"
            >
                <block v-for="(item, index) in tab" :key="index">
                    <view
                        :id="'ui-tab-item-' + uid_var + '-' + index"
                        :class="'ui-tab-item ' + (curValue === index ? 'cur' : '') + ' ' + tpl"
                        :data-index="index"
                        :data-item="item"
                        @tap="clickFun"
                    >
                        <view :class="'ui-tab-icon ' + item.icon" v-if="item.icon" />
                        <view :class="'ui-tab-text ' + (curValue === index ? curColor : '')">{{ item.realm_name ? item.realm_name : item }}</view>
                        <view class="ui-tag badge ml-2" v-if="item.tag != null">{{ item.tag }}</view>
                    </view>
                </block>

                <view :class="'ui-tab-mark-warp ' + (over ? 'over' : '')" :style="'left:' + markLeft + 'px; width:' + markWidth + 'px;'">
                    <view :class="'ui-tab-mark ' + mark + ' ' + (tpl === 'btn' ? 'ui-btn' : '')">
                        <text class="text" v-if="tpl === 'btn'">{{ tab[curValue].name ? tab[curValue].name : tab[curValue] }}</text>
                    </view>
                </view>
            </scroll-view>
        </view>
        <block v-else>
            <block v-for="(item, index) in tab" :key="index">
                <view
                    :id="'ui-tab-item-' + uid_var + '-' + index"
                    :class="'ui-tab-item ' + (curValue === index ? 'cur' : '') + ' ' + tpl"
                    :data-index="index"
                    :data-item="item"
                    @tap="clickFun"
                >
                    <view :class="'ui-tab-icon ' + item.icon" v-if="item.icon" />
                    <view :class="'ui-tab-text ' + (curValue === index ? curColor : '')">{{ item.name ? item.name : item }}</view>
                    <view class="ui-tag badge ml-2" v-if="item.tag != null">{{ item.tag }}</view>
                </view>
            </block>
            <view :class="'ui-tab-mark-warp ' + (over ? 'over' : '')" :style="'left:' + markLeft + 'px; width:' + markWidth + 'px;'">
                <view :class="'ui-tab-mark ' + mark + ' ' + (tpl === 'btn' ? 'ui-btn' : '')">
                    <text class="text" v-if="tpl === 'btn'">{{ tab[curValue].name ? tab[curValue].name : tab[curValue] }}</text>
                </view>
            </view>
        </block>
    </view>
</template>

<script>
export default {
    data() {
        return {
            uid_var: '',
            curValue: 0,
            tabNodeList: [],
            scrollLeft: 0,
            markLeft: 0,
            markWidth: 0,

            content: {
                width: 100
            },

            over: false,
            left: '',
            name: false
        };
    },

    options: {
        addGlobalClass: true,
        multipleSlots: true
    },

    props: {
        value: {
            type: Number,
            default: 0
        },
        ui: {
            type: String,
            default: ''
        },
        bg: {
            type: String,
            default: 'ui-BG'
        },
        tab: {
            type: Array,
            default: () => []
        },
        // line dot long
        tpl: {
            type: String,
            default: 'line'
        },
        mark: {
            type: String,
            default: ''
        },
        align: {
            type: String,
            default: ''
        },
        curColor: {
            type: String,
            default: 'ui-TC'
        },
        scroll: {
            type: Boolean,
            default: false
        },
        inline: {
            type: Boolean,
            default: false
        }
    },

    mounted() {
        // 处理小程序 attached 生命周期
        this.attached();
        // 处理小程序 ready 生命周期
        this.$nextTick(() => this.ready());
    },

    watch: {
        tab: function () {
            this.computedChildQueryFun();
        },

        value: function (val) {
            if (val === this.curValue) {
                return false;
            } else {
                this.setCurValueFun(val);
            }
        }
    },

    methods: {
        ready() {
            this.computedQueryFun();
        },

        attached() {
            let _uid = this.getRandom(8);
            this.uid_var = _uid;
        },

        //生成随机字符串
        getRandom(num) {
            let chars = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
            let maxPos = chars.length;
            let value = '';
            for (let i = 0; i < num; i++) {
                value += chars.charAt(Math.floor(Math.random() * maxPos));
            }
            return value;
        },

        computedQueryFun() {
            let _this = this;
            let _uid = this.uid_var;
            uni.createSelectorQuery()
                .in(this)
                .select('#ui-tab-' + _uid)
                .boundingClientRect((data) => {
                    if (data != null) {
                        if (data.left === 0 && data.right === 0) {
                            _this.computedQueryFun();
                        } else {
                            _this.content = data;
                            _this.computedChildQueryFun();
                            setTimeout(() => {
                                _this.over = true;
                            }, 300);
                        }
                    }
                })
                .exec();
        },

        computedChildQueryFun() {
            let _this = this;
            let { tab, uid_var: _uid, tabNodeList, curValue } = this;
            for (let i = 0; i < tab.length; i++) {
                let item = '#ui-tab-item-' + _uid + '-' + i;
                uni.createSelectorQuery()
                    .in(this)
                    .select(item)
                    .boundingClientRect((data) => {
                        if (data != null) {
                            _this.$set(tabNodeList, i, data);
                            if (i === curValue) {
                                _this.computedMarkFun();
                            }
                        }
                    })
                    .exec();
            }
        },

        setCurValueFun(value) {
            let curValue = this.curValue;
            if (value === curValue) {
                return false;
            } else {
                this.curValue = value;
                this.computedMarkFun();
            }
        },

        clickFun(e) {
            let { index, item } = e.currentTarget.dataset;
            this.setCurValueFun(index);
            this.$emit('input', {
                detail: index
            });
            this.$emit('change', {
                detail: {
                    index: index,
                    data: item
                }
            });
        },

        computedMarkFun() {
            let { tabNodeList, curValue, content } = this;
            if (tabNodeList.length === 0) {
                return;
            }
            let list = tabNodeList;
            let cur = curValue;
            let markLeft = list[cur].left - content.left;
            let markWidth = list[cur].width;
            this.markLeft = markLeft;
            this.markWidth = markWidth;
        },

        computedScrollFun() {
            let { tabNodeList, curValue } = this;
            if (curValue === 0 || curValue === tabNodeList.length - 1) {
                return false;
            } else {
                let i = 0;
                let left = 0;
                let list = tabNodeList;
                for (i in list) {
                    if (i === curValue && i !== 0) {
                        left = left - list[i - 1].width;
                        break;
                    }
                    left = left + list[i].width;
                }
                this.scrollLeft = left;
            }
        }
    },

    created: function () {}
};
</script>
<style>
.ui-tab {
    display: flex;
    height: 4em;
}

.ui-tab,
.ui-tab .ui-tab-item {
    align-items: center;
    position: relative;
}

.ui-tab .ui-tab-item {
    display: inline-flex;
    line-height: 1.5em;
    min-height: 1.5em;
    opacity: 0.6;
    padding: 0 1em;
    transition: opacity 0.3s;
    z-index: 1;
}

.ui-tab .ui-tab-item .ui-tab-icon {
    font-size: 120%;
    margin: 0 0.25em;
}

.ui-tab .ui-tab-item.cur {
    opacity: 1;
}

.ui-tab .ui-tab-item.btn .ui-tab-text {
    transform: scale(0.9);
    transition: color 0.3s;
}

.ui-tab.ui-tab-scrolls {
    width: 100%;
}

.ui-tab.ui-tab-scrolls .ui-tab-scroll-warp {
    height: inherit;
    overflow: hidden;
    width: 100%;
}

.ui-tab.ui-tab-scrolls .ui-tab-scroll-warp .ui-tab-scroll {
    display: block;
    height: calc(4em + 17px);
    line-height: 4em;
    overflow: auto;
    padding-bottom: 17px;
    /* position: sticky; */
    top: 150rpx;
    white-space: nowrap;
    width: 100% !important;
}

.ui-tab.ui-tab-scrolls .ui-tab-scroll-warp .ui-tab-scroll .ui-tab-mark-warp {
    height: 4em;
}

.ui-tab .ui-tab-mark-warp {
    color: inherit;
    height: 100%;
    position: absolute;
    top: 0;
    z-index: 0;
}

.ui-tab .ui-tab-mark-warp.over {
    transition: 0.3s;
}

.ui-tab .ui-tab-mark-warp .ui-tab-mark {
    height: 100%;
}

.ui-tab .ui-tab-mark-warp .ui-tab-mark.ui-btn .text {
    opacity: 0;
}

.ui-tab.line .ui-tab-mark {
    border-bottom: 2px solid;
}

.ui-tab.topline .ui-tab-mark {
    border-top: 2px solid;
}

.ui-tab.dot .ui-tab-mark::after {
    border-radius: 50%;
    bottom: 0.3em;
    height: 0.5em;
    width: 0.5em;
}

.ui-tab.dot .ui-tab-mark::after,
.ui-tab.long .ui-tab-mark::after {
    background-color: currentColor;
    bottom: 0.5em;
    content: '';
    display: block;
    left: 0;
    margin: auto;
    position: absolute;
    right: 0;
}

.ui-tab.long .ui-tab-mark::after {
    border-radius: 5em;
    height: 0.35em;
    width: 2em;
}

.ui-tab.trapezoid .ui-tab-mark::after {
    background-color: currentColor;
    border-radius: 5em 5em 0 0;
    bottom: 0;
    content: '';
    display: block;
    height: 0.35em;
    left: 0;
    margin: auto;
    position: absolute;
    right: 0;
    width: calc(100% - 2em);
}

.ui-tab.btn .ui-tab-mark-warp {
    align-items: center;
    display: flex;
    justify-content: center;
}

.ui-tab.btn .ui-tab-mark-warp .ui-tab-mark.ui-btn {
    height: calc(100% - 1.6em);
    width: calc(100% - 0.6em);
}

.ui-tab.btn.sm .ui-tab-mark.ui-btn {
    border-radius: 8rpx;
    height: calc(100% - 2px);
    width: calc(100% - 2px);
}

.ui-tab.ui-tab-inline {
    display: inline-flex;
    height: 3.5em;
}

.ui-tab.ui-tab-inline.ui-tab-scrolls .ui-tab-scroll {
    height: calc(3.5em + 17px);
    line-height: 3.5em;
}

.ui-tab.ui-tab-inline.ui-tab-scrolls .ui-tab-scroll .ui-tab-mark-warp {
    height: 3.5em;
}

.ui-tab.ui-tab-inline.btn .ui-tab-mark-warp .ui-tab-mark.ui-btn {
    height: calc(100% - 5px);
    width: calc(100% - 5px);
}

.ui-tab.sm {
    height: 70rpx !important;
}

.ui-tab.sm.ui-tab-inline {
    height: 70rpx;
}

.ui-tab.sm.ui-tab-inline.ui-tab-scrolls .ui-tab-scroll {
    height: calc(70rpx + 17px);
    line-height: 70rpx;
}

.ui-tab.sm.ui-tab-inline.ui-tab-scrolls .ui-tab-scroll .ui-tab-mark-warp {
    height: 70rpx;
}

.ui-tab.sm.ui-tab-inline.btn .ui-tab-mark.ui-btn {
    border-radius: 8rpx;
    height: calc(100% - 2px);
    width: calc(100% - 2px);
}
.ui-tag {
    align-items: center;
    border-radius: 10rpx;
    box-sizing: border-box;
    display: inline-flex;
    font-size: calc(28rpx + var(--textSize));
    justify-content: center;
    line-height: normal;
    margin: 0 0.14286em;
    padding: 0.5833em 0.833em;
    position: relative;
    vertical-align: middle;
    white-space: nowrap;
}

.ui-tag.xs {
    margin: 0;
    padding: 0.2em 0.5em;
    transform: scale(0.7);
}

.ui-tag.sm {
    margin: 0 0.02em 0 0;
    transform: scale(0.8);
}

.ui-tag.lg {
    font-size: calc(32rpx + var(--textSize)) !important;
}

.ui-tag.xl {
    font-size: calc(36rpx + var(--textSize)) !important;
}

.ui-tag.xxl {
    font-size: calc(44rpx + var(--textSize)) !important;
}

.ui-tag.img {
    line-height: 1;
    padding: 0.5833em 0.833em 0.5833em 0.5em;
}

.ui-tag.img .tag-img {
    border-radius: 10rpx 0 0 10rpx;
    height: 2.5666em;
    margin: -0.5833em 0.5em -0.5833em -0.5em;
    width: 2.5666em;
}

.ui-tag.badge {
    border-radius: 200rpx;
    font-size: 2em;
    line-height: 1;
    margin: 0;
    padding: 0.4em 0.6em 0.3em;
    position: absolute;
    right: 0;
    top: 0;
    transform: scale(0.3) translateX(0.5em) translatey(-0.6em);
    transform-origin: 100% 0;
    z-index: 1;
}

.ui-tag.badge.badge-br {
    bottom: 0;
    right: 0;
    top: auto;
    transform: scale(0.3) translateX(0.5em) translatey(0.4em);
    transform-origin: 100% 100%;
}

.ui-tag.badge.badge-bl {
    bottom: 0;
    left: 0;
    right: auto;
    top: auto;
    transform: scale(0.3) translateX(-0.5em) translatey(0.4em);
    transform-origin: 0 100%;
}

.ui-tag.badge.badge-tl {
    left: 0;
    right: auto;
    transform: scale(0.3) translateX(-0.5em) translatey(-0.6em);
    transform-origin: 0 0;
}

.ui-tag.badge:not([class*='bg-']) {
    background-color: #e54d42 !important;
    color: #fff;
}

.ui-tag.empty {
    height: 40px;
    padding: 0;
    width: 40px;
}

.ui-tag.icon {
    padding: 0.4em;
}
</style>
