var atoba = require('./atob.js');
//GET请求
function GET(url, requestHandler) {
    request(url, 'GET', requestHandler);
}
//POST请求
function POST(url, requestHandler) {
    request(url, 'POST', requestHandler);
}
function request(url, method, requestHandler) {
    var that = this;
    var app = getApp();
    var e = app.globalData.getCache('userinfo');
    //注意：可以对params加密等处理
    var params = requestHandler.params || {};
    params.account_info = uni.getAccountInfoSync().miniProgram.envVersion;
    params.version = app.globalData.version;
    params.much_id = app.globalData.siteInfo.uniacid;
    params.token = !e ? '' : e.token;
    params.user_open_type = 0;
    uni.request({
        url: url,
        data: params,
        method: method,
        // OPTIONS, GET, HEAD, POST, PUT, DELETE, TRACE, CONNECT
        header: {
            'content-type': 'application/x-www-form-urlencoded'
        },
        success: (res)=> {
            //console.log(JSON.parse(decodeURIComponent(atoba.weAtob(""))));
            if (res.data instanceof Object) {
                requestHandler.success && typeof requestHandler.success == 'function' && requestHandler.success(res);
            } else {
                //注意：可以对参数解密等处理
                if (app.globalData.compareVersion(app.globalData.version, '1.1.39') == -1) {
                    requestHandler.success && typeof requestHandler.success == 'function' && requestHandler.success(res);
                } else {
                    var d = JSON.parse(decodeURIComponent(atoba.weAtob(res.data)));
                    res.data = d;
                    if (d.status == 'feng') {
                        uni.redirectTo({
                            url: '/yl_welore/pages/black_house/index?forbid_prompt=' + d.user.forbid_prompt
                        });
                        return;
                    }
                    requestHandler.success && typeof requestHandler.success == 'function' && requestHandler.success(res);
                }
            }
        },
        fail: ()=> {
            //requestHandler.fail()
        },
        complete: ()=> {
            // complete
        }
    });
}
module.exports = {
    GET: GET,
    POST: POST
};
