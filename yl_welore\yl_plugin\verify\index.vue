<template><view></view></template>

<script>
const app = getApp();
App.Component({
    /**
     * 组件的一些选项
     */
    options: {
        addGlobalClass: false,
        //组件样式隔离
        multipleSlots: true
    },
    lifetimes: {
        // 生命周期函数，可以为函数，或一个在methods段中定义的方法名
        attached: function () {}
    },
    /**
     * 组件的对外属性
     */
    properties: {
        Type: {
            type: String,
            default: ''
        }
    },
    /**
     * 组件的初始数据
     */
    data: {
        http_root: app.globalData.http_root
    },
    /**
     * 组件的方法列表
     */
    methods: {}
});
</script>
<style>
/* colorui/components/cu-custom.wxss */
</style>
