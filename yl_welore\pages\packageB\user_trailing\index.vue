<template>
    <view class="page-container">
        <cu-custom bgColor="none" :isBack="true" :isSearch="false">
            <view slot="backText">返回</view>
            <view slot="content" class="header-title">🏠 {{ title }}</view>
        </cu-custom>

        <view class="section-title">
            {{ uid != id ? '👤 他/她的' : '🌟 我的' }}{{ title }}
        </view>

        <view class="content-container">
            <!-- 圈子网格 -->
            <view class="grid col-3 margin-bottom text-center" v-if="info.length > 0">
                <view class="grid-item" v-for="(item, index) in info" :key="index">
                    <view @click="openUrl('/yl_welore/pages/packageA/circle_info/index?id=' + item.id)"
                        class="card-container">
                        <view class="image-container">
                            <image :src="item.realm_icon" class="card-image" mode="aspectFill" @error="onImageError">
                            </image>
                            <view class="image-overlay"></view>
                        </view>
                        <view class="card-content">
                            <text class="card-title">{{ item.realm_name }}</text>
                        </view>
                    </view>
                </view>
            </view>

            <!-- 空状态 -->
            <view class="empty-state" v-if="info.length == 0 && info_dsg">
                <view class="empty-icon">🏠</view>
                <view class="empty-title">暂无加入的圈子</view>
                <view class="empty-desc">快去探索更多有趣的圈子吧～</view>
            </view>
        </view>
    </view>
</template>

<script>
import http from '../../../util/http.js';
const app = getApp();
export default {
    /**
     * 页面的初始数据
     */
    data() {
        return {
            title: '',
            //导航栏 中间的标题
            page: 1,
            info: [],
            id: null,
            uid: null,
            info_dsg: false
        };
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        var e = app.globalData.getCache('userinfo');
        this.id = options.id;
        this.uid = e.uid;
        this.title = '加入的' + getApp().globalData.store.getState().diy['landgrave'];
    },
    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {
        this.page = 1;
        this.info = [];
        this.get_my_trailing();
    },
    /**
     * 加载下一页
     */
    onReachBottom() {
        this.page = this.page + 1;
        this.get_my_trailing();
    },
    /**
     * 用户点击右上角分享
     */
    onShareAppMessage() {
        var forward = app.globalData.forward;
        console.log(forward);
        if (forward) {
            return {
                title: forward.title,
                path: '/yl_welore/pages/index/index',
                imageUrl: forward.reis_img
            };
        } else {
            return {
                title: '您的好友给您发了一条信息',
                path: '/yl_welore/pages/index/index'
            };
        }
    },
    methods: {
        openUrl(url){
            uni.navigateTo({
                url:url
            })
        },
        /**
         * 图片加载错误处理
         */
        onImageError(e) {
            console.log('图片加载失败:', e);
            // 可以设置默认图片或其他处理逻辑
        },
        /**
         * 我加入的圈子
         */
        get_my_trailing() {
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.uid = this.id;
            params.get_id = -1;
            params.page = that.page;
            var b = app.globalData.api_root + 'User/get_right_needle';
            var allMsg = that.info;
            http.POST(b, {
                params: params,
                success: function (res) {
                    console.log(res);
                    if (res.data.status == 'success') {
                        if (res.data.info.length == 0) {
                            that.info_dsg = true;
                            return;
                        }
                        for (var i = 0; i < res.data.info.length; i++) {
                            allMsg.push(res.data.info[i]);
                        }
                        that.info = allMsg;
                    } else {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                },
                fail: function () {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) { }
                    });
                }
            });
        }
    }
};
</script>
<style>
page {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
}

.page-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 头部标题样式 */
.header-title {
    color: #2c2b2b;
    font-weight: 600;
    font-size: 36rpx;
    display: flex;
    align-items: center;
    gap: 8rpx;
}

/* 章节标题样式 */
.section-title {
    margin: 32rpx 52rpx 40rpx;
    font-weight: 700;
    font-size: 32rpx;
    color: #333;
    display: flex;
    align-items: center;
    gap: 12rpx;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 内容容器 */
.content-container {
    background: rgba(255, 255, 255, 0.9);
    margin: 0 24rpx;
    border-radius: 24rpx;
    padding: 32rpx;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

/* 网格项目 */
.grid-item {
    padding: 16rpx;
}

/* 卡片容器 */
.card-container {
    background: #fff;
    border-radius: 20rpx;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.card-container:hover {
    transform: translateY(-8rpx);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

/* 图片容器 */
.image-container {
    position: relative;
    width: 100%;
    height: 190rpx;
    overflow: hidden;
}

.card-image {
    width: 100%;
    height: 100%;
    transition: transform 0.3s ease;
}

.card-container:hover .card-image {
    transform: scale(1.05);
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(180deg, transparent 0%, rgba(0, 0, 0, 0.1) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.card-container:hover .image-overlay {
    opacity: 1;
}

/* 卡片内容 */
.card-content {
    padding: 24rpx 20rpx;
    text-align: center;
}

.card-title {
    color: #333;
    font-size: 24rpx;
    font-weight: 500;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 120rpx 40rpx;
    color: #666;
}

.empty-icon {
    font-size: 120rpx;
    margin-bottom: 32rpx;
    opacity: 0.8;
}

.empty-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 16rpx;
}

.empty-desc {
    font-size: 28rpx;
    color: #999;
    line-height: 1.5;
}

/* 加载状态样式 */
.loading-container {
    text-align: center;
    padding: 40rpx;
}

.loading-text {
    color: #666;
    font-size: 28rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12rpx;
}

/* 响应式优化 */
@media (max-width: 750rpx) {
    .content-container {
        margin: 0 16rpx;
        padding: 24rpx;
        border-radius: 16rpx;
    }

    .section-title {
        margin: 24rpx 32rpx 32rpx;
        font-size: 28rpx;
    }

    .grid-item {
        padding: 12rpx;
    }
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30rpx);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.card-container {
    animation: fadeInUp 0.6s ease forwards;
}

.grid-item:nth-child(1) .card-container {
    animation-delay: 0.1s;
}

.grid-item:nth-child(2) .card-container {
    animation-delay: 0.2s;
}

.grid-item:nth-child(3) .card-container {
    animation-delay: 0.3s;
}

.grid-item:nth-child(4) .card-container {
    animation-delay: 0.4s;
}
</style>
