<template>
    <view>
        <cu-custom bgColor="bg-white" :isSearch="false" :isBack="true">
            <view slot="backText"></view>
            <view slot="content" style="color: #000000; font-weight: 600; font-size: 36rpx">卡密兑换</view>
        </cu-custom>
        <scroll-view scroll-x class="bg-white nav">
            <view class="flex text-center">
                <view :class="'cu-item flex-sub ' + (0 == TabCur ? 'text-blue cur' : '')" @tap="tabSelect" data-key="0">激活卡密</view>
                <view :class="'cu-item flex-sub ' + (1 == TabCur ? 'text-blue cur' : '')" @tap="tabSelect" data-key="1">兑换记录</view>
            </view>
        </scroll-view>

        <view v-if="TabCur == 0" style="margin: 30rpx; padding: 30rpx; background-color: #ffffff; border-radius: 5px; text-align: center">
            <view class="cu-bar bg-white">
                <view class="action">
                    <text class="cuIcon-titles text-blue"></text>
                    <text class="text-xl text-bold">卡密激活</text>
                </view>
            </view>
            <view class="cu-form-group margin-top" style="border: 1px solid #9999; margin: 20rpx; border-radius: 20rpx">
                <input @input="in_input" :value="card_code" style="text-align: center" type="text" maxlength="50" placeholder="输入卡密" />
            </view>
            <button @tap="in_submit" class="cu-btn margin-top lg bg-blue round" style="width: 75%">兑换</button>
        </view>
        <view v-if="TabCur == 0" class="text-gray" style="margin: 15px; padding: 15px">
            <text class="cuIcon-question lg"></text>
            <text style="margin-left: 10rpx">目前仅支持系统内部卡密兑换</text>
        </view>
        <view v-if="TabCur == 1" class="cu-list menu sm-border card-menu margin-top">
            <view class="cu-item" v-for="(item, index) in list" :key="index">
                <view class="content padding-tb-sm">
                    <view style="word-break: break-all; width: 85%">
                        {{ item.mu_id }}
                    </view>
                    <view class="text-gray text-sm">
                        <text>激活时间：{{ item.use_time }}</text>
                    </view>
                </view>

                <view class="action">
                    {{ item.financial_type }}
                </view>
            </view>
            <view :class="'cu-load ' + (!di_msg ? 'loading' : 'over')"></view>
        </view>
    </view>
</template>

<script>
const app = getApp();
var http = require('../../../util/http.js');
export default {
    /**
     * 页面的初始数据
     */
    data() {
        return {
            TabCur: 0,
            card_code: '',
            page: 1,
            list: [],
            di_msg: false
        }
    },
    methods: {
        in_submit() {
            var b = app.globalData.api_root + 'Attest/activation_card';
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.card_code = this.card_code;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.code == 1) {
                        uni.showModal({
                            title: '提示',
                            content: res.data.msg,
                            showCancel: false,
                            success: (res) => {}
                        });
                    } else {
                        uni.showModal({
                            title: '提示',
                            content: res.data.msg,
                            showCancel: false,
                            success: (res) => {}
                        });
                    }
                    that.card_code = '';
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => {}
                    });
                }
            });
        },
        in_input(d) {
            var card_code = d.detail.value;
            this.card_code = card_code;
        },
        tabSelect(d) {
            var key = d.currentTarget.dataset.key;
            this.TabCur = key;
            this.page = 1;
            this.list = [];
            if (key == 1) {
                this.get_avc_list();
            }
        },
        get_avc_list() {
            var b = app.globalData.api_root + 'Attest/activation_log';
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.page = this.page;
            var allMsg = that.list;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    for (var i = 0; i < res.data.length; i++) {
                        allMsg.push(res.data[i]);
                    }
                    that.list = allMsg;
                    if (res.data.length == 0 || allMsg.length < 10) {
                        that.di_msg = true;
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => {}
                    });
                }
            });
        }
    },
    /**
     * 加载下一页
     */
    onReachBottom() {
        if (this.TabCur == 1) {
            this.page = this.page + 1;
            this.get_avc_list();
        }
    }
};
</script>
<style>
/* yl_welore/pages/packageF/cammy/index.wxss */
</style>
