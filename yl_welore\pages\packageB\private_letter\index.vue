<template>
    <view>
        <cu-custom bgColor="bg-white" :isSearch="false" :isBack="true">
            <view slot="backText">返回</view>
            <view slot="content" style="color: #2c2b2b; font-weight: 600; font-size: 36rpx">{{ title }}</view>
        </cu-custom>
        <scroll-view
            :enhanced="true"
            :scroll-y="true"
            @scroll="end_tire"
            lower-threshold="50"
            :enable-flex="true"
            class="cu-chat"
            :scroll-into-view="toView"
            style="height: 1320rpx"
            id="scroll"
        >
            <view class="cu-info round">💬 现在可以开始聊天了!</view>
            <block v-for="(item, index) in list" :key="index">
                <view v-if="item.se_user_id == uid" class="cu-item self" :id="'msg-' + index">
                    <block v-if="item.le_type == 0">
                        <view class="main">
                            <view class="content_right shadow">
                                <!-- <text user-select="true">{{item.le_content}}</text> -->
                                <rich-text style="word-break: break-word" :nodes="item.le_content"></rich-text>
                            </view>
                        </view>
                        <view class="cu-avatar round" :style="'background-image:url(' + item.se_user_head + ');'"></view>
                    </block>
                    <block v-if="item.le_type == 1">
                        <view class="main" @tap="open_url" :data-id="index">
                            <view class="content_right_article shadow">
                                <text class="cicon-link" style="margin-right: 10rpx"></text>
                                <text :user-select="true">{{ item.le_content.title }}</text>
                            </view>
                        </view>
                        <view class="cu-avatar round" :style="'background-image:url(' + item.se_user_head + ');'"></view>
                    </block>
                    <view class="date">⏰ {{ item.le_time }}</view>
                </view>

                <view v-if="item.se_user_id == id" class="cu-item" :id="'msg-' + index">
                    <view class="cu-avatar round" :style="'background-image:url(' + item.re_user_head + ');'"></view>
                    <block v-if="item.le_type == 0">
                        <view class="main">
                            <view class="content_left shadow">
                                <rich-text style="word-break: break-word" :nodes="item.le_content"></rich-text>
                            </view>
                        </view>
                    </block>
                    <block v-if="item.le_type == 1">
                        <view class="main" @tap="open_url" :data-id="index">
                            <view class="content_right_article shadow">
                                <text class="cicon-link" style="margin-right: 10rpx"></text>
                                <text :user-select="true">{{ item.le_content.title }}</text>
                            </view>
                        </view>
                    </block>
                    <view class="date">⏰ {{ item.le_time }}</view>
                </view>
            </block>
            <view v-if="my_message != ''" class="cu-item self" id="msg-message">
                <block v-if="my_message.le_type == 0">
                    <view class="main">
                        <view class="content_right shadow">
                            <text :user-select="true">{{ my_message.le_content }}</text>
                        </view>
                    </view>
                </block>
                <block v-if="my_message.le_type == 1">
                    <view class="main">
                        <view class="content_right_article shadow">
                            <text class="cicon-link" style="margin-right: 10rpx"></text>
                            <text :user-select="true">{{ my_message.le_content.title }}</text>
                        </view>
                    </view>
                </block>
                <view class="cu-avatar round" :style="'background-image:url(' + my_message.se_user_head + ');'"></view>
                <view class="date">⏰ {{ my_message.le_time }}</view>
            </view>
            <view :style="'padding-top: ' + bottom + 'px;'"></view>
            <view id="dibu"></view>
        </scroll-view>

        <view class="cu-bar foot input cur" :style="'bottom:' + bottom + 'px;box-shadow:none;padding-left: 10px;'">
            <view class="input-container">
                <textarea
                    @keyboardheightchange="abc"
                    :value="get_text"
                    :auto-height="true"
                    @tap="InputFocus"
                    @input="get_reply_text"
                    :adjust-position="false"
                    :focus="value_false"
                    :show-confirm-bar="false"
                    maxlength="300"
                    cursor-spacing="0"
                    style="width: 100%"
                    placeholder="开始聊天吧..."
                ></textarea>
            </view>
            <view class="action emoji-btn" @tap="openPhEmoji">
                <text class="cicon-emoji-o text-black" style="font-size: 25px"></text>
            </view>
            <button @tap="add_hui" class="cu-btn icon send-btn">
                <text class="cicon-near-me-o text-white" style="font-size: 50rpx"></text>
            </button>
        </view>
        <view @tap="get_bottom" class="scroll-bottom-btn">
            <button class="cu-btn icon bg-whit scroll-btn">
                <text class="cuIcon-unfold" style="font-size: 50rpx; color: #548cfd"></text>
            </button>
        </view>

        <view v-if="emoji" class="emoji-panel">
            <swiper :indicator-dots="true" style="height: 400rpx">
                <block v-for="(emojis, t_index) in emj_list" :key="t_index">
                    <swiper-item>
                        <view class="grid col-9 margin-bottom text-center" style="padding-top: 10px">
                            <view @tap="set_emoji" :data-key="t_index" :data-index="n_index" class="emoji-item" v-for="(n_item, n_index) in emojis" :key="n_index">
                                <image :src="http_root + 'addons/yl_welore/web/static/expression/' + n_item" style="width: 60rpx; height: 60rpx"></image>
                            </view>
                        </view>
                    </swiper-item>
                </block>
            </swiper>
        </view>

        <view v-if="!modalName && article" class="right_icon" @tap="show_right_at">
            <text class="cicon-first-page"></text>
        </view>
        <view v-if="article" :class="'cu-modal drawer-modal justify-end ' + (modalName ? 'show' : '')" @tap="hideModal" style="background-color: transparent">
            <view @tap.stop.prevent="ins_article" class="cu-dialog basis-df right_at" style="height: 250rpx">
                <view class="text_num text_lin" style="padding: 40rpx 20rpx 40rpx 40rpx; font-size: 16px">
                    <text class="cicon-link"></text>
                    <text>{{ article.study_title }}</text>
                </view>
                <!-- <view class="text_num" style="padding: 0px 5px 10px 10px;">
      <text>{{article.chun_text}}</text>
    </view> -->
                <view class="text-blue" style="padding: 5px 5px 10px 20px">
                    <text class="cicon-first-page" style="font-size: 40rpx; vertical-align: middle"></text>
                    <text style="vertical-align: middle">发到聊天</text>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
var app = getApp();
var http = require('../../../util/http.js');
export default {
    data() {
        return {
            http_root: app.globalData.http_root,
            get_text: '',
            fang: false,
            list: [],
            del_mod: false,
            title: '',
            top_mod: false,
            lahei_mod: false,
            jubao_mod: false,
            animBack: {},
            isPopping: false,

            //是否已经弹出
            jubao_text: '',

            sc_height: 500,
            inter: '',

            //循环定时器
            my_message: {
                le_type: 0,

                le_content: {
                    title: ''
                },

                se_user_head: '',
                le_time: ''
            },

            //我发送的数据
            end_tire_show: true,

            //true 可以滚动 //false 不能滚动
            modalName: false,

            article: {
                study_title: ''
            },

            emj_list: [],
            emoji: false,
            value_false: false,
            bottom: 15,
            height: '',
            isIpx: '',
            id: '',
            uid: '',
            del_id: 0,
            toView: '',
            InputBottom: 0,
            t_index: 0,
            n_index: 0,
            emojis: [],
            n_item: ''
        };
    },
    onUnload() {
        this.endInter();
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        this.startInter();
        const e = app.globalData.getCache('userinfo');
        if (!e) {
            uni.login({
                success: (res) => {
                    var params = new Object();
                    params.code = res.code;
                    http.POST(
                        app.globalData.api_root + 'Login/index',
                        {
                            params: params,
                            success: (open) => {
                                var data = new Object();
                                data.openid = open.data.info.openid;
                                data.session_key = open.data.info.session_key;
                                http.POST(
                                    app.globalData.api_root + 'Login/add_tourist',
                                    {
                                        params: data,
                                        success: (d) => {
                                            app.globalData.setCache('userinfo', d.data.info);
                                            this.height = app.globalData.height;
                                            this.isIpx = app.globalData.isIpx;
                                            this.id = options.id;
                                            this.uid = d.data.info.uid;
                                            this.del_id = 0;
                                            this.get_private_list(0);
                                            this.getExpEmjList();
                                        }
                                    }
                                );
                            }
                        }
                    );
                }
            });
        } else {
            const article = app.globalData.getCache('article');
            console.log(article);
            this.article = article;
            this.height = app.globalData.height;
            this.isIpx = app.globalData.isIpx;
            this.id = options.id;
            this.uid = e.uid;
            this.del_id = 0;
            this.get_private_list(0);
            this.getExpEmjList();
        }
    },
    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {
        const subscribe = app.globalData.getCache('subscribe');
        if (!subscribe) {
            app.globalData.subscribe_message(
                (res) => {
                    //请求成功的回调函数
                    console.log(res);
                    if (res == '') {
                        return;
                    }
                    app.globalData.setCache('subscribe', res.parallelism_data);
                },
                () => {
                    //请求失败的回调函数，不需要时可省略
                }
            );
        }
    },
    /**
     * 下拉刷新
     */
    onPullDownRefresh() {
        setTimeout(() => {
            uni.hideNavigationBarLoading(); //完成停止加载
            uni.stopPullDownRefresh(); //停止下拉刷新
        }, 1500);
        this.list = [];
        this.get_private_list();
    },
    methods: {
        abc(d) {
            var height = d.detail.height;
            if (height > 0) {
                this.bottom = height;
            }
            if (!this.emoji && height == 0) {
                this.bottom = 15;
            }
        },

        openPhEmoji() {
            this.emoji = !this.emoji;
            this.value_false = false;

            if (this.emoji) {
                this.bottom = 225;
            } else {
                this.bottom = 15;
            }
            uni.createSelectorQuery()
                .in(uni)
                .select('#scroll')
                .boundingClientRect((rect) => {
                    // 使页面滚动到底部
                    uni.pageScrollTo({
                        scrollTop: rect.height,
                        duration: 0
                    });
                })
                .exec();
        },

        set_emoji(d) {
            var index = d.currentTarget.dataset.index;
            var t_index = d.currentTarget.dataset.key;
            var str = this.emj_list[t_index][index];
            var k = str.split('.')[0];
            console.log(k);
            this.get_text = this.get_text + '[#:' + k + ']';
        },

        open_url(d) {
            var index = d.currentTarget.dataset.id;
            var info = this.list[index].le_content;
            uni.navigateTo({
                url: '/yl_welore/pages/packageA/article/index?id=' + info.paid + '&type=' + info.type
            });
        },

        ins_article() {
            var b = app.globalData.api_root + 'Conversation/ins_article';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.re_user_id = this.id;
            params.study_title = this.article.study_title;
            params.paper_id = this.article.id;
            params.paper_type = this.article.study_type;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.status == 'error') {
                        uni.showModal({
                            title: '提示',
                            content: res.data.msg,
                            showCancel: false,
                            success: (res) => {}
                        });
                    } else {
                        this.my_message = res.data.info;
                        this.toView = 'msg-message';
                        this.modalName = false;
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => {}
                    });
                }
            });
        },

        get_bottom() {
            var list = this.list;
            this.toView = 'msg-' + (list.length - 1);
        },

        end_tire(d) {
            var index = this.list.length - 1;
            var query = uni.createSelectorQuery(); //创建节点查询器 query
            query.select('#msg-' + index).boundingClientRect(); //选择Id=id的节点，获取节点位置信息的查询请求
            query.exec((res) => {
                var bottom = res[0].bottom;
                if (bottom > 1000) {
                    this.end_tire_show = false;
                } else {
                    this.end_tire_show = true;
                }
            });
        },

        /**
         * 启动定时器
         */
        startInter() {
            this.inter = setInterval(() => {
                this.get_new_message();
            }, 3000);
        },

        /**
         * 获取最新一条数据
         */
        get_new_message() {
            var last = this.list[this.list.length - 1];
            if (typeof last == 'undefined') {
                //this.get_private_list(0);
                return;
            }
            var b = app.globalData.api_root + 'Conversation/get_new_message';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.last_id = last.id;
            params.id = this.id;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    console.log('更新');
                    var list = this.list;
                    list.push(...res.data);
                    this.list = list;
                    this.my_message = '';
                    if (this.end_tire_show) {
                        this.toView = 'msg-' + (list.length - 1);
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => {}
                    });
                }
            });
        },

        /**
         * 结束定时器
         */
        endInter() {
            clearInterval(this.inter);
        },

        refresh() {
            this.get_text = '';
            this.list = [];
            this.get_private_list(0);
        },

        getExpEmjList() {
            var b = app.globalData.api_root + 'Polls/get_emj_list';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            http.POST(b, {
                params: params,
                success: (res) => {
                    this.emj_list = res.data;
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => {}
                    });
                }
            });
        },

        show_right_at() {
            this.modalName = true;
        },

        //隐藏
        hideModal() {
            this.modalName = false;
        },

        /**
         * 获取留言内容
         */
        get_reply_text(c) {
            this.get_text = c.detail.value;
            this.fang = false;
        },

        /**
         * add留言内容
         */
        add_hui() {
            if (this.fang == true) {
                return;
            }
            if (this.get_text == '') {
                return;
            }
            var b = app.globalData.api_root + 'User/add_private';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.get_text = this.get_text;
            params.re_user_id = this.id;
            this.get_text = '';
            this.emoji = false;
            this.bottom = 15;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.status == 'success') {
                        var subscribe = app.globalData.getCache('subscribe');
                        if (subscribe && subscribe['YL0006'] && subscribe['YL0009'] && subscribe['YL0004']) {
                            app.globalData.authorization(subscribe['YL0006'], subscribe['YL0009'], subscribe['YL0004'], (res) => {});
                        }
                        //var list = this.list;
                        //list.push();
                        if (this.list.length == 0) {
                            this.get_text = '';
                            this.get_private_list(0);
                        } else {
                            this.get_text = '';
                            this.my_message = res.data.info;
                            this.toView = 'msg-message';
                        }

                        // this.startInter();
                    } else {
                        // wx.hideLoading();
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => {}
                    });
                }
            });
        },

        /**
         * 列表
         */
        get_private_list(type) {
            this.top_mod = true;
            var b = app.globalData.api_root + 'User/get_private_list';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.re_user_id = this.id;
            params.top_id = this.top_id;
            console.log(params.top_id);
            var allMsg = this.list;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.status == 'success') {
                        if (res.data.info.length != 0) {
                            for (var i = 0; i < res.data.info.length; i++) {
                                allMsg.unshift(res.data.info[i]);
                            }
                            this.list = allMsg;
                            this.title = res.data.user.user_nick_name;
                            this.top_mod = false;
                        }
                        this.title = res.data.user.user_nick_name;
                        this.top_mod = false;
                        if (type == 0) {
                            this.toView = 'msg-' + (this.list.length - 1);
                        }
                        uni.hideLoading();
                        //this.pageScrollToBottom();
                    } else {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                        uni.hideLoading();
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => {}
                    });
                }
            });
        },

        InputFocus(e) {
            this.value_false = true;
            this.emoji = false;
        },

        InputBlur(e) {
            if (getApp().globalData.store.getState().isIphoneX) {
                this.InputBottom = 15;
            } else {
                this.InputBottom = 0;
            }
        }
    }
};
</script>
<style>
page {
    background-color: #ffffff;
}

.content_left {
    padding: 20rpx;
    display: inline-flex;
    max-width: 100%;
    align-items: center;
    font-size: 30rpx;
    position: relative;
    min-height: 80rpx;
    line-height: 40rpx;
    text-align: left;
    border-radius: 25rpx;
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
    color: #ffffff;
    letter-spacing: 0.5px;
    font-weight: 300;
    box-shadow: 0 4rpx 15rpx rgba(255, 154, 158, 0.3);
    border: 1rpx solid rgba(255, 255, 255, 0.2);
}
.content_right_article {
    padding: 20rpx;
    display: inline-flex;
    max-width: 100%;
    align-items: center;
    font-size: 30rpx;
    position: relative;
    min-height: 80rpx;
    line-height: 40rpx;
    text-align: left;
    border-radius: 25rpx;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    letter-spacing: 0.5px;
    font-weight: 300;
    box-shadow: 0 4rpx 15rpx rgba(195, 207, 226, 0.3);
    border: 1rpx solid rgba(255, 255, 255, 0.5);
}
.content_right {
    padding: 20rpx;
    display: inline-flex;
    max-width: 100%;
    align-items: center;
    font-size: 30rpx;
    position: relative;
    min-height: 80rpx;
    line-height: 40rpx;
    text-align: left;
    border-radius: 25rpx;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #ffffff;
    letter-spacing: 0.5px;
    font-weight: 300;
    box-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.3);
    border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.right_at {
    top: 30%;
    border-top-left-radius: 10px !important;
    border-bottom-left-radius: 10px !important;
    background-color: #f2f2f2;
    text-align: left !important;
}

.right_icon {
    position: fixed;
    top: 36%;
    right: 0px;
    padding: 5px 8px 5px 8px;
    z-index: 2000;
    font-size: 30px;
    background-color: #f2f2f2;
    border-top-left-radius: 10px !important;
    border-bottom-left-radius: 10px !important;
}

/* 新增样式 */
.input-container {
    background: linear-gradient(135deg, #e4e6f1 0%, #f8f9fc 100%);
    border-radius: 30px;
    padding: 10px;
    width: 75%;
    box-shadow: inset 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
    border: 1rpx solid rgba(255, 255, 255, 0.5);
}

.emoji-btn {
    margin-left: 8rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8rpx;
    border-radius: 50%;
    background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
    box-shadow: 0 2rpx 8rpx rgba(255, 171, 160, 0.3);
    transition: all 0.3s ease;
}

.emoji-btn:active {
    transform: scale(0.95);
}

.send-btn {
    background: linear-gradient(135deg, #548cfd 0%, #667eea 100%) !important;
    width: 80rpx;
    height: 80rpx;
    box-shadow: 0 4rpx 15rpx rgba(84, 140, 253, 0.4);
    border: none;
    transition: all 0.3s ease;
}

.send-btn:active {
    transform: scale(0.95);
}

.scroll-bottom-btn {
    position: fixed;
    right: 20px;
    text-align: center;
    font-size: 11px;
    font-weight: 300;
    bottom: 30%;
}

.scroll-btn {
    border: 0.5px solid #548cfd;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fc 100%) !important;
    box-shadow: 0 4rpx 15rpx rgba(84, 140, 253, 0.2);
    transition: all 0.3s ease;
}

.scroll-btn:active {
    transform: scale(0.95);
}

.emoji-panel {
    width: 100%;
    height: 400rpx;
    background: linear-gradient(135deg, #f3f3f3 0%, #ffffff 100%);
    position: fixed;
    bottom: 15px;
    border-top-left-radius: 20rpx;
    border-top-right-radius: 20rpx;
    box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.emoji-item {
    margin: 5px 0px;
    padding: 8rpx;
    border-radius: 12rpx;
    transition: all 0.3s ease;
}

.emoji-item:active {
    background-color: rgba(84, 140, 253, 0.1);
    transform: scale(0.95);
}
</style>
