<template>
    <view>
        <view class="nav-wrap" :style="'height: ' + (height * 2 + 20) + 'px;'">
            <view class="nav-title" :style="'line-height: ' + (height * 2 + 44) + 'px;'">签到</view>
            <view style="display: flex; justify-content: space-around; flex-direction: column; height: 100%">
                <view class="nav-capsule">
                    <view @tap="navbackFun">
                        <image src="/static/yl_welore/style/icon/ahlib_nav_icon_back.png" mode="aspectFill" class="back-pre"></image>
                    </view>
                </view>
            </view>
        </view>
        <view class="gs_banner" :style="'margin-top:' + (height * 2 + 20) + 'px;'">
            <!-- <image src='../image/banner.jpg'></image> -->
            <view class="gs_continue">
                <view>每日坚持签到</view>
                <view>
                    总共签到：
                    <text>20</text>
                    天
                </view>
            </view>
        </view>
        <view class="gs_sign">
            <view class="gs_sign_box">
                <view class="gs_pillar">
                    <view class="gs_post">
                        <view></view>
                    </view>
                    <view class="gs_post">
                        <view></view>
                    </view>
                    <view class="gs_post">
                        <view></view>
                    </view>
                    <view class="gs_post">
                        <view></view>
                    </view>
                    <view class="gs_post">
                        <view></view>
                    </view>
                </view>
                <view class="gs_sign_day">
                    <image @tap="onshow" src="/static/yl_welore/style/icon/sign_icon.png"></image>
                    <view>
                        持续签到
                        <text>2</text>
                        天
                    </view>
                </view>
                <view class="gs_sign_content">
                    <view class="gs_week">
                        <block v-for="(item, index) in week" :key="index">
                            <view class="gs_wook">
                                <view style="width: 43px; height: 43px; line-height: 43px">{{ item.wook }}</view>
                            </view>
                        </block>
                    </view>
                    <view class="gs_week">
                        <block v-for="(item, index) in day" :key="index">
                            <view class="gs_wook" style="width: 43px; height: 43px; line-height: 43px">
                                <view :class="{'dateOn': item.wook == getDate}">{{ item.wook }}</view>
                                <view class="gs_clocksucceed" v-if="item.src"><image :src="item.src"></image></view>
                            </view>
                        </block>
                    </view>
                    <view class="gs_circle">
                        <view class="gs_incircle">
                            <view class="gs_excircle">
                                <view class="gs_innercircle">
                                    <view class="gs_btn">
                                        <text>打卡</text>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
var app = getApp();
export default {
    data() {
        return {
            sysW: null,
            lastDay: null,
            firstDay: null,
            year: null,
            hasEmptyGrid: false,
            cur_year: '',
            cur_month: '',
            getDate: null,
            month: null,

            week: [
                {
                    wook: '一'
                },
                {
                    wook: '二'
                },
                {
                    wook: '三'
                },
                {
                    wook: '四'
                },
                {
                    wook: '五'
                },
                {
                    wook: '六'
                },
                {
                    wook: '日'
                }
            ],

            day: [
                {
                    wook: ''
                },
                {
                    wook: ''
                },
                {
                    wook: ''
                },
                {
                    wook: ''
                },
                {
                    wook: ''
                },
                {
                    wook: ''
                },
                {
                    wook: ''
                }
            ],

            days: [],

            height: '',
            marLet: '',
            judge: 0,
            weekday: '',
            todayIndex: '',
            weeks_ch: '',
            empytGrids: '',
            src: '',
            currentTab: ''
        };
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        this.setNowDate();
        this.getProWeekList();
        this.dataTime();
        var res = uni.getSystemInfoSync();
        console.log(res);
        this.height = app.globalData.height;
        this.sysW = res.windowHeight / 14 - 15;
        //更具屏幕宽度变化自动设置宽度
        this.marLet = this.firstDay;
        this.getDate = this.getDate;
        this.judge = 1;
        this.month = this.month;

        /**
         * 获取系统信息
         */
        console.log(this.month);
    },
    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady() {},
    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {},
    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide() {},
    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload() {},
    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh() {},
    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom() {},
    /**
     * 用户点击右上角分享
     */
    onShareAppMessage() {},
    methods: {
        getProWeekList() {
            let date = new Date();
            let dateTime = date.getTime(); // 获取现在的时间
            let dateDay = date.getDay(); // 获取现在的
            let oneDayTime = 86400 * 1000; //一天的时间
            let proWeekList;
            console.log(dateTime);
            for (let i = 0; i < 7; i++) {
                let time = dateTime - (dateDay - 1 - i) * oneDayTime;
                proWeekList = new Date(time).getDate(); //date格式转换为yyyy-mm-dd格式的字符串
                this.day[i].wook = proWeekList;
                //that.data.day[i].wook = new Date(time).getDate();
            }
        },

        dateSelectAction(e) {
            let cur_day = e.currentTarget.dataset.idx;
            this.todayIndex = cur_day;
            console.log(`点击的日期:${this.cur_year}年${this.cur_month}月${cur_day + 1}日`);
        },

        setNowDate() {
            const date = new Date();
            const cur_year = date.getFullYear();
            const cur_month = date.getMonth() + 1;
            const todayIndex = date.getDate();
            console.log(`日期：${todayIndex}`);
            const weeks_ch = ['日', '一', '二', '三', '四', '五', '六'];
            this.calculateEmptyGrids(cur_year, cur_month);
            this.calculateDays(cur_year, cur_month);
            this.cur_year = cur_year;
            this.cur_month = cur_month;
            this.weeks_ch = weeks_ch;
            this.todayIndex = todayIndex;
        },

        getThisMonthDays(year, month) {
            return new Date(year, month, 0).getDate();
        },

        getFirstDayOfWeek(year, month) {
            return new Date(Date.UTC(year, month - 1, 1)).getDay();
        },

        calculateEmptyGrids(year, month) {
            const firstDayOfWeek = this.getFirstDayOfWeek(year, month);
            let empytGrids = [];
            if (firstDayOfWeek > 0) {
                for (let i = 0; i < firstDayOfWeek; i++) {
                    empytGrids.push(i);
                }
                this.hasEmptyGrid = true;
                this.empytGrids = empytGrids;
            } else {
                this.hasEmptyGrid = false;
                this.empytGrids = [];
            }
        },

        calculateDays(year, month) {
            const thisMonthDays = this.getThisMonthDays(year, month);
            for (let i = 1; i <= thisMonthDays; i++) {
                // days[i].push(i);
                this.$set(this.days, i - 1, {
                    item: i,
                    src: ''
                });
            }
            console.log(this.days);
        },

        dataTime() {
            var date = new Date();
            var year = date.getFullYear();
            var month = date.getMonth();
            var months = date.getMonth() + 1;

            //获取现今年份
            this.year = year;

            //获取现今月份
            this.month = months;

            //获取今日日期
            this.getDate = date.getDate();

            //最后一天是几号
            var d = new Date(year, months, 0);
            this.lastDay = d.getDate();

            //第一天星期几
            let firstDay = new Date(year, month, 1);
            this.firstDay = firstDay.getDay();
        },

        //滑动切换
        swiperTab(e) {
            this.currentTab = e.detail.current;
        },

        //点击切换
        clickTab(e) {
            if (this.currentTab === e.target.dataset.current) {
                return false;
            } else {
                this.currentTab = e.target.dataset.current;
            }
            // console.log(that.data.nubmerLength)
        },

        upper(e) {
            console.log(e);
        },

        lower(e) {
            console.log(e);
        },

        scroll(e) {
            console.log(e);
        },

        navbackFun() {
            console.log('占位：函数 _navback 未声明');
        },

        onshow() {
            console.log('占位：函数 onshow 未声明');
        }
    }
};
</script>
<style>
.nav-wrap {
    position: fixed;
    width: 100%;
    top: 0;
    background: #fff;
    color: #000;
    z-index: 9999999;
}

/* 标题要居中 */

.nav-title {
    position: absolute;
    text-align: center;
    max-width: 400rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    font-size: 36rpx;
    color: #2c2b2b;
    font-weight: 600;
}

.nav-capsule {
    display: flex;
    align-items: center;
    margin-left: 20rpx;
    width: 60rpx;
    justify-content: space-around;
    border-radius: 50rpx;
    margin-top: 35rpx;
    z-index: 999999999;
}

.navbar-v-line {
    width: 1px;
    height: 32rpx;
    background-color: #f3f3f3;
}

.back-pre {
    width: 40rpx;
    height: 40rpx;
    margin-top: 12rpx;
}

.nav-capsule .back-home {
    width: 34rpx;
    height: 34rpx;
    margin-top: 9rpx;
    margin-right: 16rpx;
}

page {
    background-color: #f5f5f5;
}
.gs_banner image {
    width: 750rpx;
    height: 256rpx;
    vertical-align: top;
}
.gs_continue {
    background-color: #fff;
    padding: 30rpx 20rpx;
}
.gs_continue view:first-child {
    font-size: 34rpx;
    color: #454545;
    padding-bottom: 20rpx;
}
.gs_continue view:last-child {
    font-size: 32rpx;
    color: #707070;
}
.gs_continue view text {
    color: #dd2522;
}
.gs_sign {
    margin-top: 30rpx;
}
.gs_sign_box {
    background-color: #fff;
    width: 677rpx;
    margin: 0 auto;
    position: relative;
    border-radius: 15rpx;
}
.gs_pillar {
    overflow: hidden;
    position: absolute;
    top: -16rpx;
    width: 100%;
}
.gs_pillar .gs_post {
    float: left;
    width: 20%;
    text-align: center;
}
.gs_pillar .gs_post view {
    display: inline-block;
    width: 18rpx;
    height: 42rpx;
    background-color: #53acfc;
    border-radius: 20rpx;
}
.gs_sign_day {
    padding: 60rpx 20rpx 0;
}
.gs_sign_day image {
    width: 50rpx;
    height: 50rpx;
    vertical-align: middle;
}
.gs_sign_day view {
    display: inline-block;
    font-size: 30rpx;
    margin-left: 20rpx;
    color: #707070;
}
.gs_sign_day view text {
    color: #dd2522;
}
.gs_sign_content {
    padding: 20rpx;
}
.gs_week {
    overflow: hidden;
}
.gs_wook {
    display: inline-block;
    text-align: center;
    position: relative;
    box-sizing: border-box;
}
.gs_wook view {
    display: inline-block;
    font-size: 30rpx;
    color: #707070;
    width: 60rpx;
    height: 60rpx;
    line-height: 60rpx;
}
.gs_clocksucceed {
    position: absolute;
    top: 10rpx;
    left: 16rpx;
    background-color: #fff;
}
.gs_clocksucceed image {
    width: 50rpx;
    height: 50rpx;
    vertical-align: bottom;
}
.gs_sign_content .gs_week:nth-child(2) .gs_wook view {
    color: #454545;
}
.gs_circle {
    padding: 50rpx 0;
}
.gs_incircle {
    width: 225rpx;
    height: 225rpx;
    background-image: linear-gradient(to top, #8bc4f6, #8bc4f6);
    border-radius: 50%;
    padding: 10rpx;
    margin: 20rpx auto;
    cursor: pointer;
}
.gs_excircle {
    width: 205rpx;
    height: 205rpx;
    background-color: #fff;
    border-radius: 50%;
    padding: 10rpx;
}
.gs_innercircle {
    width: 185rpx;
    height: 185rpx;
    background-image: linear-gradient(to top, #53acfc, #63b2f7);
    border-radius: 50%;
    padding: 10rpx;
}
.gs_btn {
    text-align: center;
}
.gs_btn text {
    background-color: transparent;
    border: none;
    outline: none;
    color: #fff;
    line-height: 185rpx;
    font-size: 36rpx;
}
.gs_calendar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: none;
}
.gs_bg {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.3);
}
.gs_gs_calendar_box {
    position: absolute;
    top: 180rpx;
    left: 40rpx;
    width: 677rpx;
    background-color: #fff;
    padding: 20rpx;
    box-sizing: border-box;
}
.canlendarBgView {
}
.canlendarView {
}
.canlendarTopView {
    overflow: hidden;
    padding: 40rpx 20rpx;
    margin-left: 20rpx;
}
.leftBgView {
    float: left;
    width: 153rpx;
    text-align: center;
}
.leftView {
}
.leftView image {
    width: 60rpx;
    height: 60rpx;
}
.centerView {
    float: left;
    font-size: 32rpx;
    height: 60rpx;
    line-height: 60rpx;
    width: 260rpx;
    text-align: center;
}
.rightBgView {
    float: left;
    width: 156rpx;
    text-align: center;
}
.rightView {
}
.rightView image {
    width: 60rpx;
    height: 60rpx;
}
.weekBgView {
    overflow: hidden;
}
.weekView {
    float: left;
    width: 14.28571428571429%;
    text-align: center;
    font-size: 30rpx;
    color: #707070;
}
.dateBgView {
    overflow: hidden;
    margin-bottom: 20rpx;
}
.dateEmptyView {
}
.dateView {
    display: inline-block;
    text-align: center;
    position: relative;
}
.datesView {
    font-size: 30rpx;
    color: #2b2b2b;
}
.dateOn {
    border-radius: 50%;
    background-color: #53acfc;
    color: #fff !important;
}
.del {
    position: absolute;
    top: -20rpx;
    right: -20rpx;
    width: 50rpx;
    height: 50rpx;
    background-color: #fff;
    border-radius: 50%;
}
.del image {
    width: 50rpx;
    height: 50rpx;
    vertical-align: text-top;
}
.clocksucceed {
    position: absolute;
    top: -3rpx;
    left: 20rpx;
    background-color: #fff;
}
.clocksucceed image {
    width: 50rpx;
    height: 50rpx;
    vertical-align: middle;
}
</style>
