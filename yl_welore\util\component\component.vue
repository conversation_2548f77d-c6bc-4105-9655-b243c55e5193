<template>
    <view class="content">
        <!-- 输入框（表格） -->
        <view
            :class="(interval ? 'pay_number' : 'pay_number_interval') + '  ' + (focus_classClone ? 'get_focus' : '')"
            @tap.stop.prevent="set_focus"
            :style="'width:' + width + ';height:' + height + ';font-size: 30px;font-weight: 700;'"
        >
            <view
                :class="
                    (focus_classClone ? (interval ? 'get_focus_dot' : 'get_focus_dot_interval') : interval ? 'password_dot' : 'password_dot_interval') +
                    ' ' +
                    (index == 0 ? 'noBorder' : '')
                "
                v-for="(item, index) in value_num"
                :key="index"
            >
                <view v-if="value_lengthClone == item - 1 && focus_classClone" class="cursor"></view>

                <view v-if="value_lengthClone >= item" :class="see ? '' : 'dot'">{{ see ? val_arr[index] : '' }}</view>
            </view>
        </view>

        <!-- 输入框（隐藏） -->
        <input
            :value="input_valueClone"
            :focus="get_focusClone"
            maxlength="8"
            type="number"
            class="input_container"
            placeholder=""
            @input="get_value"
            @focus="get_focusClone"
            @blur="blur"
        />
    </view>
</template>

<script>
// component.js
export default {
    // 组件属性
    data() {
        return {
            val_arr: '',
            focus_classClone: false,
            get_focusClone: false,
            value_lengthClone: 0,
            input_valueClone: ''
        };
    },
    props: {
        //输入框密码位数
        value_length: {
            type: Number,
            default: 0
        },
        // 是否显示间隔输入框
        interval: {
            type: Boolean,
            default: true
        },
        // 是否有下一步按钮（如果有则当输入6位数字时不自动清空内容）
        isNext: {
            type: Boolean,
            default: false
        },
        //输入框聚焦状态
        get_focus: {
            type: Boolean,
            default: false
        },
        //输入框初始内容
        input_value: {
            type: String,
            default: ''
        },
        //输入框聚焦样式
        focus_class: {
            type: Boolean,
            default: false
        },
        //输入框格子数
        value_num: {
            type: Array,
            default: () => [1, 2, 3, 4, 5, 6, 7, 8]
        },
        //输入框高度
        height: {
            type: String,
            default: '98rpx'
        },
        //输入框宽度
        width: {
            type: String,
            default: '604rpx'
        },
        //是否明文展示
        see: {
            type: Boolean,
            default: false
        }
    },
    // 组件方法
    methods: {
        // 获得焦点时
        get_focusFun() {
            let that = this;
            that.setData({
                focus_classClone: true
            });
        },
        // 失去焦点时
        blur() {
            let that = this;
            that.setData({
                focus_classClone: false
            });
        },
        // 点击聚焦
        set_focus() {
            let that = this;
            that.setData({
                get_focusClone: true
            });
        },
        // 获取输入框的值
        get_value(data) {
            let that = this;
            // 设置空数组用于明文展现
            let val_arr = [];
            // 获取当前输入框的值
            let now_val = data.detail.value;
            // 遍历把每个数字加入数组
            for (let i = 0; i < 8; i++) {
                val_arr.push(now_val.substr(i, 1));
            }
            // 获取输入框值的长度
            let value_length = data.detail.value.length;
            // 更新数据
            that.setData({
                value_lengthClone: value_length,
                val_arr: val_arr,
                input_valueClone: now_val
            });
        }
    },
    created: function () {},
    watch: {
        value_length: {
            handler: function (newVal, oldVal) {
                this.value_lengthClone = this.clone(this.value_length);
                let that = this;
                let input_value = that.input_value;
                that.$emit('valueNow', {
                    detail: input_value
                });
                // 当输入框的值等于6时（发起支付等...）
                if (newVal == 8) {
                    // 设定延时事件处理
                    setTimeout(function () {
                        // 引用组件页面的自定义函数(前一个参数为函数，后一个为传递给父页面的值)
                        that.$emit('valueSix', {
                            detail: input_value
                        });
                        // 当没有
                        if (!that.isNext) {
                            // 回到初始样式
                            that.setData({
                                get_focusClone: false,
                                value_lengthClone: 0,
                                input_valueClone: ''
                            });
                        }
                    }, 100);
                }
            },

            immediate: true
        },

        interval: {
            handler: function (newVal, oldVal) {},
            immediate: true
        },

        isNext: {
            handler: function (newVal, oldVal) {},
            immediate: true
        },

        get_focus: {
            handler: function (newVal, oldVal) {
                this.get_focusClone = this.clone(this.get_focus);
            },

            immediate: true
        },

        input_value: {
            handler: function (newVal, oldVal) {
                this.input_valueClone = this.clone(this.input_value);
            },

            immediate: true
        },

        focus_class: {
            handler: function (newVal, oldVal) {
                this.focus_classClone = this.clone(this.focus_class);
            },

            immediate: true
        },

        value_num: {
            handler: function (newVal, oldVal) {},
            immediate: true,
            deep: true
        },

        height: {
            handler: function (newVal, oldVal) {},
            immediate: true
        },

        width: {
            handler: function (newVal, oldVal) {},
            immediate: true
        },

        see: {
            handler: function (newVal, oldVal) {},
            immediate: true
        }
    }
};
</script>
<style>
/* 支付密码框 */

.pay_number {
    margin: 0 auto;
    display: flex;
    flex-direction: row;
    border: 1px solid #cfd4d3;
    border-radius: 3px;
}

.pay_number_interval {
    margin: 0 auto;
    display: flex;
    flex-direction: row;
    border-left: 1px solid #cfd4d3;
    /* border:none; */
}

/* 第一个格子输入框 */
.content .noBorder {
    border-left: none;
}

/* 支付密码框聚焦的时候 */

.get_focus {
    border-color: #1cbbb4;
}

/* 单个格式样式 */

.password_dot {
    flex: 1;
    border-left: 1px solid #cfd4d3;
    display: flex;
    align-items: center;
    justify-content: center;
}

.password_dot_interval {
    flex: 1;
    border: 1px solid #cfd4d3;
    margin-right: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 单个格式样式（聚焦的时候） */

.get_focus_dot {
    flex: 1;
    border-left: 1px solid #1cbbb4;
    display: flex;
    align-items: center;
    justify-content: center;
}

.get_focus_dot_interval {
    flex: 1;
    border: 1px solid #1cbbb4;
    margin-right: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 模拟光标 */

.cursor {
    width: 1px;
    height: 15px;
    background-color: #1cbbb4;
    animation: focus 0.7s infinite;
}

/* 光标动画 */

@keyframes focus {
    from {
        opacity: 1;
    }

    to {
        opacity: 0;
    }
}

/* 格式中的点 */

.dot {
    width: 10px;
    height: 10px;
    background-color: #000;
    border-radius: 50%;
}

/* 输入框 */

.input_container {
    /* height: 0;
  width: 0; */
    min-height: 0;
    position: relative;
    text-indent: -999em;
    left: -100%;
}
</style>
