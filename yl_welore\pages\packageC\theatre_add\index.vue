<template>
    <view>
        <cu-custom bgColor="bg-white" :isSearch="false" :isBack="true">
            <view slot="content" style="color: #2c2b2b; font-weight: 600; font-size: 36rpx">新增剧集</view>
        </cu-custom>
        <view style="padding: 10px">
            <view class="ui-form bg-white" style="border-radius: 5px; padding: 10px">
                <view class="flex justify-between align-center border-bottom" style="min-height: 4em; padding: 0.5em 30rpx">
                    <view class="">发布类型</view>
                    <view class="">
                        <radio-group class="block" @change="radioType">
                            <view>
                                <radio :checked="key == 0 ? true : false" value="0" class="yellow"></radio>
                                <text class="margin-left-sm" style="vertical-align: middle">新剧</text>
                                <radio :checked="key == 1 ? true : false" value="1" class="olive margin-left-sm"></radio>
                                <text class="margin-left-sm" style="vertical-align: middle">连载</text>
                            </view>
                        </radio-group>
                    </view>
                </view>
                <block v-if="key == 1">
                    <view class="flex justify-between align-center border-bottom" style="min-height: 4em; padding: 0.5em 30rpx">
                        <view class="text-black" style="width: 200rpx">选择连载剧</view>
                        <view class="">
                            <picker @change="bindPickerType" :value="index" range-key="title" :range="myList">
                                <view class="picker">
                                    <text style="font-weight: 500">{{ my_index == -1 ? '请选择' : myList[my_index].title }}</text>
                                    <text class="cicon-forward text-gray"></text>
                                </view>
                            </picker>
                        </view>
                    </view>
                </block>
                <block v-if="key == 0">
                    <view class="flex justify-between align-center border-bottom" style="min-height: 4em; padding: 0.5em 30rpx">
                        <view class="text-black" style="width: 200rpx">短剧标题</view>
                        <view style="width: 100%">
                            <input
                                @input="set_add_form"
                                data-key="1"
                                :value="title"
                                style="background-color: #f1f1f1; height: 45px; padding: 10px; border-radius: 10rpx"
                                placeholder="短剧标题"
                            />
                        </view>
                        <view style="position: absolute; left: 10rpx; color: red">*</view>
                    </view>
                    <view class="flex justify-between align-center border-bottom" style="min-height: 4em; padding: 0.5em 30rpx">
                        <view class="text-black" style="width: 200rpx">短剧别名</view>
                        <view style="width: 100%">
                            <input
                                @input="set_add_form"
                                data-key="2"
                                :value="alias"
                                style="background-color: #f1f1f1; height: 45px; padding: 10px; border-radius: 5px"
                                placeholder="短剧别名"
                            />
                        </view>
                    </view>
                    <view class="flex justify-start align-center border-bottom" style="padding: 0.5em 30rpx">
                        <view style="position: absolute; left: 10rpx; color: red">*</view>
                        <view class="text-black" style="width: 23%">短剧类型</view>
                        <view style="width: 77%">
                            <view class="cu-capsule" style="margin: 0rpx; padding: 0rpx 15rpx 10rpx 0rpx" v-for="(item, index) in type" :key="index">
                                <view class="cu-tag line-gray">
                                    {{ item.name }}
                                </view>

                                <view class="cu-tag bg-gray">
                                    <text class="_icon-close" @tap="delType" :data-index="index"></text>
                                </view>
                            </view>
                            <view @tap="openTypeMode" class="cu-capsule" style="margin: 0rpx">
                                <view class="cu-tag bg-yellow" style="color: #ffffff">
                                    <text class="_icon-add"></text>
                                    <text style="margin-left: 5rpx">新增类型</text>
                                </view>
                            </view>
                        </view>
                    </view>
                    <view class="flex justify-between align-center border-bottom" style="min-height: 4em; padding: 0.5em 30rpx">
                        <view class="text-black" style="width: 200rpx">短剧导演</view>
                        <view style="width: 100%">
                            <input
                                @input="set_add_form"
                                data-key="3"
                                :value="director"
                                style="background-color: #f1f1f1; height: 45px; padding: 10px; border-radius: 5px"
                                placeholder="短剧导演"
                            />
                        </view>
                    </view>
                    <view class="flex justify-between align-center border-bottom" style="min-height: 4em; padding: 0.5em 30rpx">
                        <view class="text-black" style="width: 200rpx">短剧编剧</view>
                        <view style="width: 100%">
                            <input
                                @input="set_add_form"
                                data-key="4"
                                :value="screenwriter"
                                style="background-color: #f1f1f1; height: 45px; padding: 10px; border-radius: 5px"
                                placeholder="短剧编剧"
                            />
                        </view>
                    </view>
                    <view class="flex justify-between align-center border-bottom" style="min-height: 4em; padding: 0.5em 30rpx">
                        <view class="text-black" style="width: 200rpx">短剧主演</view>
                        <view style="width: 100%">
                            <input
                                @input="set_add_form"
                                data-key="5"
                                :value="lead_actors"
                                style="background-color: #f1f1f1; height: 45px; padding: 10px; border-radius: 5px"
                                placeholder="短剧主演"
                            />
                        </view>
                    </view>
                    <view class="flex justify-between align-center border-bottom" style="min-height: 4em; padding: 0.5em 30rpx">
                        <view class="text-black" style="width: 200rpx">短剧制片</view>
                        <view style="width: 100%">
                            <input
                                @input="set_add_form"
                                data-key="6"
                                :value="production_country"
                                style="background-color: #f1f1f1; height: 45px; padding: 10px; border-radius: 5px"
                                placeholder="短剧制片国家或地区"
                            />
                        </view>
                    </view>
                    <view class="flex justify-between align-center border-bottom" style="min-height: 4em; padding: 0.5em 30rpx">
                        <view class="text-black" style="width: 200rpx">短剧语言</view>
                        <view style="width: 100%">
                            <input
                                @input="set_add_form"
                                data-key="7"
                                :value="language"
                                style="background-color: #f1f1f1; height: 45px; padding: 10px; border-radius: 5px"
                                placeholder="短剧语言"
                            />
                        </view>
                    </view>
                    <view class="flex justify-between align-center border-bottom" style="min-height: 4em; padding: 0.5em 30rpx">
                        <view class="text-black" style="width: 200rpx">上映日期</view>
                        <view style="width: 100%">
                            <input
                                @input="set_add_form"
                                data-key="8"
                                :value="release_date"
                                style="background-color: #f1f1f1; height: 45px; padding: 10px; border-radius: 5px"
                                placeholder="短剧上映日期"
                            />
                        </view>
                    </view>
                    <view class="flex justify-between align-center border-bottom" style="min-height: 4em; padding: 0.5em 30rpx">
                        <view class="text-black" style="width: 200rpx">短剧片长</view>
                        <view style="width: 100%">
                            <input
                                type="number"
                                @input="set_add_form"
                                data-key="9"
                                :value="duration_minutes"
                                style="background-color: #f1f1f1; height: 45px; padding: 10px; border-radius: 5px"
                                placeholder="短剧短剧片长（分钟）"
                            />
                        </view>
                    </view>
                    <view class="border-bottom" style="min-height: 4em; padding: 0.5em 30rpx">
                        <view style="position: absolute; left: 10rpx; color: red">*</view>
                        <view class="left_text text-black">剧情简介</view>
                        <view style="margin-top: 20rpx">
                            <textarea
                                @input="set_add_form"
                                data-key="10"
                                :value="plot_summary"
                                style="background-color: #f1f1f1; padding: 15px 10px; border-radius: 5px; width: 100%"
                                maxlength="200"
                                placeholder="短剧剧情简介"
                            ></textarea>
                        </view>
                    </view>

                    <view v-if="config.is_require_user_copyright == 1" class="flex justify-between border-bottom">
                        <view style="position: absolute; left: 10rpx; color: red; top: 15rpx">*</view>
                        <view style="min-height: 4em; padding: 0.5em 30rpx; width: 65%">
                            <view class="left_text text-black">用户版权或授权证书图片</view>
                            <view class="">
                                <view style="width: 100%">
                                    <view
                                        v-if="user_copyright_img != ''"
                                        style="margin-top: 10px; position: relative"
                                        class="bg-img"
                                        @tap="ViewImage"
                                        data-key="1"
                                        :data-url="user_copyright_img"
                                    >
                                        <image :src="user_copyright_img" style="width: 300rpx" mode="widthFix"></image>
                                        <view class="cu-tag bg-red" data-key="1" @tap.stop.prevent="DelImg" style="z-index: 10; position: absolute; right: 0">
                                            <text class="cuIcon-close"></text>
                                        </view>
                                    </view>
                                    <view
                                        v-if="user_copyright_img == ''"
                                        data-key="1"
                                        @tap="previewImage"
                                        style="width: 250rpx; height: 250rpx; background-color: #f1f1f1; text-align: center; border-radius: 5px; margin-top: 20rpx"
                                    >
                                        <view class="cicon-add-round" style="margin-top: 60rpx; font-size: 60rpx"></view>
                                        <view style="line-height: 80rpx; font-size: 12px">上传图片</view>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>
                </block>
                <view class="flex justify-between">
                    <view style="min-height: 4em; padding: 0.5em 30rpx; width: 50%; position: relative">
                        <view style="position: absolute; left: 10rpx; color: red; top: 15rpx">*</view>
                        <view class="left_text text-black">短剧封面</view>
                        <view class="">
                            <view style="width: 100%">
                                <view v-if="poster_url != ''" style="margin-top: 10px; position: relative" class="bg-img" @tap="ViewImage" data-key="2" :data-url="poster_url">
                                    <image :src="poster_url" style="width: 300rpx" mode="widthFix"></image>
                                    <view class="cu-tag bg-red" data-key="2" @tap.stop.prevent="DelImg" style="z-index: 10; position: absolute; right: 0">
                                        <text class="cuIcon-close"></text>
                                    </view>
                                </view>
                                <view
                                    v-if="poster_url == ''"
                                    data-key="2"
                                    @tap="previewImage"
                                    style="width: 250rpx; height: 250rpx; background-color: #f1f1f1; text-align: center; border-radius: 5px; margin-top: 20rpx"
                                >
                                    <view class="cicon-add-round" style="margin-top: 60rpx; font-size: 60rpx"></view>
                                    <view style="line-height: 80rpx; font-size: 12px">上传图片</view>
                                </view>
                            </view>
                        </view>
                    </view>
                    <view style="min-height: 4em; padding: 0.5em 30rpx; width: 50%; position: relative">
                        <view style="position: absolute; left: 10rpx; color: red; top: 15rpx">*</view>
                        <view class="left_text text-black">上传短剧</view>
                        <view class="">
                            <view class="grid col-4 grid-square flex-sub">
                                <view v-if="msi_episode_url != ''" style="margin-top: 10px; width: 100%; height: 100%; margin-bottom: 0; padding-bottom: 0; margin-right: 0">
                                    <video :src="msi_episode_url" style="height: 275rpx; width: 100%"></video>
                                    <view class="cu-tag bg-red" @tap.stop.prevent="DelMovie" style="z-index: 10; background-color: #e54d42">
                                        <text class="cuIcon-close"></text>
                                    </view>
                                </view>
                                <view
                                    v-if="msi_episode_url == ''"
                                    @tap="previewMovie"
                                    style="margin-right: 0px; width: 250rpx; height: 250rpx; background-color: #f1f1f1; text-align: center; border-radius: 5px; margin-top: 20rpx"
                                >
                                    <view class="cicon-play-circle" style="margin-top: 60rpx; font-size: 60rpx"></view>
                                    <view style="line-height: 80rpx; font-size: 12px">上传视频</view>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
                <view class="flex justify-between align-center border-bottom" style="min-height: 4em; padding: 0.5em 30rpx">
                    <view class="text-black" style="width: 200rpx">视频直链</view>
                    <view style="width: 100%">
                        <input
                            @input="set_add_form"
                            data-key="12"
                            :value="url_link"
                            style="background-color: #f1f1f1; height: 45px; padding: 10px; border-radius: 5px"
                            placeholder="填写视频直链（优先视频上传）"
                        />
                    </view>
                </view>
                <block v-if="config.is_allow_user_charge != 0">
                    <view class="flex justify-between align-center border-bottom" style="min-height: 4em; padding: 0.5em 30rpx">
                        <view class="text-black" style="width: 200rpx">付费观看</view>
                        <view style="width: 100%">
                            <radio-group class="block" @change="unlockType">
                                <view>
                                    <radio :checked="paid_unlocking_type == 0 ? true : false" value="0" class="yellow"></radio>
                                    <text class="margin-left-sm" style="vertical-align: middle">免费</text>
                                    <radio :checked="paid_unlocking_type == 1 ? true : false" value="1" class="olive margin-left-sm"></radio>
                                    <text class="margin-left-sm" style="vertical-align: middle">贝壳</text>
                                    <radio :checked="paid_unlocking_type == 2 ? true : false" value="2" class="blue margin-left-sm"></radio>
                                    <text class="margin-left-sm" style="vertical-align: middle">积分</text>
                                </view>
                            </radio-group>
                        </view>
                    </view>
                    <view v-if="paid_unlocking_type != 0" class="flex justify-between align-center border-bottom" style="min-height: 4em; padding: 0.5em 30rpx">
                        <view class="text-black" style="width: 200rpx">付费金额</view>
                        <view style="width: 100%">
                            <input
                                type="digit"
                                @input="set_add_form"
                                data-key="11"
                                :value="paid_unlocking_price"
                                style="background-color: #f1f1f1; height: 45px; padding: 10px; border-radius: 5px"
                                placeholder="付费金额"
                            />
                        </view>
                    </view>
                </block>
                <view @tap="submit" class="bg-yellow padding round text-center shadow-blur text-white" style="z-index: 1; margin: 30px 20px 20px 20px">发布</view>
            </view>
        </view>

        <view :class="'cu-modal ' + (typeMode ? 'show' : '')" @tap="hideModal">
            <view class="cu-dialog" catchtap>
                <view style="padding: 40rpx; text-align: left">
                    <view
                        @tap="insType"
                        :data-index="index"
                        :class="'cu-tag ' + (item.is == 1 ? 'bg-green' : '')"
                        style="margin: 20rpx"
                        v-for="(item, index) in typeList"
                        :key="index"
                    >
                        {{ item.name }}
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
var app = getApp();
import http from '../../../util/http.js';
export default {
    /**
     * 页面的初始数据
     */
    data() {
        return {
            key: 0,
            my_video_id: 0,
            paid_unlocking_type: 0,
            //付费解锁
            paid_unlocking_price: '',
            user_copyright_img: '',
            //授权证书
            poster_url: '',
            msi_episode_url: '',
            my_index: -1,
            //连载剧下标
            typeList: [],
            //类型
            myList: [],
            //连载剧
            config: {},
            //配置
            typeMode: false,
            title: '',
            alias: '',
            type: [],
            director: '',
            screenwriter: '',
            lead_actors: '',
            production_country: '',
            language: '',
            release_date: '',
            duration_minutes: '',
            plot_summary: '',
            url_link: ''
        };
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        this.getType();
    },
    methods: {
        submit() {
            uni.showLoading({
                title: '上传中...'
            });
            var b = app.globalData.api_root + 'Microseries/doAdd';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.openid = e.openid;
            params.title = this.title;
            if (this.type.length == 0 && this.key == 0) {
                uni.showToast({
                    title: '请选择短剧类型',
                    icon: 'none',
                    duration: 2000
                });
                return;
            }
            if (this.type.length > 0) {
                var ids = this.type.map((obj) => obj.id);
                var stringifiedNumbers = ids.join(',');
                params.type = stringifiedNumbers;
            }
            params.key = this.key;
            params.my_video_id = this.my_video_id;
            params.poster_url = this.poster_url;
            params.director = this.director;
            params.screenwriter = this.screenwriter;
            params.lead_actors = this.lead_actors;
            params.production_country = this.production_country;
            params.language = this.language;
            params.release_date = this.release_date;
            params.duration_minutes = this.duration_minutes;
            params.alias = this.alias;
            params.plot_summary = this.plot_summary;
            params.paid_unlocking_type = this.paid_unlocking_type;
            params.paid_unlocking_price = this.paid_unlocking_price;
            params.msi_episode_url = this.msi_episode_url;
            params.url_link = this.url_link;
            params.user_copyright_img = this.user_copyright_img;
            http.POST(b, {
                params: params,
                success: (res) => {
                    uni.hideLoading();
                    console.log(res);
                    if ((res.data.status = 'success')) {
                        uni.showModal({
                            title: '提示',
                            content: res.data.msg,
                            showCancel: false,
                            success: function (res) {
                                uni.navigateBack();
                            }
                        });
                    } else {
                        uni.showModal({
                            title: '提示',
                            content: res.data.msg,
                            showCancel: false,
                            success: function (res) {}
                        });
                    }
                },
                fail: () => {
                    uni.hideLoading();
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        },
        DelImg(d) {
            console.log(d);
            var key = d.currentTarget.dataset.key;
            if (key == 1) {
                this.user_copyright_img = '';
            }
            if (key == 2) {
                this.poster_url = '';
            }
        },
        DelMovie() {
            this.msi_episode_url = '';
        },
        bindPickerType(d) {
            console.log(d);
            var index = d.detail.value;
            this.my_index = index;
            this.my_video_id = this.myList[index].id;
        },
        set_add_form(d) {
            var key = d.currentTarget.dataset.key;
            var value = d.detail.value;
            switch (key) {
                case '1':
                    this.title = value;
                    break;
                case '2':
                    this.alias = value;
                    break;
                case '3':
                    this.director = value;
                    break;
                case '4':
                    this.screenwriter = value;
                    break;
                case '5':
                    this.lead_actors = value;
                    break;
                case '6':
                    this.production_country = value;
                    break;
                case '7':
                    this.language = value;
                    break;
                case '8':
                    this.release_date = value;
                    break;
                case '9':
                    this.duration_minutes = value;
                    break;
                case '10':
                    this.plot_summary = value;
                    break;
                case '11':
                    this.paid_unlocking_price = value;
                    break;
                case '12':
                    this.url_link = value;
                    break;
            }
        },
        radioType(d) {
            this.key = d.detail.value;
            this.my_video_id = 0;
            this.my_index = -1;
        },
        unlockType(d) {
            this.paid_unlocking_type = d.detail.value;
        },
        openTypeMode() {
            this.typeMode = true;
        },
        hideModal() {
            this.typeMode = false;
        },
        ViewImage(e) {
            console.log(e);
            var key = e.currentTarget.dataset.key;
            var urls = '';
            if (key == 1) {
                urls = this.user_copyright_img;
            }
            if (key == 2) {
                urls = this.poster_url;
            }
            uni.previewImage({
                urls: [urls],
                current: e.currentTarget.dataset.url
            });
        },
        previewMovie() {
            var e = app.globalData.getCache('userinfo');
            var b = app.globalData.api_root + 'User/img_upload';
            uni.chooseMedia({
                count: 1,
                mediaType: ['video'],
                // 可以指定是原图还是压缩图，默认二者都有
                sourceType: ['album', 'camera'],
                // 可以指定来源是相册还是相机，默认二者都有
                sizeType: ['original', 'compressed'],
                success: (res) => {
                    uni.showLoading({
                        title: '上传中...',
                        mask: true
                    });
                    console.log(res);
                    var tempFilePaths = res.tempFiles[0].tempFilePath;
                    uni.uploadFile({
                        url: b,
                        filePath: tempFilePaths,
                        name: 'sngpic',
                        header: {
                            'content-type': 'multipart/form-data'
                        },
                        formData: {
                            'content-type': 'multipart/form-data',
                            token: e.token,
                            openid: e.openid,
                            much_id: app.globalData.siteInfo.uniacid
                        },
                        success: (res) => {
                            console.log(res);
                            var data = JSON.parse(res.data);
                            console.log(data);
                            if (data.status == 'error') {
                                uni.showToast({
                                    title: data.msg,
                                    icon: 'none',
                                    duration: 2000
                                });
                            } else {
                                this.msi_episode_url = data.url;
                                uni.hideLoading();
                            }
                        },
                        fail: (res) => {
                            uni.showToast({
                                title: '上传错误！',
                                icon: 'none',
                                duration: 2000
                            });
                        }
                    });
                }
            });
        },
        /**
         * 上传主图
         */
        previewImage(d) {
            console.log(d);
            var key = d.currentTarget.dataset.key;
            var e = app.globalData.getCache('userinfo');
            var b = app.globalData.api_root + 'User/img_upload';
            uni.chooseMedia({
                count: 1,
                mediaType: ['image'],
                // 可以指定是原图还是压缩图，默认二者都有
                sourceType: ['album', 'camera'],
                // 可以指定来源是相册还是相机，默认二者都有
                sizeType: ['original', 'compressed'],
                success: (res) => {
                    uni.showLoading({
                        title: '上传中...',
                        mask: true
                    });
                    console.log(res);
                    var tempFilePaths = res.tempFiles[0].tempFilePath;
                    uni.uploadFile({
                        url: b,
                        filePath: tempFilePaths,
                        name: 'sngpic',
                        header: {
                            'content-type': 'multipart/form-data'
                        },
                        formData: {
                            'content-type': 'multipart/form-data',
                            token: e.token,
                            openid: e.openid,
                            much_id: app.globalData.siteInfo.uniacid
                        },
                        success: (res) => {
                            console.log(res);
                            var data = JSON.parse(res.data);
                            console.log(data);
                            if (data.status == 'error') {
                                uni.showToast({
                                    title: data.msg,
                                    icon: 'none',
                                    duration: 2000
                                });
                            } else {
                                if (key == 1) {
                                    this.user_copyright_img = data.url;
                                }
                                if (key == 2) {
                                    this.poster_url = data.url;
                                }
                                uni.hideLoading();
                            }
                        },
                        fail: (res) => {
                            uni.showToast({
                                title: '上传错误！',
                                icon: 'none',
                                duration: 2000
                            });
                        }
                    });
                }
            });
        },
        delType(d) {
            console.log(d);
            var index = d.currentTarget.dataset.index;
            var arr = this.type;
            var info = this.type[index];
            console.log(info);
            var type_list = this.typeList;
            var type_index = type_list.findIndex((item) => item.id === info.id);
            if (type_index !== -1) {
                // 修改is属性为0
                type_list[type_index].is = 0;
            }
            arr.splice(index, 1);
            this.type = arr;
            this.typeList = type_list;
        },
        insType(d) {
            var index = d.currentTarget.dataset.index;
            var info = this.typeList[index];
            var typeList = this.typeList;
            typeList[index].is = typeList[index].is == 0 ? 1 : 0;
            var arr = this.type;
            if (typeList[index].is == 1) {
                arr.push(info);
            } else {
                arr = arr.filter((item) => item.id !== info.id);
            }
            this.type = arr;
            this.typeList = typeList;
        },
        getType() {
            var b = app.globalData.api_root + 'Microseries/getType';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.openid = e.openid;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    this.typeList = res.data.type;
                    this.myList = res.data.list;
                    this.config = res.data.config;
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        }
    }
};
</script>
<style>
.border-bottom {
    position: relative;
}
</style>
