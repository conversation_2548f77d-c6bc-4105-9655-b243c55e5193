<template>
    <view>
        <cu-custom bgColor="bg-white" :isSearch="false" :isBack="true">
            <view slot="backText">返回</view>
            <view slot="content" style="color: #2c2b2b; font-weight: 600; font-size: 36rpx">黑名单</view>
        </cu-custom>
        <view style="width: 100%; height: 1px; margin-top: 10px"></view>
            <view class="cu-list menu-avatar">
                <view
                    :class="'cu-item ' + (modalName == 'move-box-' + l_index ? 'move-cur' : '')"
                    @touchstart="ListTouchStartFun"
                    @touchmove="ListTouchMove"
                    @touchend="ListTouchEnd"
                    :data-target="'move-box-' + l_index"
                    v-for="(item, l_index) in list"
                    :key="l_index"
                >
                    <view class="cu-avatar round lg" :style="'background-image:url(' + item.user_head_sculpture + ');'"></view>

                    <view class="content">
                        <view class="text-grey">{{ item.user_nick_name }}</view>
                        <view class="text-gray text-sm">
                            {{ item.bl_time }}
                        </view>
                    </view>

                    <view class="move">
                        <view class="bg-grey">取消</view>
                        <view :data-index="l_index" :data-pu_user_id="item.pu_user_id" :data-id="item.id" class="bg-red" @tap="del_black">删除</view>
                    </view>
                </view>
            </view>
        <view :class="'cu-load ' + (list.length == 0 ? 'over' : '')"></view>
    </view>
</template>

<script>
var app = getApp();
var http = require('../../../util/http.js');
export default {
    data() {
        return {
            actions: [
                {
                    name: '移除',
                    color: '#fff',
                    fontsize: '20',
                    width: 100,
                    background: '#ed3f14'
                },
                {
                    name: '取消',
                    width: 100,
                    color: '#80848f',
                    fontsize: '20'
                }
            ],

            list: [],
            ListTouchStart: '',
            ListTouchDirection: '',
            modalName: '',
            l_index: ''
        };
    }
    /**
     * 生命周期函数--监听页面加载
     */,
    onLoad(options) {},
    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {
        this.get_black_list();
    },
    methods: {
        /**
         * 列表
         */
        get_black_list() {
            var b = app.globalData.api_root + 'User/get_black_list';
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            http.POST(b, {
                params: params,
                success: function (res) {
                    that.list = res.data;
                },
                fail: function () {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        },

        del_black(c) {
            var id = c.currentTarget.dataset.id;
            var pu_user_id = c.currentTarget.dataset.pu_user_id;
            var index = c.currentTarget.dataset.index;
            var b = app.globalData.api_root + 'User/del_black';
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.id = id;
            params.pu_user_id = pu_user_id;
            http.POST(b, {
                params: params,
                success: function (res) {
                    if (res.data.status == 'success') {
                        var lists = that.list;
                        lists.splice(index, 1);
                        that.list = lists;
                    } else {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                },
                fail: function () {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        },

        // ListTouch触摸开始
        ListTouchStartFun(e) {
            this.ListTouchStart = e.touches[0].pageX;
        },

        // ListTouch计算方向
        ListTouchMove(e) {
            this.ListTouchDirection = e.touches[0].pageX - this.ListTouchStart > 0 ? 'right' : 'left';
        },

        // ListTouch计算滚动
        ListTouchEnd(e) {
            if (this.ListTouchDirection == 'left') {
                this.modalName = e.currentTarget.dataset.target;
            } else {
                this.modalName = null;
            }
            this.ListTouchDirection = null;
        }
    }
};
</script>
<style>
page {
    background-color: #fff;
}
</style>
