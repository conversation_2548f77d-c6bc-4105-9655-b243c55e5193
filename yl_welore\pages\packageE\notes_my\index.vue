<template>
    <view>
        <cu-custom bgColor="bg-white" :isSearch="false" :isBack="true">
            <view slot="backText"></view>
            <view slot="content" style="color: #000000; font-weight: 600; font-size: 36rpx">我的</view>
        </cu-custom>
        <scroll-view scroll-x class="bg-white nav" style="padding-bottom: 10px">
            <view class="flex text-center">
                <view :class="'cu-item flex-sub ' + (1 == TabCur ? 'text-purple' : '')" @tap="tabSelect" data-id="1" style="position: relative">
                    <text>我放入</text>
                    <view v-if="TabCur == 1" class="tab_bottom"></view>
                </view>
                <view :class="'cu-item flex-sub ' + (2 == TabCur ? 'text-purple' : '')" @tap="tabSelect" data-id="2" style="position: relative">
                    <text>我抽到的</text>
                    <view v-if="TabCur == 2" class="tab_bottom"></view>
                </view>
            </view>
        </scroll-view>
        <view class="cu-card dynamic" style="overflow: initial" v-for="(item, l_index) in list" :key="l_index">
            <view class="cu-item shadow" style="overflow: initial">
                <view class="cu-list menu-avatar" style="overflow: initial">
                    <view class="cu-item">
                        <view v-if="item.gender == 1" class="man">男生</view>
                        <view v-if="item.gender == 0" class="woman">女生</view>
                    </view>
                </view>
                <view class="text-content font_weight">
                    {{ item.hedge_content }}
                </view>
                <view class="grid flex-sub padding-lr col-3 grid-square">
                    <view class="bg-img" :style="'background-image:url(' + img + ');'" v-for="(img, index) in item.image_part" :key="index"></view>
                </view>
                <view class="text-content margin-top font_weight">
                    <text>留的联系方式：</text>
                    <text>{{ item.contact_person }}</text>
                    <button @tap="copyBtn" :data-key="item.contact_person" class="cu-btn round bg-green button-hover sm margin-left">点击复制</button>
                </view>
                <view style="border-top: 1rpx solid #eee; margin-top: 20px"></view>
                <view class="text-gray text-sm padding cf" v-if="TabCur == 1">
                    <view class="fl" style="height: 64rpx; line-height: 64rpx">
                        <text v-if="item.check_status == 0">待审核</text>
                        <text v-if="item.check_status == 1">已被{{ item.smoke_content }}人抽到</text>
                        <text class="text-red" v-if="item.check_status == 2">审核未通过：{{ item.check_opinion }}</text>
                    </view>
                    <button @tap="tape_del" :data-id="item.id" class="cu-btn round bg-red button-hover fr">删除</button>
                    <button @tap="update_this" :data-id="l_index" v-if="item.check_status == 2" class="cu-btn round bg-blue button-hover fr" style="margin-right: 10px">
                        修改
                    </button>
                </view>
            </view>
        </view>
        <view :class="'cu-modal bottom-modal ' + (modal == 'f_modal' ? 'show' : '')">
            <view class="cu-dialog" style="height: 80%; border-radius: 10px">
                <view class="cu-bar bg-white">
                    <view class="content font_weight">
                        <text>修改信息</text>
                    </view>
                    <view class="action text-green"></view>
                    <view class="action font_weight" @tap="hideModal">取消</view>
                </view>
                <view class="padding">
                    <view class="cu-form-group">
                        <view class="title">联系方式</view>
                        <input :value="info.contact_person" @input="contact_person_input" style="text-align: right" placeholder="微信号 / QQ" />
                    </view>
                    <view class="bg-white margin-top padding-bottom">
                        <view class="cu-form-group">
                            <textarea
                                :value="info.hedge_content"
                                style="text-align: left"
                                maxlength="1000"
                                @input="textareaAInput"
                                placeholder="介绍自己或对另一半的期待..."
                            ></textarea>
                        </view>
                        <view class="cu-form-group" style="border-top: 0px">
                            <view class="grid col-4 grid-square flex-sub">
                                <view class="bg-img" @tap="ViewImage" :data-url="info.image_part[index]" v-for="(item, index) in info.image_part" :key="index">
                                    <image :src="info.image_part[index]" mode="aspectFill" style="left: 0"></image>

                                    <view class="cu-tag bg-red" @tap.stop.prevent="DelImg" :data-index="index">
                                        <text class="cuIcon-close"></text>
                                    </view>
                                </view>
                                <view class="solids" @tap="chooseImage" v-if="info.image_part.length < 3">
                                    <text class="cuIcon-cameraadd"></text>
                                </view>
                            </view>
                        </view>
                        <view @tap="type_update_do" class="bg-yellow padding radius text-center shadow-blur margin-top">
                            <view>确定修改</view>
                        </view>
                    </view>
                </view>
            </view>
        </view>
        <view :class="'cu-load ' + (!di_msg ? 'loading' : 'over')"></view>
    </view>
</template>

<script>
const app = getApp();
var http = require('../../../util/http.js');

export default {
    /**
     * 页面的初始数据
     */
    data() {
        return {
            list: [],
            TabCur: 1,
            page: 1,
            id: 0,
            info: {},
            modal: null,
            di_msg: false
        }
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        this.TabCur = options.key;
        this.page = 1;
        if (this.TabCur == '1') {
            this.get_tape();
        } else {
            this.get_somke_tape();
        }
        uni.hideShareMenu();
    },
    /**
     * 加载下一页
     */
    onReachBottom() {
        if (this.TabCur == '1') {
            this.page = this.page + 1;
            this.get_tape();
        } else {
            this.page = this.page + 1;
            this.get_somke_tape();
        }
    },
    methods: {
        tape_del(e) {
            var that = this;
            var id = e.currentTarget.dataset.id;
            this.id = id;
            uni.showModal({
                title: '提示',
                content: '是否要删除这个纸条？',
                success(res) {
                    if (res.confirm) {
                        that.del_tape_do();
                    } else if (res.cancel) {
                        console.log('用户点击取消');
                    }
                }
            });
        },
        del_tape_do() {
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            var b = app.globalData.api_root + 'Tape/del_tape_do';
            params.token = e.token;
            params.openid = e.openid;
            params.id = this.id;
            http.POST(b, {
                params: params,
                success(res) {
                    console.log(res);
                    if (res.data.code == 0) {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 1500
                        });
                        that.page = 1;
                        that.list = [];
                        that.get_tape();
                    } else {
                        uni.showModal({
                            title: '提示',
                            content: res.data.msg,
                            showCancel: false,
                            success(res) {}
                        });
                    }
                },
                fail() {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success(res) {}
                    });
                }
            });
        },
        get_somke_tape() {
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            var b = app.globalData.api_root + 'Tape/somke_tape_list';
            params.token = e.token;
            params.openid = e.openid;
            params.page = this.page;
            var allMsg = that.list;
            http.POST(b, {
                params: params,
                success(res) {
                    console.log(res);
                    if (res.data.info.length == 0 || res.data.info.length <= 2) {
                        that.di_msg = true;
                    }
                    for (var i = 0; i < res.data.info.length; i++) {
                        allMsg.push(res.data.info[i]);
                    }
                    that.list = allMsg;
                },
                fail() {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success(res) {}
                    });
                }
            });
        },
        get_tape() {
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            var b = app.globalData.api_root + 'Tape/get_tape_list';
            params.token = e.token;
            params.openid = e.openid;
            params.page = this.page;
            var allMsg = that.list;
            http.POST(b, {
                params: params,
                success(res) {
                    console.log(res);
                    if (res.data.info.length == 0 || res.data.info.length <= 2) {
                        that.di_msg = true;
                    }
                    for (var i = 0; i < res.data.info.length; i++) {
                        allMsg.push(res.data.info[i]);
                    }
                    that.list = allMsg;
                },
                fail() {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success(res) {}
                    });
                }
            });
        },
        tabSelect(e) {
            var id = e.currentTarget.dataset.id;
            this.TabCur = e.currentTarget.dataset.id;
            this.page = 1;
            this.list = [];
            if (id == 1) {
                this.get_tape();
            } else {
                this.get_somke_tape();
            }
        },
        update_this(e) {
            var index = e.currentTarget.dataset.id;
            this.info = this.list[index];
            this.modal = 'f_modal';
        },
        /**
         * 删除图片
         */
        DelImg(e) {
            var that = this;
            var index = e.target.dataset['index'];
            var notes = that.info.image_part;
            notes.splice(index, 1);
            that.info.image_part = notes;
        },
        /**
         * 上传图片
         */
        chooseImage() {
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var b = app.globalData.api_root + 'User/img_upload';
            uni.chooseImage({
                count: 3,
                sizeType: ['original', 'compressed'],
                // 可以指定是原图还是压缩图，默认二者都有
                sourceType: ['album', 'camera'],
                // 可以指定来源是相册还是相机，默认二者都有
                success(res) {
                    console.log(res);
                    uni.showLoading({
                        title: '上传中...',
                        mask: true
                    });
                    let tempFilePaths = res.tempFilePaths;
                    for (var i = 0, h = tempFilePaths.length; i < h; i++) {
                        uni.uploadFile({
                            url: b,
                            filePath: tempFilePaths[i],
                            name: 'sngpic',
                            header: {
                                'content-type': 'multipart/form-data'
                            },
                            formData: {
                                'content-type': 'multipart/form-data',
                                token: e.token,
                                openid: e.openid,
                                much_id: app.globalData.siteInfo.uniacid
                            },
                            success(res) {
                                console.log(res);
                                if (res.data == '') {
                                    uni.hideLoading();
                                    uni.showModal({
                                        title: '提示',
                                        content: '内存溢出，请稍候重试'
                                    });
                                    return;
                                }
                                var data = JSON.parse(res.data);
                                console.log(data);
                                if (data.status == 'error') {
                                    uni.hideLoading();
                                    uni.showModal({
                                        title: '提示',
                                        content: data.msg
                                    });
                                    return;
                                } else {
                                    that.info.image_part = that.info.image_part.concat(data.url);
                                    uni.hideLoading();
                                }
                            },
                            fail(res) {
                                uni.showModal({
                                    title: '提示',
                                    content: '上传错误！'
                                });
                            }
                        });
                    }
                }
            });
        },
        contact_person_input(e) {
            this.info.contact_person = e.detail.value;
        },
        textareaAInput(e) {
            this.info.hedge_content = e.detail.value;
        },
        type_update_do() {
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            var b = app.globalData.api_root + 'Tape/update_tape_do';
            params.token = e.token;
            params.openid = e.openid;
            params.contact_person = this.info.contact_person;
            params.hedge_content = this.info.hedge_content;
            params.imgList = this.info.image_part;
            params.id = this.info.id;
            http.POST(b, {
                params: params,
                success(res) {
                    console.log(res);
                    uni.showModal({
                        title: '提示',
                        content: res.data.msg,
                        showCancel: false,
                        success(res) {
                            that.modal = null;
                            that.list = [];
                            that.page = 1;
                            if (that.TabCur == 1) {
                                that.get_tape();
                            } else {
                                that.get_somke_tape();
                            }
                        }
                    });
                },
                fail() {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success(res) {}
                    });
                }
            });
        },
        /**
         * 一键复制
         */
        copyBtn(e) {
            var that = this;
            uni.setClipboardData({
                data: e.currentTarget.dataset.key,
                success(res) {
                    console.log(res);
                }
            });
        },
        hideModal() {
            this.modal = null;
        }
    }
};
</script>
<style>
.tab_bottom {
    width: 20%;
    position: absolute;
    height: 3px;
    background-color: var(--purple);
    bottom: 0px;
    border-radius: 10px;
    margin: 0 auto;
    left: 0;
    right: 0;
}

.man {
    color: #ffffff;
    display: inline-block;
    position: absolute;
    width: 115px;
    height: 35px;
    line-height: 35px;
    padding-left: 15px;
    background: #59324c;
    left: -8px;
    top: 20px;
}

.man:before {
    content: '';
    position: absolute;
    height: 0;
    width: 0;
    border-bottom: 8px solid black;
    border-left: 8px solid transparent;
    top: -8px;
    left: 0;
}

.man:after {
    content: '';
    position: absolute;
    height: 0;
    width: 0;
    border-top: 16px solid transparent;
    border-bottom: 19px solid transparent;
    border-left: 15px solid #59324c;
    right: -15px;
}

.woman {
    color: #ffffff;
    display: inline-block;
    position: absolute;
    width: 115px;
    height: 35px;
    line-height: 35px;
    padding-left: 15px;
    background: #e03997;
    left: -8px;
    top: 20px;
}

.woman:before {
    content: '';
    position: absolute;
    height: 0;
    width: 0;
    border-bottom: 8px solid #e03997;
    border-left: 8px solid transparent;
    top: -8px;
    left: 0;
}

.woman:after {
    content: '';
    position: absolute;
    height: 0;
    width: 0;
    border-top: 16px solid transparent;
    border-bottom: 19px solid transparent;
    border-left: 15px solid #e03997;
    right: -15px;
}
</style>
