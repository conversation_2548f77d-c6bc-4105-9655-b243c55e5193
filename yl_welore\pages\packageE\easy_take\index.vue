<template>
    <view>
        <cu-custom bgColor="bg-white" :isSearch="false" :isBack="true">
            <view slot="backText"></view>
            <view slot="content" style="color: #000000; font-weight: 600; font-size: 36rpx">提货记录</view>
        </cu-custom>
        <scroll-view scroll-x class="bg-white nav">
            <view class="flex text-center">
                <view :class="'cu-item flex-sub ' + (TabCur == 0 ? 'text-blue cur' : '')" @tap="tabSelect" data-id="0">未核销</view>
                <view :class="'cu-item flex-sub ' + (TabCur == 1 ? 'text-blue cur' : '')" @tap="tabSelect" data-id="1">已核销</view>
            </view>
        </scroll-view>
        <view style="margin: 15px; padding: 10px; background: #ffffff; border-radius: 10px" v-for="(item, index) in list" :key="index">
            <view style="border-radius: 5px">
                <view class="flex solid-bottom justify-between" style="padding: 10px">
                    <view>
                        <image :src="item.easy.merchant_icon_carousel[0]" style="width: 35px; height: 35px; vertical-align: middle; border-radius: 50%"></image>
                        <text style="vertical-align: middle; margin-left: 10px">{{ item.easy.merchant_name }}</text>
                    </view>
                    <view>
                        <image src="" style="width: 35px; height: 35px; vertical-align: middle"></image>
                        <view v-if="item.overdue == 2" class="bg-red cu-tag round">已过期</view>
                    </view>
                </view>
                <view style="margin: 20px" slot="content">
                    <view>
                        <text style="vertical-align: middle">用户：</text>
                        <image :src="item.user_info.user_head_sculpture" style="width: 30px; height: 30px; vertical-align: middle; border-radius: 50%"></image>
                        <text style="vertical-align: middle; margin-left: 10px">{{ item.user_info.user_nick_name }}</text>
                    </view>
                </view>
                <view style="margin: 20px" slot="content">
                    <text>商品：{{ item.goods.product_name }}</text>
                    <text style="margin-left: 10px">x1</text>
                </view>
                <view style="margin: 20px" slot="content">
                    <text>购买时间：</text>
                    <text>{{ item.create_time }}</text>
                </view>
                <view style="margin: 20px" slot="content">
                    <text>核销时间：</text>
                    <view v-if="item.verify == 0" class="cu-tag radius sm bg-red">未核销</view>
                    <view v-if="item.verify != 0" class="cu-tag radius bg-green">{{ item.verify }}</view>
                </view>

                <!-- <view slot="content" style="text-align:center;margin:20px;">
      <text style="font-size: 30px;font-weight: 700;letter-spacing: 10px;">{{item.rand_captcha}}</text>
    </view> -->
                <!-- <view slot="content" style="text-align:center;margin: 20px">
      <button bindtap="open_url" data-key="0" data-id="{{item.paper.id}}" data-type="{{item.paper.study_type}}" class="cu-btn round bg-blue shadow">查看详情</button>
      <button bindtap="open_url" data-key="1" data-id="{{item.paper.id}}" wx:if="{{item.overdue==1}}" class="cu-btn round bg-green shadow margin-left">线下验证</button>
    </view> -->
            </view>
        </view>
        <view :class="'cu-load ' + (!di_msg ? 'loading' : 'over')"></view>
    </view>
</template>

<script>
const app = getApp();
var http = require('../../../util/http.js');

export default {
    /**
     * 页面的初始数据
     */
    data() {
        return {
            TabCur: 0,
            list: [],
            page: 1,
            di_msg: false
        }
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        this.get_my_take();
    },

    /**
     * 加载下一页
     */
    onReachBottom() {
        this.page = this.page + 1;
        this.get_my_take();
    },

    methods: {
        tabSelect(e) {
            this.TabCur = e.currentTarget.dataset.id;
            this.list = [];
            this.page = 1;
            this.get_my_take();
        },

        get_my_take() {
            var b = app.globalData.api_root + 'Order/get_user_take';
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.page = this.page;
            params.type = this.TabCur;
            http.POST(b, {
                params: params,
                success(res) {
                    console.log(res);
                    var list = that.list;
                    list.push(...res.data);
                    that.list = list;
                    if (res.data.length == 0 || res.data.length < 3) {
                        that.di_msg = true;
                    }
                },
                fail() {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success() {}
                    });
                }
            });
        }
    }
}
</script>
<style></style>
