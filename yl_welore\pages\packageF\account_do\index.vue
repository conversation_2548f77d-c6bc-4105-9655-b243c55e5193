<template>
    <view>
        <web-view :src="url"></web-view>
    </view>
</template>

<script>
const app = getApp();
var http = require('../../../util/http.js');

export default {
    /**
     * 页面的初始数据
     */
    data() {
        return {
            http_root: app.globalData.http_root,
            url: ''
        }
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        var e = app.globalData.getCache('userinfo');
        var url =
            'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' +
            options.wx_app_id +
            '&redirect_uri=' +
            this.http_root +
            'addons/yl_welore/web/index.php?s=/api/wechat/index&response_type=code&scope=snsapi_base&state=' +
            e.id +
            'A' +
            e.much_id +
            '#wechat_redirect';
        // 规则1: setData改为uniapp格式
        this.url = url;
    },
    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady() {},
    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {},
    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide() {},
    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload() {},
    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh() {},
    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom() {},
    /**
     * 用户点击右上角分享
     */
    onShareAppMessage() {},
    methods: {

    }
}
</script>
<style>
/* yl_welore/pages/packageF/account_do/index.wxss */
</style>
