<template>
    <view>
        <cu-custom bgColor="bg-white" :isBack="true" :isSearch="false">
            <view slot="backText">返回</view>
            <view slot="content" style="color: #2c2b2b; font-weight: 600; font-size: 36rpx">提现明细</view>
        </cu-custom>

        <view style="clear: both; height: 0"></view>
        <view style="margin: 0px 17px; width: 87%; height: auto; border-radius: 10px; padding: 10px; font-size: 14px">
            <view style="margin: 20px 0px" v-for="(item, index) in withdraw_list" :key="index">
                <view style="float: left">
                    <view class="">
                        <text v-if="item.withdraw_type == 0">微信</text>
                        <text v-if="item.withdraw_type == 1">银行卡</text>
                        提现
                    </view>
                    <view style="color: #999999; font-size: 13px">
                        <text v-if="item.status == 0">{{ item.seek_time }}</text>
                        <text v-if="item.status > 0">{{ item.verify_time }}</text>
                        <text v-if="item.status == 0" style="margin-left: 10px; color: #ff9933">审核中</text>
                        <text v-if="item.status == 1" style="margin-left: 10px; color: #33cc99">已提现</text>
                        <text v-if="item.status == 2" style="margin-left: 10px; color: #cc3333">审核未通过</text>
                    </view>
                </view>

                <view style="float: right; line-height: 50px">
                    <view style="font-size: 18px; color: #54b835">
                        {{ item.display_money }}
                    </view>
                </view>

                <view style="clear: both; height: 0"></view>
            </view>
        </view>
    </view>
</template>

<script>
const app = getApp();
import http from '../../../util/http.js';
export default {
    data() {
        return {
            user_info: {},
            withdraw_list: [],
            page: 0
        };
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        this.page = 1;
        this.withdraw_listFun();
    },
    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {},
    /**
     * 加载下一页
     */
    onReachBottom() {
        this.page = this.page + 1;
        this.withdraw_listFun();
    },
    /**
     * 用户点击右上角分享
     */
    onShareAppMessage() {
        const forward = app.globalData.forward;
        console.log(forward);
        if (forward) {
            return {
                title: forward.title,
                path: '/yl_welore/pages/index/index',
                imageUrl: forward.reis_img
            };
        } else {
            return {
                title: '您的好友给您发了一条信息',
                path: '/yl_welore/pages/index/index'
            };
        }
    },
    methods: {
        /**
         * 提现
         */
        withdraw_listFun() {
            const b = app.globalData.api_root + 'User/get_withdraw_list';
            const e = app.globalData.getCache('userinfo');
            const params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.uid = e.uid;
            params.page = this.page;
            http.POST(b, {
                params: params,
                success: (res) => {
                    this.withdraw_list = res.data;
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        }
    }
};
</script>
<style>
page {
    background-color: #fff;
}
</style>
