<template>
    <view>
        <cu-custom bgColor="bg-yaoqing" :isSearch="false" :isBack="true" :ShowUid="false">
            <view slot="backText">返回</view>
            <view slot="content" style="color: #2c2b2b; font-weight: 600; font-size: 36rpx">推荐有奖</view>
        </cu-custom>

        <view>
            <image src="/static/yl_welore/style/icon/1033914-20180419150322109-1754886203.png" mode="widthFix" style="width: 100%"></image>
        </view>
        <view style="text-align: center">我的邀请码</view>
        <view style="text-align: center; padding: 20px 0px; font-size: 40px; font-weight: 700; letter-spacing: 1px">
            {{ code }}
        </view>
        <view style="text-align: center">
            <input
                type="text"
                :value="yzm_text"
                @input="get_yzm_text"
                placeholder-class="pp_class"
                placeholder="输入好友验证码"
                style="height: 40px; font-size: 25px; font-weight: 700; background-color: #f5f5f5; border-radius: 30px; width: 255px; margin: 0 auto"
            />
            <view style="text-align: center; font-size: 12px; color: #999999; margin-top: 5px">输入朋友邀请码,随机获得奖励</view>
            <view
                @tap="add_user_invitation"
                style="
                    background-image: linear-gradient(to right, #ffdf01 0%, #ffca0a 100%);
                    box-shadow: 0px 0px 10px 0px #ffca0a;
                    color: #fff;
                    padding: 10px;
                    border-radius: 30px;
                    width: 20%;
                    margin: 20px auto;
                "
            >
                提交
            </view>
        </view>

        <button @getuserinfo="img_quan" open-type="getUserInfo" style="text-align: center" hover-class="none">
            <!-- <image src='../../../style/icon/icon_card_weixin___card.png' style='width:70px;height:70px;'></image> -->
            <image src="/static/yl_welore/style/icon/icon_card_friend___card.png" style="width: 70px; height: 70px"></image>
        </button>
        <view style="text-align: center; margin: 10px 0px">生成海报</view>

        <view :class="'cu-modal ' + (set_img_quan ? 'show' : '')">
            <view class="cu-dialog" id="canvas-container">
                <canvas v-if="set_img_quan" canvas-id="myCanvas" style="width: 100%; background-color: #ffffff; height: 500px"></canvas>
                <view class="cu-bar bg-white">
                    <view class="action margin-0 flex-sub solid-left" @tap="no_set_img_quan">取消</view>
                    <view class="action margin-0 flex-sub text-green" @tap="saveShareImg">
                        <text class="cuIcon-picfill"></text>
                        保存到相册
                    </view>
                </view>
            </view>
        </view>

        <view :class="'cu-modal ' + (check_user_login ? 'show' : '')">
            <view class="cu-dialog">
                <view class="cu-bar bg-white justify-end">
                    <view class="content">微信授权</view>
                    <view class="action" @tap="hideModal">
                        <text class="cuIcon-close text-red"></text>
                    </view>
                </view>
                <view class="padding bg-white" style="padding-top: 0px">
                    <view style="text-align: center">
                        <image v-if="stateVar.copyright.sgraph" :src="stateVar.copyright.sgraph" style="height: 70px; width: 70px; border-radius: 50%"></image>

                        <image
                            v-if="!stateVar.copyright.sgraph"
                            src="/static/yl_welore/style/icon/icon_card_weixin___card.png"
                            style="height: 70px; width: 70px; border-radius: 50%"
                        ></image>
                    </view>
                    <view class="wx_user_face" style="width: 100%; margin-bottom: 10px">
                        <text style="font-size: 15px">你需要小程序提供以下权限即可继续操作</text>
                    </view>
                    <view class="wx_login_info">
                        <text style="font-size: 14px; color: #999">·获得你的公开信息（昵称，头像等）</text>
                    </view>
                </view>
                <view class="cu-bar bg-green">
                    <button hover-class="none" open-type="getUserInfo" @tap="onGotUserInfo" style="color: #fff">授权微信账号登录</button>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
var app = getApp();
var http = require('../../../util/http.js');
var util = require('../../../util/data.js');
const fsm = uni.getFileSystemManager();
import regeneratorRuntime from '../../../util/runtime';
export default {
    data() {
        return {
            info: [],
            page: 1,
            check_user_login: false,
            set_img_quan: false,
            code: '',
            yzm_text: '',
            scene: '',
            imagePath_c: '',

            stateVar: {
                copyright: {
                    sgraph: ''
                }
            }
        };
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        var scene = decodeURIComponent(options.scene);
        console.log(scene);
        this.doIt(scene);
    },
    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {},
    methods: {
        async doIt(scene) {
            var e = app.globalData.getCache('userinfo');
            const do0 = await this.checkToken();
            if (!e || do0.data.status == 'no') {
                //缓存为空执行登陆
                const do1 = await this.getLogin();
            } else {
                //字段为空执行登陆
                if (typeof e.token_impede == 'undefined') {
                    const do1 = await this.getLogin();
                } else {
                    //字段0或者小于当前时间执行登陆
                    var t = parseInt(+new Date() / 1000);
                    if (e.token_impede == 0 || t >= e.token_impede) {
                        const do1 = await this.getLogin();
                    }
                }
            }
            const do2 = await this.ger_user_code(1, 0);
            if (scene != 'undefined' && scene != '') {
                this.ger_user_code(2, scene);
            }
        },

        checkToken() {
            return new Promise((resolve, reject) => {
                var e = app.globalData.getCache('userinfo');
                if (!e) {
                    resolve(e);
                    return 'no'; //执行登陆
                } else {
                    var b = app.globalData.api_root + 'Check/check_token';
                    var e = app.globalData.getCache('userinfo');
                    var params = new Object();
                    params.token = e.token;
                    params.openid = e.openid;
                    http.POST(b, {
                        params: params,
                        success: (res) => {
                            resolve(res);
                        },
                        fail: function () {
                            uni.showModal({
                                title: '提示',
                                content: '网络繁忙，请稍候重试！',
                                showCancel: false,
                                success: function (res) {}
                            });
                        }
                    });
                }
            });
        },

        /**
         * 输入的验证码
         */
        get_yzm_text(e) {
            this.yzm_text = e.detail.value;
        },

        //获取验证码
        ger_user_code(type, uid) {
            return new Promise((resolve, reject) => {
                var e = app.globalData.getCache('userinfo');
                var params = new Object();
                params.token = e.token;
                params.openid = e.openid;
                if (type == 1) {
                    params.uid = e.uid;
                } else {
                    params.uid = uid;
                }
                var b = app.globalData.api_root + 'User/ger_user_code';
                http.POST(b, {
                    params: params,
                    success: (res) => {
                        console.log(res);
                        if (type == 1) {
                            this.code = res.data.code;
                        } else {
                            this.yzm_text = res.data.code;
                        }
                        resolve(res);
                    },
                    fail: function () {
                        uni.showModal({
                            title: '提示',
                            content: '网络繁忙，请稍候重试！',
                            showCancel: false,
                            success: function (res) {}
                        });
                    }
                });
            });
        },

        /**
         * 输入验证码提交
         */
        add_user_invitation() {
            var e = app.globalData.getCache('userinfo');
            if (e.tourist == 1) {
                this.check_user_login = true;
                return;
            }
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.uid = e.uid;
            params.yzm_text = this.yzm_text;
            if (this.yzm_text == '') {
                uni.showToast({
                    title: '内容不能为空',
                    icon: 'none',
                    duration: 2000
                });
                return false;
            }
            var b = app.globalData.api_root + 'User/add_user_invitation';
            http.POST(b, {
                params: params,
                success: (res) => {
                    if (res.data.status == 'success') {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    } else {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                },
                fail: function () {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        },

        hideModal() {
            var e = app.globalData.getCache('userinfo');
            this.check_user_login = false;
            //this.ger_cor_user_code(e.uid);
        },

        img_quan() {
            var e = app.globalData.getCache('userinfo');
            if (e.tourist == 1) {
                this.check_user_login = true;
                return;
            }
            this.set_img_quan = true;
            this.getAvaterInfo();
        },

        no_set_img_quan() {
            this.set_img_quan = false;
        },

        /**
         * 先下载头像图片
         */
        getAvaterInfo() {
            uni.showLoading({
                title: '生成中...',
                mask: true
            });
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.img = e.user_head_sculpture;
            var b = app.globalData.api_root + 'User/base64EncodeImage';
            http.POST(b, {
                params: params,
                success: (res) => {
                    this.getQrCode(res.data.base); //继续下载二维码图片
                },

                fail: function () {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        },

        /**
         * 下载二维码图片
         */
        getQrCode(avaterSrc) {
            uni.showLoading({
                title: '生成中...',
                mask: true
            });
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.uid = e.uid;
            params.much_id = app.globalData.siteInfo.uniacid;
            var b = app.globalData.api_root + 'User/qrcode_code';
            uni.request({
                url: b,
                method: 'POST',
                data: params,
                responseType: 'arraybuffer',
                header: {
                    'content-type': 'application/json,charset=utf-8'
                },
                success: (red) => {
                    var base64 = uni.arrayBufferToBase64(red.data);
                    this.sharePosteCanvas(avaterSrc, base64);
                }
            });
        },

        /**
         * 开始用canvas绘制分享海报
         * @param avaterSrc 下载的头像图片路径
         * @param codeSrc   下载的二维码图片路径
         */
        sharePosteCanvas(avaterSrc, codeSrc) {
            uni.showLoading({
                title: '生成中...',
                mask: true
            });
            var cardInfo = app.globalData.getCache('userinfo'); //需要绘制的数据集合
            const ctx = uni.createCanvasContext('myCanvas', this);
            var width = '';
            uni.createSelectorQuery()
                .in(this)
                .select('#canvas-container')
                .boundingClientRect((rect) => {
                    var height = rect.height;
                    var right = rect.right;
                    width = rect.width;
                    var left = rect.left;
                    ctx.save();
                    ctx.setFillStyle('#FFE200');
                    ctx.fillRect(0, 0, width, height);
                    //上半部分
                    var path2 = '/static/yl_welore/style/icon/1033914-20180419150322109-1754886203.png';
                    ctx.drawImage(path2, 0, 0, width, 150);

                    //头像
                    ctx.beginPath();
                    ctx.fill();
                    var avatarurl_width = 50; //绘制的头像宽度
                    var avatarurl_heigth = 50; //绘制的头像高度
                    var avatarurl_x = width / 2 - 25;
                    var avatarurl_y = 160;
                    //先画个圆   前两个参数确定了圆心 （x,y） 坐标  第三个参数是圆的半径  四参数是绘图方向  默认是false，即顺时针
                    var headData = avaterSrc.split(',');
                    const head_buffer = util._base64ToArrayBuffer(headData[1]);
                    const fileName_head = uni.env.USER_DATA_PATH + '/head_img.png';
                    fsm.writeFileSync(fileName_head, head_buffer, 'binary');
                    ctx.arc(avatarurl_width / 2 + avatarurl_x, avatarurl_heigth / 2 + avatarurl_y, avatarurl_width / 2, 0, Math.PI * 2, false);
                    ctx.clip();
                    ctx.drawImage(fileName_head, avatarurl_x, avatarurl_y, avatarurl_width, avatarurl_heigth); // 推进去图片，必须是https图片
                    //昵称
                    ctx.restore();
                    ctx.setFontSize(20);
                    ctx.setFillStyle('#000');
                    ctx.fillText(cardInfo.user_nick_name, (width - ctx.measureText(cardInfo.user_nick_name).width) / 2, avatarurl_y + 75);
                    //说明
                    ctx.setFontSize(11);
                    ctx.setFillStyle('#000');
                    ctx.fillText('邀你一起赚赏金', (width - ctx.measureText('邀你一起赚赏金').width) / 2, avatarurl_y + 100);
                    //验证码背景
                    var path3 = '/static/yl_welore/style/icon/1033914-20180419150331561-740454292.png';
                    ctx.drawImage(path3, width / 2 - 115, avatarurl_y + 130, 230, 60);
                    //验证码
                    ctx.setFontSize(35);
                    ctx.setFillStyle('#FFE200');
                    ctx.fillText(this.code, (width - ctx.measureText(this.code).width) / 2, avatarurl_y + 165);
                    //说明
                    var path4 = '/static/yl_welore/style/icon/1033914-20180419150338090-1432865429.png';
                    ctx.drawImage(path4, width / 2 - 125, avatarurl_y + 230, 130, 70);
                    //验证码
                    ctx.setFontSize(10);
                    ctx.setFillStyle('#000000');
                    ctx.fillText('进入小程序输入朋友的邀', width / 2 - 120, avatarurl_y + 250);
                    ctx.fillText('请码，各自都会获得赏金', width / 2 - 120, avatarurl_y + 265);
                    ctx.fillText('哦~', width / 2 - 120, avatarurl_y + 280);
                    //二维码
                    const buffer_code1 = util._base64ToArrayBuffer(codeSrc);
                    const fileName_code1 = uni.env.USER_DATA_PATH + '/code_img' + this.code + '.png';
                    fsm.writeFileSync(fileName_code1, buffer_code1, 'binary');
                    ctx.drawImage(fileName_code1, width - 120, avatarurl_y + 200, 80, 80);
                    //长按
                    var path5 = '/static/yl_welore/style/icon/1033914-20180419150342935-1154229474.png';
                    ctx.drawImage(path5, width - 120, avatarurl_y + 280, 80, 22);
                })
                .exec();
            setTimeout(() => {
                ctx.draw();
                uni.canvasToTempFilePath({
                    canvasId: 'myCanvas',
                    success: (res) => {
                        var tempFilePath = res.tempFilePath;
                        this.imagePath_c = tempFilePath;
                    },
                    fail: function (res) {
                        console.log(res);
                    }
                });
                uni.hideLoading();
            }, 1000);
        },

        //点击保存到相册
        saveShareImg() {
            uni.showLoading({
                title: '正在保存',
                mask: true
            });
            setTimeout(() => {
                uni.canvasToTempFilePath(
                    {
                        canvasId: 'myCanvas',
                        success: (res) => {
                            uni.hideLoading();
                            var tempFilePath = res.tempFilePath;
                            uni.saveImageToPhotosAlbum({
                                filePath: tempFilePath,
                                success(res) {
                                    uni.showModal({
                                        content: '图片已保存到相册，赶紧晒一下吧~',
                                        showCancel: false,
                                        confirmText: '好的',
                                        confirmColor: '#333',
                                        success: function (res) {
                                            if (res.confirm) {
                                            }
                                        },
                                        fail: function (res) {}
                                    });
                                },
                                fail: function (res) {
                                    console.log(res);
                                    if (res.errMsg == 'saveImageToPhotosAlbum:fail auth deny') {
                                        uni.showModal({
                                            content: '检测到您未打开微信保存图片到相册，开启后即可保存图片',
                                            confirmText: '去开启',
                                            success(res) {
                                                if (res.confirm) {
                                                    uni.openSetting({
                                                        success(res) {}
                                                    });
                                                } else if (res.cancel) {
                                                }
                                            }
                                        });
                                    }
                                }
                            });
                        },
                        fail: function (err) {
                            console.log(err);
                        }
                    },
                    this
                );
            }, 1000);
        },

        onGotUserInfo() {
            uni.showLoading({
                title: '登陆中...',
                mask: true
            });
            uni.getUserProfile({
                desc: '获取个人信息',
                success: (user) => {
                    console.log(user);
                    var e = app.globalData.getCache('userinfo');
                    var data_all = new Object();
                    data_all.wx_openid = e.openid;
                    data_all.userInfo = JSON.stringify(user.userInfo);
                    data_all.uniacid = app.globalData.siteInfo.uniacid;
                    http.POST(app.globalData.api_root + 'Login/do_login', {
                        params: data_all,
                        success: (res) => {
                            console.log(res);
                            if (res.data.code == 1) {
                                uni.showModal({
                                    title: '提示',
                                    content: '登录失败，请稍候重试！',
                                    showCancel: false,
                                    success: function (res) {}
                                });
                                return;
                            } else {
                                uni.showToast({
                                    title: '登录成功',
                                    icon: 'none',
                                    duration: 2000
                                });
                            }
                            var user_info = res.data;
                            var e = app.globalData.getCache('userinfo');
                            user_info.openid = res.data.user_wechat_open_id;
                            user_info.token = res.data.token;
                            user_info.uid = res.data.id;
                            user_info.session_key = e.session_key;
                            console.log(user_info);
                            app.globalData.setCache('userinfo', user_info);
                            uni.hideLoading();
                            this.check_user_login = false;
                        },
                        fail: function (r) {
                            console.log('r');
                            uni.hideLoading();
                        }
                    });
                },
                fail(res) {
                    console.log(res);
                    uni.hideLoading();
                    if (res.errMsg == 'getUserProfile:fail auth deny') {
                        return;
                    }
                    uni.showModal({
                        title: '提示',
                        content: res.errMsg,
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        },

        /**
         * 登陆
         */
        getLogin() {
            return new Promise((resolve, reject) => {
                uni.login({
                    success: (res) => {
                        console.log(res);
                        var params = new Object();
                        params.code = res.code;
                        http.POST(app.globalData.api_root + 'Login/index', {
                            params: params,
                            success: (open) => {
                                console.log(open);
                                var data = new Object();
                                data.openid = open.data.info.openid;
                                data.session_key = open.data.info.session_key;
                                http.POST(app.globalData.api_root + 'Login/add_tourist', {
                                    params: data,
                                    success: (d) => {
                                        console.log(d.data.info);
                                        app.globalData.setCache('userinfo', d.data.info);
                                        resolve(d);
                                    }
                                });
                            }
                        });
                    }
                });
            });
        }
    }
};
</script>
<style>
page {
    background-color: #fff;
}
.pp_class {
    font-size: 14px;
    font-weight: 500;
}

button::after {
    line-height: normal;
    font-size: 30rpx;
    width: 0;
    height: 0;
    top: 0;
    left: 0;
}

button {
    line-height: normal;
    display: block;
    padding-left: 0px;
    padding-right: 0px;
    background-color: rgba(255, 255, 255, 0);
    font-size: 30rpx;
    overflow: inherit;
}
</style>
