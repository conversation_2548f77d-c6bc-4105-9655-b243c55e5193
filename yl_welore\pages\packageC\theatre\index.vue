<template>
    <view>
        <view :class="currIndex == 1 ? 'page-1' : 'page'"></view>
        <cu-custom v-if="currIndex != 1" bgColor="transparent" :isSearch="false" :isBack="true">
            <view slot="content" style="color: #2c2b2b; font-weight: 600; font-size: 36rpx">{{ config.custom_title }}
            </view>
        </cu-custom>
        <Theatre_count v-if="currIndex == 1" :parentData="currentInstance"
            @get_ser_name="get_ser_name"
            @changeSwiper="changeSwiper"
            @open_mode="open_mode"
            @videoWaiting="videoWaiting"
            @timeupdate="timeupdate"
            @loadedmetadata="loadedmetadata"
            @progress="progress"
            @eventPlay="eventPlay"
            @playOrPause="playOrPause"></Theatre_count>
        <Theatre_list v-if="currIndex == 0" :parentData="currentInstance"
            @get_ser_name="get_ser_name"
            @sou="sou"
            @tabSelect="tabSelect"
            @open_url="open_url"></Theatre_list>
        <Theatre_user v-if="currIndex == 2"  @open_url="open_url" :parentData="currentInstance"></Theatre_user>

        <view class="tabbar-box">
            <block v-for="(item, index) in menu" :key="index">
                <view :class="'menu-item ' + (currIndex == index && 'active')" @tap.stop.prevent="tabClick"
                    :data-index="index">
                    <view :class="'iconfont ' + item.icon + ' ' + (currIndex == index ? 'text-white' : '')"></view>
                    <text>{{ item.name }}</text>
                </view>
            </block>
            <!-- 添加选中圆形 -->
            <view class="active-tabbar-box" :style="'--n:' + currIndex"></view>
        </view>
    </view>
</template>

<script>
import Theatre_count from './theatre_count.vue';
import Theatre_list from './theatre_list.vue';
import Theatre_user from './theatre_user.vue';
import http from '../../../util/http.js';
import regeneratorRuntime from '../../../util/runtime';
const app = getApp();
export default {
    components: {
        Theatre_count,
        Theatre_list,
        Theatre_user
    },
    /**
     * 页面的初始数据
     */
    data() {
        return {
            currentInstance:this,
            di_msg: true,
            TabCur: 0,
            type_list: [],
            type_id: 0,
            index_list: [],
            page: 1,
            config: {},
            scrollLeft: 0,
            select: false,
            animBack: {},
            menu: [
                {
                    name: '剧场',
                    icon: 'cicon-group'
                },
                {
                    name: '发现',
                    icon: 'cicon-play-circle'
                },
                {
                    name: '我的',
                    icon: 'cicon-avatar'
                }
            ],
            // 当前选中菜单的索引，默认为第一个菜单
            currIndex: 0,
            current: 0,
            // 记录上一个current
            circular: false,
            // 是否可以循环播放
            isPlaying: true,
            // 暂停|播放
            videoList: [],
            // 页面的视频列表
            windowH: 0,
            rzindex: 0,
            u: {},
            content: ''
        };
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        this.doIt();
    },

    onReady: function () {
        this.videoContext = uni.createVideoContext('myVideo');
    },

    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom() {
        this.page = this.page + 1;
        this.getIndexList();
    },

    onShareAppMessage() {
        return {
            title: this.config.custom_title,
            path: '/yl_welore/pages/packageC/theatre/index'
        };
    },

    onShareTimeline() {
        return {
            title: this.config.custom_title,
            path: '/yl_welore/pages/packageC/theatre/index'
        };
    },

    methods: {
        get_ser_name(d) {
            console.log(d);
            var content = d.detail.value;
            this.content = content;
            if (content == '') {
                this.page = 1;
                this.index_list = [];
                this.getIndexList();
            }
        },
        sou() {
            this.page = 1;
            this.index_list = [];
            this.getIndexList();
        },
        // 菜单点击事件
        tabClick(e) {
            let { index } = e.currentTarget.dataset;
            this.currIndex = index;
        },
        doIt() {
            app.globalData.getLogin(
                // 成功回调 returnA 
                (userInfo) => {
                    console.log(' 登录成功:', userInfo);
                    this.u=userInfo;
                    this.getTypeList();
                    this.getRandomIndexList();
                },
                // 失败回调 returnB 
                (err) => {
                    console.error(' 登录失败:', err);
                }
            );
        },
        
        getTypeList() {
                var b = app.globalData.api_root + 'Microseries/getIndexTypeList';
                var e = app.globalData.getCache('userinfo');
                var params = new Object();
                params.openid = e.openid;
                http.POST(b, {
                    params: params,
                    success: (res) => {
                        console.log(res);
                        var type = res.data.type;
                        type.unshift({
                            id: 0,
                            name: '全部'
                        });
                        this.type_list = type;
                        this.type_id = type[0].id;
                        this.getIndexList();
                    },
                    fail: () => {
                        uni.showModal({
                            title: '提示',
                            content: '网络繁忙，请稍候重试！',
                            showCancel: false,
                            success: function (res) { }
                        });
                    }
                });
        },
        getRandomIndexList() {
                var b = app.globalData.api_root + 'Microseries/getRandomIndex';
                var e = app.globalData.getCache('userinfo');
                var params = new Object();
                params.openid = e.openid;
                http.POST(b, {
                    params: params,
                    success: (res) => {
                        console.log(res);
                        this.videoList = res.data.list;
                    },
                    fail: () => {
                        uni.showModal({
                            title: '提示',
                            content: '网络繁忙，请稍候重试！',
                            showCancel: false,
                            success: function (res) { }
                        });
                    }
                });
        },
        getIndexList() {
            var b = app.globalData.api_root + 'Microseries/getIndex';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.openid = e.openid;
            params.type_id = this.type_id;
            params.page = this.page;
            params.content = this.content;
            http.POST(b, {
                params: params,
                success: (res) => {
                    this.index_list.push(...res.data.list);
                    this.config = res.data.config;
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) { }
                    });
                }
            });
        },

        tabSelect(e) {
            this.TabCur = e.currentTarget.dataset.key;
            this.type_id = e.currentTarget.dataset.id;
            this.scrollLeft = (e.currentTarget.dataset.key - 1) * 60;
            this.index_list = [];
            this.page = 1;
            this.getIndexList();
        },
        /**
         * 切换视频
         */
        changeSwiper: function (e) {
            if (e.detail.source == 'touch') {
                // 手动
                let data = this;
                // swiper滑到的位置
                let swiperIndex = e.detail.current;
                // 当前item的位置
                let current = data.current;
                console.log();
                if (typeof this.videoList[current + 2] == 'undefined') {
                    var key = {
                        url: 'https://aweme.snssdk.com/aweme/v1/playwm/?video_id=v0200f330000bqk0juej5ughp5d7esc0&ratio=720p&line=0',
                        data: {
                            staff: '林阿姨',
                            staffMes: '5年从业经验，妥善照顾好每一位新生儿妈妈'
                        }
                    };
                    var list = this.videoList;
                    list.push(key);
                    this.videoList = list;
                    this.current = swiperIndex;
                    this.isPlaying = true;
                    this.playVideo();
                } else {
                    this.playVideo();
                    this.current = swiperIndex;
                    this.isPlaying = true;
                }
                // 如果没有切换就不执行其他操作
                //   if (current == swiperIndex) {
                //     return
                //   }

                //console.log("swiperIndex: " + swiperIndex)
                //console.log("-========>>>>>>  changeSwiper")

                // 播放视频
            }
        },

        /**
         * 切换视频后 播放视频
         */
        playVideo: function (e) {
            this.videoContext.play();
        },
        /**
         * 手动 暂停|播放
         */
        playOrPause: function (e) {
            let data = this;
            if (data.isPlaying) {
                this.videoContext.pause();
            } else {
                this.videoContext.play();
            }
            let isPlayingTemp = !data.isPlaying;
            this.isPlaying = isPlayingTemp;
        },
        /**
         * 开始播放
         */
        eventPlay(e) {
            console.log('-========>>>>>>  eventPlay 开始播放');
            this.rzindex = -1;
        },
        /**
         * 播放进度变化
         */
        timeupdate(e) {
            // console.log("-========>>>>>>  timeupdate 时间")
            let percent = (e.detail.currentTime / e.detail.duration) * 100;
            // console.log(percent)
        },

        /**
         * 视频缓冲
         */
        videoWaiting(e) {
            // console.log(e)
            console.log('-========>>>>>>  videoWaiting');
        },
        /**
         * 加载元数据
         */
        loadedmetadata(e) {
            console.log('-========>>>>>>  loadedmetadata');
            this.isPlaying = true;
        },
        /**
         * 加载进度变化时触发，只支持一段加载
         */
        progress(e) {
            console.log('-========>>>>>>  progress');
            console.log(e.detail.buffered);
        },
        open_mode(d) {
            this.videoContext.pause();
            this.isPlaying = false;
            uni.navigateTo({
                url: '/yl_welore/pages/packageC/theatre_video/index?id=' + d.currentTarget.dataset.id
            });
            // var animBackCollect = wx.createAnimation({
            //     duration: 200,
            //     timingFunction: 'linear'
            // })
            // animBackCollect.height('50%').step();
            // this.setData({
            //     animBack: animBackCollect.export(),
            //     select: true
            // })
        },

        hideModal() {
            var animBackCollect = uni.createAnimation({
                duration: 200,
                timingFunction: 'linear'
            });
            animBackCollect.height('100%').step();
            this.animBack = animBackCollect.export();
            this.select = false;
        },
        open_url(d) {
            console.log(d);
            var type = d.currentTarget.dataset.type;
            if (type == 1) {
                uni.navigateTo({
                    url: '/yl_welore/pages/packageC/theatre_my/index'
                });
            }
            if (type == 2) {
                uni.navigateTo({
                    url: '/yl_welore/pages/packageC/theatre_video/index?id=' + d.currentTarget.dataset.id
                });
            }
            if (type == 3) {
                uni.navigateTo({
                    url: '/yl_welore/pages/packageC/theatre_love/index'
                });
            }
            if (type == 4) {
                uni.navigateTo({
                    url: '/yl_welore/pages/packageC/theatre_history/index'
                });
            }
            if (type == 5) {
                uni.navigateTo({
                    url: '/yl_welore/pages/packageC/all_agreement/index?type=1'
                });
            }
            if (type == 6) {
                uni.navigateTo({
                    url: '/yl_welore/pages/packageC/all_agreement/index?type=2'
                });
            }
        }
    }
};
</script>
<style>
.page {
    background-image: linear-gradient(to bottom, #ffedc7 0%, #fffdf9 50%, #fffdf8 100%);
    height: 100vh;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: -1;
}

.page-1 {
    background-color: #000000;
    height: 100vh;
    position: fixed;
    top: 0;
    width: 100%;
}

.def-text {
    color: #999999;
    font-size: 30rpx;
}

.nv-text {
    position: relative;
    color: #000000;
    font-size: 34rpx;
    font-weight: 600;
}

.cut {
    left: 20rpx;
    position: absolute;
    width: 50rpx;
    height: 10rpx;
    background-image: linear-gradient(to left, transparent 0%, #ff9a9d4f 50%, #ff9a9e 100%);
    margin: 0 auto;
    bottom: 8rpx;
    border-radius: 10rpx 0rpx 0rpx 10rpx;
}

/* pages/cssCase/nTabbar/index.wxss */

page {
    height: 100%;
    /* 背景颜色 */
    --color: #ffffff;
    /* 菜单选中的背景色 */
    --bg: orange;
    /* 每个菜单的宽度 */
    --w: 250rpx;
    /* 页面总宽度 */
    --t: 750rpx;
    /* 菜单选中的圆形背景宽度 */
    --c: 90rpx;
    /* 设置底部安全距离 */
    /* 底部高度为120rpx 选中圆形高度为120rpx 底边距应该为底部高度+ 选中圆形高度的一半 即 120+60=180 */
}

.tabbar-box {
    position: fixed;
    left: 0;
    bottom: 0;
    z-index: 1;
    background: #fff;
    width: var(--t);
    border-radius: 10rpx 10rpx 0 0;
    display: flex;
    align-items: center;
    justify-content: center;
    /* 设置ios底部安全距离 */
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
}

.menu-item {
    display: flex;
    align-items: center;
    justify-content: center;
    /* 竖向垂直居中 */
    flex-direction: column;
    width: var(--w);
    height: 60rpx;
    position: relative;
}

.menu-item .iconfont {
    font-size: 60rpx;
    /* 添加过渡效果 */
    transition: all 0.5s;
}

.menu-item text {
    font-size: 26rpx;
    font-weight: bold;
    color: #222;
    position: absolute;
    transform: translateY(50rpx);
    /* 添加过渡效果 */
    transition: all 0.5s;
    /* 设置默认不显示 */
    opacity: 0;
}

/* 设置菜单选中样式 */
.menu-item.active .iconfont {
    transform: translateY(-15rpx);
}

.menu-item.active text {
    opacity: 1;
    transform: translateY(42rpx);
}

/* 菜单选中圆形样式 */
.active-tabbar-box {
    width: var(--c);
    height: var(--c);
    background: var(--bg);
    position: absolute;
    border-radius: 50%;
    box-sizing: border-box;
    border: 10rpx solid var(--color);
    --left-pad: calc(var(--t) - (3 * var(--w)));
    left: calc((var(--left-pad) / 2) + (var(--w) / 2 - (var(--c) / 2)));
    top: calc(-50% + constant(safe-area-inset-bottom) / 2);
    top: calc(-50% + env(safe-area-inset-bottom) / 2);
    z-index: -1;
    /* 添加过渡效果 */
    transition: all 0.5s;
    /* 设置选中偏移 */
    transform: translateX(calc(var(--w) * var(--n)));
}

/* 菜单选中圆形样式添加前后圆滑凹凸 */
.active-tabbar-box::after,
.active-tabbar-box::before {
    content: '';
    position: absolute;
    top: 69%;
    width: 30rpx;
    height: 30rpx;
    background: transparent;
}

.active-tabbar-box::before {
    left: -33rpx;
    border-radius: 0 30rpx 0 0;
    box-shadow: 0 -15rpx 0 0 var(--color);
}

.active-tabbar-box::after {
    right: -33rpx;
    border-radius: 30rpx 0 0 0;
    box-shadow: 0 -15rpx 0 0 var(--color);
}

/* pages/video/video.wxss */
.root {
    width: 100%;
    height: 100%;
    background-color: #000000;
}

swiper {
    width: 100%;
    height: 100%;
}

swiper-item {
    width: 100%;
    height: 100%;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

swiper-item .action {
    position: absolute;
    bottom: 480rpx;
    right: 24rpx;
}

swiper-item .info {
    font-size: 14px;
    color: #ffffff;
    position: absolute;
    bottom: 0;
    right: 0;
    left: 0;
    width: 100%;
    height: auto;
    padding: 24rpx 0rpx 24rpx 24rpx;
    box-sizing: border-box;
    background-color: transparent;
    /* 浏览器不支持时显示 */
    background-image: linear-gradient(rgba(0, 0, 0, 0.01), rgba(0, 0, 0, 0.3));
}

.content {
    font-size: 28rpx;
    margin-bottom: 18rpx;
}

.checkBtn {
    width: 50%;
    height: 68rpx;
    line-height: 68rpx;
    text-align: center;
    font-size: 24rpx;
    border-radius: 8rpx;
    border: 1px solid #ffffff;
    margin: 0 auto;
}

swiper video {
    width: 100%;
    height: 90%;
}

.pause {
    width: 50px;
    height: 50px;
    position: absolute;
    top: calc(50% - 30px);
    left: calc(50% - 30px);
}

.cu-list.menu>.cu-item.arrow:before {
    color: #672917;
}
</style>
