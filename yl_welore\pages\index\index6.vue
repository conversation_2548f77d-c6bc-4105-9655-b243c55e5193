<template>
    <view style="padding: 30rpx; margin-bottom: 120px; min-height: 1200rpx">
        <!-- <view style="border-left: 2px solid #000000;padding-left: 10px;font-size: 16px;font-weight: 300;margin-bottom:20px;">
    首页</view> -->
        <view style="border-left: 1rpx solid #e2e6e9; border-right: 1rpx solid #e2e6e9">
            <view style="padding: 15px" v-for="(item, dataListindex) in new_list" :key="dataListindex">
                <view @tap="home_url" :data-index="dataListindex" :data-id="item.tory_id" data-k="2" style="display: inline-block">
                    <text style="margin: 5px; font-size: 15px; font-weight: 300">{{ item.realm_name }}</text>
                    <view style="height: 5px; background-color: #96cbc3; width: 85%; border-radius: 5px; margin-top: -6px"></view>
                </view>

                <view style="text-align: center; margin-bottom: 10px">
                    <view style="width: 35px; height: 35px; margin: 0 auto; position: relative">
                        <image
                            @tap="home_url"
                            :data-index="dataListindex"
                            data-k="1"
                            :data-user_id="item.user_id"
                            class="now_level"
                            :src="item.user_head_sculpture"
                            style="width: 70rpx; height: 70rpx; border-radius: 50%; vertical-align: middle; display: block; margin: 0 auto"
                        ></image>
                        <!-- <image wx:if="{{item.attr!=''}}" class="now_level" style="height: 25rpx;width: 25rpx;position: absolute;right:-1px;bottom:-3px;z-index:100;max-width:initial" src="{{item.attr.attest.at_icon}}"></image> -->
                    </view>
                    <view style="margin-left: 5px; font-size: 12px; color: #666666; font-weight: 300; margin-top: 5px; display: inline-block">
                        {{ item.user_nick_name }}
                    </view>
                    <image v-if="item.attr != ''" class="now_level" style="height: 35rpx; width: 35rpx; vertical-align: middle" :src="item.attr.attest.at_icon"></image>
                    <image
                        v-if="item.user_vip == 1 && item.user_id != 0"
                        :src="http_root + 'addons/yl_welore/web/static/applet_icon/vip.png'"
                        style="width: 30rpx; height: 30rpx; vertical-align: middle"
                    ></image>
                    <image v-if="item.user_id != 0" mode="heightFix" class="now_level" :src="item.level" style="height: 13px; margin-left: 3px; vertical-align: middle"></image>
                    <image
                        class="now_level"
                        mode="heightFix"
                        v-if="item.wear_merit && item.user_id != 0"
                        :src="item.wear_merit"
                        style="height: 13px; margin-left: 3px; vertical-align: middle"
                    ></image>
                    <text
                        v-if="item.check_qq == 'da' && item.user_id != 0"
                        style="background-color: #000000; color: #fff; padding: 2px 4px; border-radius: 3px; font-size: 10px; margin-left: 3px"
                    >
                        {{ $state.diy.qq_name }}主
                    </text>
                    <text
                        v-if="item.check_qq == 'xiao' && item.user_id != 0"
                        style="background-color: #4facfe; color: #fff; padding: 2px 4px; border-radius: 3px; font-size: 10px; margin-left: 5px"
                    >
                        管理
                    </text>
                    <view style="font-family: Helvetica Neue; font-size: 12px; margin-top: 10px">
                        {{ item.index6_time }}
                    </view>
                    <view
                        @tap="home_url"
                        :data-index="dataListindex"
                        data-k="3"
                        :data-type="item.study_type"
                        :data-id="item.id"
                        style="font-family: Pingfang SC; font-size: 16px; font-weight: 600; margin: 5px"
                    >
                        <rich-text :nodes="item.study_title"></rich-text>
                    </view>
                </view>

                <block v-if="item.study_type == 0 || item.study_type == 3">
                    <view @tap="home_url" :data-index="dataListindex" data-k="3" :data-type="item.study_type" :data-id="item.id" style="text-align: center">
                        <view v-if="item.image_part.length > 0" v-for="(img, img_index) in item.image_part" :key="img_index">
                            <image :lazy-load="true" v-if="img_index == 0" :src="img" style="width: 85%; border-radius: 10px; height: 600rpx" mode="aspectFill"></image>
                        </view>
                        <view style="color: #666666; letter-spacing: 2px; line-height: 22px">
                            <rich-text class="text_num" :nodes="item.study_content"></rich-text>
                        </view>
                    </view>
                </block>

                <block v-if="item.study_type == 1">
                    <view
                        style="
                            margin: 0 auto;
                            overflow: hidden;
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            height: 170rpx;
                            width: 90%;
                            background-color: #f6f7f7;
                            border: 1px solid #f0f0f0;
                            border-radius: 10rpx;
                        "
                    >
                        <view
                            :style="
                                'background-image: url(' +
                                item.user_head_sculpture +
                                ');background-size: cover;background-position: center;width: 170rpx;background-color: #000;height: 170rpx;'
                            "
                        >
                            <view class="audioOpen" @tap="play" v-if="!item.is_voice" :data-key="dataListindex" :data-vo="item.study_voice">
                                <text style="color: #ffffff; font-size: 15px" class="cicon-play-arrow"></text>
                            </view>
                            <view class="audioOpen" @tap="stop" v-if="item.is_voice" :data-key="dataListindex" :data-vo="item.study_voice">
                                <text style="color: #ffffff; font-size: 15px" class="cicon-pause"></text>
                            </view>
                        </view>
                        <view style="width: 75%; padding: 20rpx">
                            <view style="display: flex; justify-content: space-between; align-items: center">
                                <view style="font-size: 28rpx; color: #555555; font-weight: 600">{{ item.user_nick_name }}上传的音乐</view>
                                <view class="times">{{ item.starttime }}</view>
                            </view>
                            <view style="display: flex; justify-content: space-between; align-items: center; margin-top: 20rpx">
                                <view style="font-size: 24rpx; color: #999">{{ item.user_nick_name }}</view>
                                <view>
                                    <slider style="width: 170rpx" @change="sliderChange" block-size="12px" step="1" :value="item.offset" :max="item.max" selected-color="#4c9dee" />
                                </view>
                            </view>
                        </view>
                    </view>
                </block>

                <block v-if="item.study_type == 2">
                    <video
                        @play="by_url"
                        :data-k="dataListindex"
                        :id="'myVideo' + item.id"
                        :src="item.study_video"
                        style="max-height: 380rpx; border-radius: 10px; margin: 0 auto; width: 100%"
                    ></video>
                    <!-- <view wx:if="{{item.is_buy==1}}" class="bg-cyan padding radius text-center shadow-blur light"
          style='position:relative;margin:0 auto;height:180px;z-index:100;overflow:hidden;border-radius:5px;font-size:16px;'>
          <text class="cuIcon-videofill lg text-white"
                    style="font-size:40px;position:absolute;text-align: center;left: 44%;bottom:37%;"></text>
        </view> -->
                </block>

                <block v-if="item.study_type == 4 || item.study_type == 5">
                    <view class="shadow-warp" style="margin-top: 15px; background-color: #f8f8f8">
                        <view style="padding: 15px; text-align: center">
                            <view
                                @tap.stop.prevent="home_url"
                                :data-index="dataListindex"
                                data-k="3"
                                :data-type="item.study_type"
                                :data-id="item.id"
                                style="font-size: 15px; font-weight: 600"
                            >
                                <text v-if="item.study_type == 4">（单选）</text>
                                <text v-if="item.study_type == 5">（多选）</text>
                                <rich-text class="text_num" v-if="item.study_title != ''" :nodes="item.study_title"></rich-text>
                            </view>
                            <view style="height: 10px"></view>
                            <view style="position: relative" v-if="vo_index < 3" v-for="(vo_item, vo_index) in item.vo" :key="vo_index">
                                <view
                                    style="width: 95%; height: 40px; border-radius: 5px; line-height: 40px; margin: 5px auto"
                                    class="text_num bg-white"
                                    @tap.stop.prevent="dian_option"
                                    :data-id="vo_item.id"
                                    :data-key="dataListindex"
                                    :data-index="vo_index"
                                >
                                    <view class="text-cut" style="z-index: 3; position: relative; width: 70%; margin: 0 auto">
                                        {{ vo_item.ballot_name }}
                                    </view>
                                    <text
                                        v-if="voi_item == vo_item.id"
                                        :style="'position: absolute;right: ' + (item.is_vo_check > 0 ? 90 : 7) + '%;z-index:3;top: 0;'"
                                        class="cuIcon-check lg text-green"
                                        v-for="(voi_item, index) in item.vo_id"
                                        :key="index"
                                    ></text>
                                    <text v-if="item.is_vo_check > 0" style="z-index: 3; position: absolute; right: 40rpx; top: 0">
                                        {{ vo_item.voters }}
                                    </text>
                                </view>

                                <view
                                    v-if="item.is_vo_check > 0"
                                    class="cu-progress radius sm"
                                    style="position: absolute; z-index: 1; left: 0; right: 0; top: 0; width: 95%; height: 40px; margin: 0 auto; background-color: #ffffff"
                                >
                                    <view :style="'width:' + vo_item.ratio + '%;background-image: linear-gradient(120deg, #a1c4fd 0%, #c2e9fb 100%);'"></view>
                                </view>
                            </view>
                            <view
                                @tap.stop.prevent="home_url"
                                :data-index="dataListindex"
                                data-k="3"
                                :data-type="item.study_type"
                                :data-id="item.id"
                                v-if="item.vo.length > 3"
                                style="width: 95%; height: 40px; border-radius: 5px; line-height: 40px; margin: 5px auto"
                                class="text_num bg-white"
                            >
                                查看全部选项
                                <text class="cuIcon-right lg text-gray"></text>
                            </view>
                        </view>
                        <view v-if="item.vote_deadline != '' && item.vote_deadline != 0 && item.vote_deadline != -1" style="font-weight: 300; margin-left: 46rpx">
                            截止时间：{{ item.vote_deadline }}
                        </view>
                        <view v-if="item.vote_deadline == -1" style="font-weight: 300; margin-left: 46rpx">投票已截止</view>
                        <view class="flex align-end" style="padding: 10px 0px">
                            <view class="flex-sub">
                                <view style="font-weight: 300; margin-left: 46rpx">参与人数：{{ item.vo_count }}</view>
                            </view>
                            <view class="flex-sub">
                                <button
                                    @tap.stop.prevent="vote_do"
                                    :data-index="vo_index"
                                    :data-key="dataListindex"
                                    v-if="item.vo_id.length > 0 && item.is_vo_check == 0"
                                    style="font-weight: 300; float: right; margin-right: 46rpx"
                                    class="cu-btn bg-grey round sm"
                                >
                                    投票
                                </button>
                            </view>
                        </view>
                    </view>
                </block>

                <view
                    v-if="item.gambit_id"
                    @tap="gambit_list"
                    :data-id="item.gambit_id"
                    style="display: inline-block; background-color: #f0f0f2; border-radius: 20px; padding: 3px 10px; float: left; margin: 22px 10px"
                >
                    <text style="font-size: 11px; color: #028774"># {{ item.gambit_name }} #</text>
                </view>

                <view
                    v-if="item.red == 1 || item.is_buy == 1 || item.study_type == 3"
                    style="display: inline-block; background-color: #f0f0f2; border-radius: 20px; padding: 3px 10px; float: left; margin: 22px 10px"
                >
                    <text v-if="item.red == 1 && version == 0" style="font-size: 11px; color: #b1b1b3">福利</text>
                    <text v-if="item.is_buy == 1 && version == 0" style="font-size: 11px; color: #b1b1b3">订阅</text>
                    <text v-if="item.study_type == 3" style="font-size: 11px; color: #b1b1b3">活动</text>
                </view>

                <view
                    @tap="parseEventDynamicCode($event, item.is_buy == 1 ? '' : 'add_zan')"
                    :data-id="item.id"
                    :data-key="dataListindex"
                    style="display:flex;justify-content: space-between;align-items: centerbackground-color:#f0f0f2;border-radius: 20px;padding: 3px 5px;float: right;margin: 20px 10px;"
                >
                    <view>
                        <image
                            class="now_level"
                            v-if="item.is_info_zan == false"
                            :src="http_root + 'addons/yl_welore/web/static/mineIcon/index6/no.png'"
                            mode="widthFix"
                            style="width: 40rpx; vertical-align: middle; margin: 0px 5px"
                        ></image>
                    </view>
                    <view>
                        <image
                            class="now_level"
                            v-if="item.is_info_zan == true"
                            :src="http_root + 'addons/yl_welore/web/static/mineIcon/index6/yes.png'"
                            mode="widthFix"
                            style="width: 40rpx; vertical-align: middle; margin: 0px 5px"
                        ></image>
                    </view>
                    <view>
                        <text style="font-size: 11px; color: #999999">{{ item.info_zan_count_this > 10000 ? item.info_zan_count : item.info_zan_count_this }}</text>
                    </view>
                    <view>
                        <image
                            @tap.stop.prevent="home_url"
                            :data-index="dataListindex"
                            data-k="3"
                            :data-type="item.study_type"
                            :data-id="item.id"
                            :src="http_root + 'addons/yl_welore/web/static/mineIcon/index6/hui.png'"
                            mode="widthFix"
                            style="width: 40rpx; vertical-align: middle; margin: 0px 5px"
                        ></image>
                    </view>
                    <view>
                        <text style="font-size: 11px; color: #999999">{{ item.study_repount }}</text>
                    </view>
                </view>

                <view style="clear: both; height: 0"></view>

                <view style="height: 1px; background-color: #f0f0f2; width: 92%; margin: 0 auto"></view>

                <view style="padding: 30rpx" v-if="dataListindex % ad_info.isolate == 0 && dataListindex != 0 && ad_info.adsper == 1">
                    <ad :unit-id="ad_info.adunit_id"></ad>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    props: ['data', 'compName'],
    computed: {
        new_list() {
            return this.$parent.$data.new_list;
        },
        dataListindex() {
            return this.$parent.$data.dataListindex;
        },
        item() {
            return this.$parent.$data.item;
        },
        http_root() {
            return this.$parent.$data.http_root;
        },
        $state() {
            return this.$parent.$data.$state;
        },
        order_time() {
            return this.$parent.$data.order_time;
        },
        version() {
            return this.$parent.$data.version;
        },
        img() {
            return this.$parent.$data.img;
        },
        img_index() {
            return this.$parent.$data.img_index;
        },
        vo_index() {
            return this.$parent.$data.vo_index;
        },
        vo_item() {
            return this.$parent.$data.vo_item;
        },
        voi_item() {
            return this.$parent.$data.voi_item;
        },
        index() {
            return this.$parent.$data.index;
        },
        ad_info() {
            return this.$parent.$data.ad_info;
        },
        di_msg() {
            return this.$parent.$data.di_msg;
        }
    },
    methods: {
        home_url(e) {
            this.$emit('home-url', e);
        },
        gambit_list(e) {
            this.$emit('gambit-list', e);
        },
        dian_option(e) {
            this.$emit('dian-option', e);
        },
        vote_do(e) {
            this.$emit('vote-do', e);
        },
        play(e) {
            this.$emit('play', e);
        },
        stop(e) {
            this.$emit('stop', e);
        },
        sliderChange(e) {
            this.$emit('slider-change', e);
        },
        home_pl(e) {
            this.$emit('home-pl', e);
        },
        parseEventDynamicCode(e, type) {
            this.$emit('dynamic-code', e, type);
        }
    }
};
</script>
<style></style>
