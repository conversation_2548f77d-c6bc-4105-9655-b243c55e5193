<template>
    <view>
        <cu-custom :isSearch="false" :isBack="false" bgColor="bg-white">
            <view v-if="elect_sheathe == 0 && design.shop_arbor == 1" slot="left_z">
                <view style="display: flex; justify-content: space-around; flex-direction: column; height: 100%">
                    <navigator url="/yl_welore/pages/user_smail/index" hover-class="none">
                        <view class="cu-avatar radius" style="background-color: transparent; width: 35px; height: 35px">
                            <image src="/static/yl_welore/style/icon/bgt.png" style="width: 35px; height: 35px"></image>
                            <view class="cu-tag badge">{{ user_info.user_male }}</view>
                        </view>
                    </navigator>
                </view>
            </view>
            <view slot="content" style="color: #000; font-weight: 600; font-size: 36rpx">个人中心</view>
        </cu-custom>
        <view>
            <view style="background: #fff; text-align: center; padding: 20px; overflow: hidden; position: relative">
                <view v-if="user_info.check_chou == 0">
                    <view
                        v-if="user_info.is_sign == 0"
                        :animation="animation_qian"
                        @tap="bid_qiandao"
                        style="
                            position: absolute;
                            right: 0%;
                            top: 5%;
                            background-color: #fc4c66;
                            color: #ffffff;
                            padding: 5px 10px;
                            border-top-left-radius: 20px;
                            border-bottom-left-radius: 20px;
                        "
                    >
                        每日签到
                    </view>
                    <view
                        v-if="user_info.is_sign == 1"
                        :animation="animation_qian_yes"
                        style="
                            position: absolute;
                            right: -100px;
                            top: 5%;
                            background-color: #999999;
                            color: #ffffff;
                            padding: 5px 10px;
                            border-top-left-radius: 20px;
                            border-bottom-left-radius: 20px;
                        "
                    >
                        已签到
                    </view>
                </view>
                <view
                    v-if="user_info.check_chou == 1"
                    @tap="user_url"
                    data-index="19"
                    style="
                        position: absolute;
                        right: 0%;
                        top: 5%;
                        background-color: #fc4c66;
                        color: #ffffff;
                        padding: 5px 10px;
                        border-top-left-radius: 20px;
                        border-bottom-left-radius: 20px;
                    "
                >
                    每日签到
                </view>
                <view
                    @tap="user_url"
                    data-index="18"
                    style="
                        z-index: 10;
                        position: absolute;
                        right: 0%;
                        top: 10.5%;
                        background-color: #ffca28;
                        color: #ffffff;
                        padding: 5px 10px;
                        border-top-left-radius: 20px;
                        border-bottom-left-radius: 20px;
                    "
                >
                    <text>任务中心</text>
                    <view
                        v-if="user_info.task_count > 0"
                        class="cu-tag badge"
                        style="left: -15rpx; border-radius: 50%; font-size: 24rpx; padding: 10rpx; height: 35rpx; width: 35rpx"
                    >
                        {{ user_info.task_count }}
                    </view>
                </view>
                <view @tap="my_home" class="cu-avatar round home" :style="'background-image:url(' + user_info.user_head_sculpture + ');'">
                    <view
                        style="z-index: 10"
                        v-if="user_info.tourist == 0"
                        :class="'cu-tag badge ' + (user_info.gender == 2 ? 'cuIcon-female bg-pink' : 'cuIcon-male bg-blue')"
                    ></view>
                    <image v-if="user_info.attest" :src="user_info.attest" style="z-index: 10; width: 40rpx; height: 40rpx; position: absolute; right: 0px; bottom: -5px"></image>
                    <image class="now_level" style="height: 145rpx; width: 145rpx; position: absolute; max-width: initial" :src="user_info.avatar_frame"></image>
                </view>
                <view style="font-size: 18px; font-weight: 600; letter-spacing: 1.5px; margin-top: 10px">
                    <text style="vertical-align: middle" :class="'course-name ' + user_info.special">{{ user_info.user_nick_name }}</text>
                    <image
                        mode="widthFix"
                        class="now_level"
                        v-if="user_info.wear_merit"
                        :src="user_info.wear_merit"
                        style="width: 20px; vertical-align: middle; margin-left: 3px"
                    ></image>
                </view>
                <view v-if="user_info.tourist == 0" @tap="my_level" style="margin-top: 10px">
                    <image class="now_level" :src="user_info.level_info.level_icon" mode="widthFix" style="width: 20px; vertical-align: middle"></image>
                    <text style="letter-spacing: 0.5px; font-size: 12px; color: #666; margin: 0px 0px 0px 10px; vertical-align: middle">{{ user_info.level_info.level_name }}</text>
                </view>
                <view v-if="user_info.tourist == 1">
                    <button
                        style="
                            color: #ffcc00;
                            font-size: 13px;
                            z-index: 200;
                            margin-top: 10px;
                            margin-top: 10px;
                            background-color: #00ccff;
                            color: #fff;
                            width: 100px;
                            padding: 5px 10px;
                            border-radius: 20px;
                        "
                        @tap="onGotUserInfo"
                        hover-class="none"
                    >
                        立即登录
                    </button>
                </view>
                <view class="flex">
                    <view class="flex-sub padding-sm">
                        <view @tap="user_url" data-index="2">
                            <view style="height: 27px; font-size: 20px; font-weight: 600">{{ user_info.user_track }}</view>
                            <view style="font-size: 14px; color: #a9a5a1">我的关注</view>
                        </view>
                    </view>
                    <view class="flex-sub padding-sm">
                        <view @tap="user_url" data-index="1" v-if="copyright.noble_arbor == 1">
                            <image
                                v-if="user_info.is_vip == 0"
                                :src="http_root + 'addons/yl_welore/web/static/applet_icon/novip.png'"
                                style="width: 25px; height: 25px; margin-top: -1px"
                            ></image>
                            <image
                                v-if="user_info.is_vip == 1"
                                :src="http_root + 'addons/yl_welore/web/static/applet_icon/vip.png'"
                                style="width: 25px; height: 25px; margin-top: -1px"
                            ></image>
                            <view style="font-size: 14px; color: #a9a5a1">{{ user_info.vip_end_time == 0 ? '未成为会员' : user_info.vip_end_time }}</view>
                        </view>
                    </view>
                    <view class="flex-sub padding-sm">
                        <view @tap="user_url" data-index="3">
                            <view style="height: 27px; font-size: 20px; font-weight: 600">{{ user_info.user_fs }}</view>
                            <view style="font-size: 13px; color: #a9a5a1">我的粉丝</view>
                        </view>
                    </view>
                </view>
                <view style="text-align: left; font-size: 15px; font-weight: 700; margin: 10px 0px">我的服务</view>
                <view class="flex">
                    <view class="flex-sub padding-sm margin-xs">
                        <view @tap="user_url" data-index="5" class="cu-item arrow" hover-class="none" style="border-bottom: 0px">
                            <view class="content">
                                <image :src="http_root + 'addons/yl_welore/web/static/mineIcon/user1/sc.png'" style="width: 25px; height: 25px"></image>
                                <view style="color: #b3b3b3; font-size: 13px; margin-top: 5px">我的收藏</view>
                            </view>
                        </view>
                    </view>
                    <view class="flex-sub padding-sm margin-xs">
                        <view @tap="user_url" data-index="6" class="cu-item arrow" hover-class="none">
                            <view class="content">
                                <image :src="http_root + 'addons/yl_welore/web/static/mineIcon/user1/quanzi.png'" style="width: 25px; height: 25px"></image>
                                <view style="color: #b3b3b3; font-size: 13px; margin-top: 5px">我的{{ design.landgrave }}</view>
                            </view>
                        </view>
                    </view>
                    <view class="flex-sub padding-sm margin-xs" v-if="version == 0 && copyright.shop_arbor == 1">
                        <view @tap="user_url" data-index="8" class="cu-item arrow" hover-class="none">
                            <view class="content">
                                <image :src="http_root + 'addons/yl_welore/web/static/mineIcon/user1/dui.png'" style="width: 25px; height: 25px"></image>
                                <view style="color: #b3b3b3; font-size: 13px; margin-top: 5px">我的订单</view>
                            </view>
                        </view>
                    </view>
                    <view class="flex-sub padding-sm margin-xs" v-if="copyright.wallet_arbor == 1 && version == 0 && user_info.conceal == 0">
                        <view @tap="user_url" data-index="9" class="cu-item arrow" hover-class="none">
                            <view class="content">
                                <image :src="http_root + 'addons/yl_welore/web/static/mineIcon/user1/money.png'" style="width: 25px; height: 25px"></image>
                                <view style="color: #b3b3b3; font-size: 13px; margin-top: 5px">我的钱包</view>
                            </view>
                        </view>
                    </view>
                </view>
                <view style="text-align: left; font-size: 15px; font-weight: 700; margin: 10px 0px">常用工具</view>
                <view class="grid col-4">
                    <view class="padding-sm" v-if="version == 0 && copyright.whisper_arbor == 1 && user_info.conceal == 0">
                        <view @tap="user_url" data-index="20" class="cu-item arrow" hover-class="none">
                            <view class="content" style="position: relative">
                                <view v-if="$state.slogin == 0" class="cu-tag badge" style="border-radius: 50%; font-size: 24rpx; padding: 10rpx; height: 35rpx; width: 35rpx">
                                    {{ user_info.secret }}
                                </view>
                                <image :src="http_root + 'addons/yl_welore/web/static/mineIcon/user1/secret.png'" style="width: 25px; height: 25px"></image>
                                <view style="color: #b3b3b3; font-size: 13px; margin-top: 5px">{{ design.custom_hiss_title ? design.custom_hiss_title : '树洞' }}</view>
                            </view>
                        </view>
                    </view>
                    <view class="padding-sm" v-if="version == 0">
                        <view @tap="user_url" data-index="10" class="cu-item arrow" hover-class="none">
                            <view class="content">
                                <image :src="http_root + 'addons/yl_welore/web/static/mineIcon/user1/yq.png'" style="width: 25px; height: 25px"></image>
                                <view style="color: #b3b3b3; font-size: 13px; margin-top: 5px">邀请好友</view>
                            </view>
                        </view>
                    </view>
                    <view class="padding-sm" v-if="version == 0 && copyright.shop_arbor == 1 && elect_sheathe == 1">
                        <view @tap="user_url" data-index="11" class="cu-item arrow" hover-class="none">
                            <view class="content">
                                <image :src="http_root + 'addons/yl_welore/web/static/mineIcon/user1/jf.png'" style="width: 25px; height: 25px"></image>
                                <view style="color: #b3b3b3; font-size: 13px; margin-top: 5px">{{ design.mall }}</view>
                            </view>
                        </view>
                    </view>
                    <view class="padding-sm">
                        <view @tap="user_url" data-index="17" class="cu-item arrow" hover-class="none">
                            <view class="content">
                                <image :src="http_root + 'addons/yl_welore/web/static/mineIcon/user1/yan.png'" style="width: 25px; height: 25px"></image>
                                <view style="color: #b3b3b3; font-size: 13px; margin-top: 5px">活动验证</view>
                            </view>
                        </view>
                    </view>
                    <view class="padding-sm">
                        <view @tap="user_url" data-index="16" class="cu-item arrow" hover-class="none">
                            <view class="content">
                                <image :src="http_root + 'addons/yl_welore/web/static/mineIcon/user1/kb.png'" style="width: 25px; height: 25px"></image>
                                <view style="color: #b3b3b3; font-size: 13px; margin-top: 5px">我的卡包</view>
                            </view>
                        </view>
                    </view>
                    <view v-if="version == 0" class="padding-sm">
                        <view @tap="user_url" data-index="15" class="cu-item arrow" hover-class="none">
                            <view class="content">
                                <image :src="http_root + 'addons/yl_welore/web/static/mineIcon/user1/lahei.png'" style="width: 25px; height: 25px"></image>
                                <view style="color: #b3b3b3; font-size: 13px; margin-top: 5px">黑名单</view>
                            </view>
                        </view>
                    </view>
                    <view class="padding-sm" v-if="version == 0 && copyright.tribute_arbor == 1">
                        <view @tap="user_url" data-index="7" class="cu-item arrow" hover-class="none">
                            <view class="content">
                                <image :src="http_root + 'addons/yl_welore/web/static/mineIcon/user1/lw.png'" style="width: 25px; height: 25px"></image>
                                <view style="color: #b3b3b3; font-size: 13px; margin-top: 5px">收到礼物</view>
                            </view>
                        </view>
                    </view>
                    <view class="padding-sm" v-if="version == 0 && copyright.engrave_arbor == 1">
                        <view @tap="user_url" data-index="21" class="cu-item arrow" hover-class="none">
                            <view class="content">
                                <image :src="http_root + 'addons/yl_welore/web/static/mineIcon/user1/name_p.png'" style="width: 25px; height: 25px"></image>
                                <view style="color: #b3b3b3; font-size: 13px; margin-top: 5px">身份铭牌</view>
                            </view>
                        </view>
                    </view>
                    <view class="padding-sm" v-if="version == 0 && copyright.travel_arbor == 1">
                        <view @tap="user_url" data-index="22" class="cu-item arrow" hover-class="none">
                            <view class="content">
                                <image :src="http_root + 'addons/yl_welore/web/static/mineIcon/user1/certification.png'" style="width: 25px; height: 25px"></image>
                                <view style="color: #b3b3b3; font-size: 13px; margin-top: 5px">身份认证</view>
                            </view>
                        </view>
                    </view>
                    <view class="padding-sm" v-if="version == 0 && copyright.feeling_arbor == 1 && copyright.feeling_stipulate == 0 && user_info.conceal == 0">
                        <view @tap="user_url" data-index="23" class="cu-item arrow" hover-class="none">
                            <view class="content">
                                <image :src="http_root + 'addons/yl_welore/web/static/mineIcon/user1/friends.png'" style="width: 25px; height: 25px"></image>
                                <view style="color: #b3b3b3; font-size: 13px; margin-top: 5px">{{ design.feel_title_em }}</view>
                            </view>
                        </view>
                    </view>
                    <view class="padding-sm" v-if="open_cord">
                        <view @tap="user_url" data-index="25" class="cu-item arrow" hover-class="none">
                            <view class="content">
                                <image :src="http_root + 'addons/yl_welore/web/static/mineIcon/user1/duihuan.png'" style="width: 25px; height: 25px"></image>
                                <view style="color: #b3b3b3; font-size: 13px; margin-top: 5px">卡密兑换</view>
                            </view>
                        </view>
                    </view>
                    <view class="padding-sm" v-if="version == 0 && open_wangpan">
                        <view @tap="user_url" data-index="26" class="cu-item arrow" hover-class="none">
                            <view class="content">
                                <image :src="http_root + 'addons/yl_welore/web/static/mineIcon/user1/wangpan.png'" style="width: 25px; height: 25px"></image>
                                <view style="color: #b3b3b3; font-size: 13px; margin-top: 5px">我的网盘</view>
                            </view>
                        </view>
                    </view>
                    <view class="padding-sm" v-if="version == 0 && open_lost">
                        <view @tap="user_url" data-index="28" class="cu-item arrow" hover-class="none">
                            <view class="content">
                                <image :src="http_root + 'addons/yl_welore/web/static/mineIcon/user1/lost.png'" style="width: 25px; height: 25px"></image>
                                <view style="color: #b3b3b3; font-size: 13px; margin-top: 5px">失物招领</view>
                            </view>
                        </view>
                    </view>
                    <view class="padding-sm" v-if="version == 0 && open_used">
                        <view @tap="user_url" data-index="31" class="cu-item arrow" hover-class="none">
                            <view class="content">
                                <image :src="http_root + 'addons/yl_welore/web/static/mineIcon/user1/ershou.png'" style="width: 25px; height: 25px"></image>
                                <view style="color: #b3b3b3; font-size: 13px; margin-top: 5px">{{ design.custom_title }}</view>
                            </view>
                        </view>
                    </view>
                    <view class="padding-sm" v-if="version == 0 && open_employ">
                        <view @tap="user_url" data-index="32" class="cu-item arrow" hover-class="none">
                            <view class="content">
                                <image :src="http_root + 'addons/yl_welore/web/static/mineIcon/user1/pin.png'" style="width: 25px; height: 25px"></image>
                                <view style="color: #b3b3b3; font-size: 13px; margin-top: 5px">{{ design.custom_title_em }}</view>
                            </view>
                        </view>
                    </view>
                    <view class="padding-sm" v-if="version == 0 && open_convenience">
                        <view @tap="user_url" data-index="29" class="cu-item arrow" hover-class="none">
                            <view class="content">
                                <image :src="http_root + 'addons/yl_welore/web/static/mineIcon/user1/bianmin.png'" style="width: 25px; height: 25px"></image>
                                <view style="color: #b3b3b3; font-size: 13px; margin-top: 5px">{{ design.easy_title_em }}</view>
                            </view>
                        </view>
                    </view>
                    <view class="padding-sm" v-if="version == 0 && open_convenience && user_info.assistant == 1">
                        <view @tap="user_url" data-index="30" class="cu-item arrow" hover-class="none">
                            <view class="content">
                                <image :src="http_root + 'addons/yl_welore/web/static/mineIcon/user1/tihuo.png'" style="width: 25px; height: 25px"></image>
                                <view style="color: #b3b3b3; font-size: 13px; margin-top: 5px">核销记录</view>
                            </view>
                        </view>
                    </view>
                    <view class="padding-sm" v-if="version == 0 && open_sweepstake">
                        <view @tap="user_url" data-index="33" class="cu-item arrow" hover-class="none">
                            <view class="content">
                                <image :src="http_root + 'addons/yl_welore/web/static/mineIcon/user1/xingyun.png'" style="width: 25px; height: 25px"></image>
                                <view style="color: #b3b3b3; font-size: 13px; margin-top: 5px">{{ design.sweepstake_title ? design.sweepstake_title : '幸运抽奖' }}</view>
                            </view>
                        </view>
                    </view>
                    <view class="padding-sm" v-if="version == 0 && copyright.short_drama_arbor == 1">
                        <view @tap="user_url" data-index="34" class="cu-item arrow" hover-class="none">
                            <view class="content">
                                <image :src="http_root + 'addons/yl_welore/web/static/mineIcon/user1/duanju.png'" style="width: 25px; height: 25px"></image>
                                <view style="color: #b3b3b3; font-size: 13px; margin-top: 5px">{{ design.micro_title ? design.micro_title : '短剧视频' }}</view>
                            </view>
                        </view>
                    </view>
                </view>
                <view style="text-align: left; font-size: 15px; font-weight: 700; margin: 10px 0px">更多服务</view>
                <view class="grid col-4">
                    <view class="padding-sm" v-if="open_account">
                        <view @tap="user_url" data-index="27" class="cu-item arrow" hover-class="none">
                            <view class="content">
                                <image :src="http_root + 'addons/yl_welore/web/static/mineIcon/user1/wechat.png'" style="width: 25px; height: 25px"></image>
                                <view style="color: #b3b3b3; font-size: 13px; margin-top: 5px">公众通知</view>
                            </view>
                        </view>
                    </view>
                    <view class="padding-sm">
                        <view @tap="user_url" data-index="12" class="cu-item arrow" hover-class="none">
                            <view class="content">
                                <image :src="http_root + 'addons/yl_welore/web/static/mineIcon/user1/kf.png'" style="width: 25px; height: 25px"></image>
                                <view style="color: #b3b3b3; font-size: 13px; margin-top: 5px">服务中心</view>
                            </view>
                        </view>
                    </view>
                    <view class="padding-sm" v-if="admin == 1">
                        <view @tap="user_url" data-index="13" class="cu-item arrow" hover-class="none">
                            <view class="content">
                                <image :src="http_root + 'addons/yl_welore/web/static/mineIcon/user1/shen.png'" style="width: 25px; height: 25px"></image>
                                <view style="color: #b3b3b3; font-size: 13px; margin-top: 5px">内容审核</view>
                            </view>
                        </view>
                    </view>
                    <view class="padding-sm">
                        <view @tap="user_url" data-index="14" class="cu-item arrow" hover-class="none">
                            <view class="content">
                                <image :src="http_root + 'addons/yl_welore/web/static/mineIcon/user1/gongsi.png'" style="width: 25px; height: 25px"></image>
                                <view style="color: #b3b3b3; font-size: 13px; margin-top: 5px">关于我们</view>
                            </view>
                        </view>
                    </view>
                </view>
                <view class="" style="min-height: 110px; margin-top: 20px; margin-bottom: 40px">
                    <view class="" style="word-break: break-all; text-align: center; color: var(--blue)">{{ copyright.title }}</view>
                    <view class="" style="word-break: break-all; text-align: center; font-size: 12px; margin-top: 10px">
                        {{ copyright.copyright }}
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    props: ['data', 'compName'],
    computed: {
        elect_sheathe() {
            return this.$parent.$data.elect_sheathe;
        },
        design() {
            return this.$parent.$data.design;
        },
        user_info() {
            return this.$parent.$data.user_info;
        },
        animationData() {
            return this.$parent.$data.animationData;
        },
        flag() {
            return this.$parent.$data.flag;
        },
        animationDataD() {
            return this.$parent.$data.animationDataD;
        },
        http_root() {
            return this.$parent.$data.http_root;
        },
        copyright() {
            return this.$parent.$data.copyright;
        },
        version() {
            return this.$parent.$data.version;
        },
       
        open_wangpan() {
            return this.$parent.$data.open_wangpan;
        },
        open_cord() {
            return this.$parent.$data.open_cord;
        },
        open_lost() {
            return this.$parent.$data.open_lost;
        },
        open_used() {
            return this.$parent.$data.open_used;
        },
        open_employ() {
            return this.$parent.$data.open_employ;
        },
        open_convenience() {
            return this.$parent.$data.open_convenience;
        },
        open_sweepstake() {
            return this.$parent.$data.open_sweepstake;
        },
        open_account() {
            return this.$parent.$data.open_account;
        },
        admin() {
            return this.$parent.$data.admin;
        },
        
    },
    methods:{
        my_home(e) {
            this.$emit('my_home', e);
        },
        my_level(e) {
            this.$emit('my_level', e);
        },
        onGotUserInfo(e) {
            this.$emit('onGotUserInfo', e);
        },
        bid_qiandao(e) {
            this.$emit('bid_qiandao', e);
        },
        user_url(e) {
            this.$emit('user_url', e);
        }
    }
};
</script>
<style></style>
