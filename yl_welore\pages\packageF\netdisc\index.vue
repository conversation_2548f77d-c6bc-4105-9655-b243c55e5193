<template>
    <view>
        <cu-custom bgColor="bg-white" :isSearch="false" :isBack="true">
            <view slot="backText"></view>
            <view slot="content" style="color: #000000; font-weight: 600; font-size: 36rpx">我的网盘</view>
        </cu-custom>
        <view class="flex padding-lr justify-start align-center">
            <view class="padding-sm margin-xs">
                <image :src="user_head_sculpture" style="border-radius: 50%; height: 80px; width: 80px"></image>
            </view>
            <view>
                <view style="font-size: 18px; font-weight: 600" class="font-yl-4">{{ user_nick_name }}</view>
                <view class="text-grey" style="font-size: 12px; margin: 15rpx 0px">容量（{{ info.user_use }} / {{ info.user_big }}）已使用{{ info.half_score }}</view>
                <view class="cu-progress round sm striped active">
                    <view class="bg-yellow" :style="'width:' + info.half_score + ';'"></view>
                </view>
            </view>
        </view>
        <view class="flex solid-bottom justify-center align-center" style="padding-bottom: 15px">
            <view @tap="get_open" class="cu-tag line-yellow radius" style="letter-spacing: 1px">网盘用户用户协议</view>
        </view>
        <view class="cu-bar search bg-white" style="margin-top: 10px">
            <view class="search-form round">
                <text class="cuIcon-search" style="margin-left: 15px"></text>
                <input type="text" @input="search_name" placeholder="搜索网盘文件" placeholder-style="margin-left:10px;letter-spacing:1px" confirm-type="search" />
            </view>
            <view class="action">
                <button @tap="search_do" class="cu-btn bg-yellow shadow-blur round text-white">搜索</button>
            </view>
        </view>

        <view class="flex justify-between align-center" @tap="open_inside" :data-index="index" v-for="(item, index) in list" :key="index">
            <view class="padding-sm margin-xs">
                <view class="flex align-center">
                    <view class="margin-xs">
                        <image :src="http_root + 'addons/yl_welore/web/static/file_icon/' + item.file_icon" style="height: 30px; width: 30px"></image>
                    </view>
                    <view class="margin-xs" style="margin-left: 15px">
                        <view class="font-yl-2 text_num_1" style="font-size: 14px; font-weight: 600">
                            <text>{{ item.file_name }}</text>
                            <text v-if="item.is_dir == 0">.{{ item.file_suffix }}</text>
                        </view>
                        <view style="font-size: 12px; margin-top: 8px; color: #9e9e9e">
                            <text>{{ item.add_time }}</text>
                            <text v-if="item.is_dir == 0" style="margin-left: 20px">{{ item.file_size }}</text>
                        </view>
                    </view>
                </view>
            </view>

            <view @tap.stop.prevent="openMod" :data-index="index" class="padding-sm margin-xs">
                <text class="cuIcon-more text-gray" style="font-size: 20px"></text>
            </view>
        </view>
        <view style="margin: 20px 0px 100px 0px" :class="'cu-load ' + (!di_msg ? 'loading' : 'over')"></view>
        <!-- 文件操作 -->
        <view :class="'cu-modal bottom-modal ' + (modalName == 'bottomModal' ? 'show' : '')" @tap="hideMod">
            <view class="cu-dialog" style="border-top-left-radius: 10px; border-top-right-radius: 10px" @tap.stop.prevent="maopao">
                <view class="cu-bar bg-white solid-bottom">
                    <view class="action">
                        <view class="flex justify-start align-center">
                            <view>
                                <image :src="http_root + 'addons/yl_welore/web/static/file_icon/' + file_info.file_icon" style="height: 30px; width: 30px"></image>
                            </view>
                            <view>
                                <text class="font-yl-2 text-grey text_num_1" style="font-size: 14px; margin-left: 30rpx">{{ file_info.file_name }}</text>
                            </view>
                        </view>
                        <view></view>
                    </view>
                </view>
                <view class="bg-white" style="text-align: left">
                    <view class="cu-list menu">
                        <view v-if="file_info.is_dir == 0" @tap="openFile" class="cu-item margin-tb">
                            <view class="content text-black">
                                <text class="cuIcon-down" style="font-size: 20px; vertical-align: middle"></text>
                                <text style="font-size: 12px">{{ file_info.suf == 1 ? '预览' : '复制下载链接' }}</text>
                            </view>
                        </view>
                        <view @tap="openForward" class="cu-item margin-tb">
                            <view class="content text-black">
                                <text class="cuIcon-forward" style="font-size: 20px; vertical-align: middle"></text>
                                <text style="font-size: 12px">分享</text>
                            </view>
                        </view>
                        <view v-if="file_info.is_dir == 0" @tap="openModMove" class="cu-item margin-tb">
                            <view class="content text-black">
                                <text class="cuIcon-exit" style="font-size: 20px; vertical-align: middle"></text>
                                <text style="font-size: 12px">移动</text>
                            </view>
                        </view>
                        <view @tap="openModRename" class="cu-item margin-tb">
                            <view class="content text-black">
                                <text class="cuIcon-edit" style="font-size: 20px; vertical-align: middle"></text>
                                <text style="font-size: 12px">重命名</text>
                            </view>
                        </view>
                        <view @tap="DelMod" class="cu-item margin-tb">
                            <view class="content text-black">
                                <text class="cuIcon-delete" style="font-size: 20px; vertical-align: middle"></text>
                                <text style="font-size: 12px">删除</text>
                            </view>
                        </view>
                    </view>
                </view>
                <view @tap="hideMod" style="height: 55px; line-height: 55px">取消</view>
            </view>
        </view>
        <!-- 文件操作 -->
        <!-- 重命名 -->
        <view :class="'cu-modal ' + (modalName == 'Rename' ? 'show' : '')">
            <view class="cu-dialog">
                <view class="cu-bar bg-white justify-end">
                    <view class="content" style="font-size: 18px; font-weight: 600">重命名</view>
                    <view class="action" @tap="hideMod">
                        <text class="cuIcon-close text-red"></text>
                    </view>
                </view>
                <view class="padding-sm">
                    <view class="cu-form-group">
                        <input placeholder="" @input="get_name" :value="file_info.file_name" maxlength="20" />
                        <text @tap="clean_name" class="cuIcon-roundclosefill" style="color: #d8d9dc"></text>
                    </view>
                </view>
                <view class="cu-bar bg-white justify-center">
                    <view class="action">
                        <button class="cu-btn line-yellow text-yellow" @tap="hideMod">取消</button>
                        <button class="cu-btn bg-yellow margin-left-xl" @tap="update_file_name">确定</button>
                    </view>
                </view>
            </view>
        </view>
        <!-- 重命名 -->
        <!-- 移动 -->
        <view :class="'cu-modal bottom-modal ' + (modalName == 'Move' ? 'show' : '')" @tap="hideMod">
            <view class="cu-dialog bg-white" style="height: 65%" @tap.stop.prevent="maopao">
                <view class="cu-bar bg-white solid-bottom">
                    <view style="font-size: 12px; width: 70%; padding-left: 26rpx" class="text_num_1 text-grey">移动文件：{{ file_info.file_name }} 到</view>
                    <view class="action text-blue" @tap="confirm_move" style="margin-right: 20px; font-weight: 600; font-size: 16px">确定移动</view>
                </view>
                <scroll-view :scroll-y="true" class="padding-sm margin-xs" style="height: 85%">
                    <view class="flex justify-between align-center solid-bottom" @tap="select_dir" :data-index="d_index" v-for="(item, d_index) in dir_list" :key="d_index">
                        <view class="flex align-center" style="padding: 20px 0px">
                            <view class="margin-xs">
                                <image :src="http_root + 'addons/yl_welore/web/static/file_icon/' + item.file_icon" style="height: 30px; width: 30px"></image>
                            </view>
                            <view class="margin-xs" style="margin-left: 15px; text-align: left">
                                <view class="font-yl-2 text_num_1" style="font-size: 14px; font-weight: 600">{{ item.file_name }}</view>
                                <view style="font-size: 12px; margin-top: 8px; color: #9e9e9e">
                                    <text>{{ item.id == 0 ? '根目录' : item.add_time }}</text>
                                </view>
                            </view>
                        </view>

                        <view style="padding-right: 40px">
                            <text style="font-size: 40rpx" v-if="dir_index == d_index" class="cuIcon-check text-green"></text>
                        </view>
                    </view>
                </scroll-view>
            </view>
        </view>
        <!-- 移动 -->
        <!-- 上传进度 -->
        <view :class="'cu-modal ' + (modalName == 'schedule' ? 'show' : '')">
            <view class="cu-dialog">
                <view class="cu-bar bg-white justify-end">
                    <view class="content">正在上传中...</view>
                </view>
                <view class="padding-xl">
                    <view class="flex">
                        <view class="cu-progress round">
                            <view class="bg-green" :style="'width:' + loading + '%;'"></view>
                        </view>
                        <text class="margin-left">{{ loading }}%</text>
                    </view>
                    <view style="margin-top: 15px">文件上传中，请不要关闭页面...</view>
                </view>
            </view>
        </view>
        <!-- 上传进度 -->
        <!-- 下载进度 -->
        <view :class="'cu-modal ' + (modalName == 'don_schedule' ? 'show' : '')">
            <view class="cu-dialog">
                <view class="cu-bar bg-white justify-end">
                    <view class="content">正在下载文件...</view>
                </view>
                <view class="padding-xl">
                    <view class="flex">
                        <view class="cu-progress round">
                            <view class="bg-green" :style="'width:' + loading + '%;'"></view>
                        </view>
                        <text class="margin-left">{{ loading }}%</text>
                    </view>
                    <view style="margin-top: 15px">文件下载中，请不要关闭页面...</view>
                </view>
            </view>
        </view>
        <!-- 下载进度 -->
        <view class="btn">
            <view @tap.stop.prevent="click" :class="'btn-main ' + btnAnimation">
                <text class="_icon-add"></text>
            </view>
        </view>
        <view :class="'mask1 ' + maskAnimation" @touchmove.stop.prevent="preventdefault"></view>
        <view class="menu-container" @touchmove.stop.prevent="preventdefault" v-if="!isShow">
            <view class="add_menu">
                <view class="menu-list">
                    <view class="menu-item" @tap="new_folder" style="width: 33%; animation-delay: 0.1s">
                        <image mode="aspectFill" :src="http_root + 'addons/yl_welore/web/static/file_icon/dir.png'" class="menu-icon"></image>
                        <text class="menu-name">新建文件夹</text>
                    </view>
                    <view class="menu-item" @tap="new_upload_local" style="width: 33%; animation-delay: 0.1s">
                        <image mode="aspectFill" :src="http_root + 'addons/yl_welore/web/static/file_icon/upload.png'" class="menu-icon"></image>
                        <text class="menu-name">上传本地文件</text>
                    </view>
                    <view class="menu-item" @tap="new_upload" style="width: 33%; animation-delay: 0.1s">
                        <image mode="aspectFill" :src="http_root + 'addons/yl_welore/web/static/file_icon/wx.png'" class="menu-icon"></image>
                        <text class="menu-name">上传微信文件</text>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
const app = getApp();
var http = require('../../../util/http.js');
var upload = require('../../../util/upload.js');
const yl_file = uni.getFileSystemManager();

export default {
    data() {
        return {
            isShow: true,
            http_root: app.globalData.http_root,
            modalName: '',
            user_nick_name: '',
            user_head_sculpture: '',
            info: {},
            file_info: {},
            page: 1,
            page_k: 1,
            search_page: 1,
            search: '',
            list: [],
            dir_count: 0,
            file_index: 0,
            dir_list: [],
            dir_page: 1,
            dir_index: null,
            loading: 0,
            is_search: false,
            onload: 0,
            btnAnimation: '',
            maskAnimation: '',
            di_msg: false
        }
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        console.log(options);
        var e = app.globalData.getCache('userinfo');
        this.get_my_file();
        this.user_nick_name = e.user_nick_name;
        this.user_head_sculpture = e.user_head_sculpture;
    },
    /**
     * 生命周期函数--监听页面显示
     */
    onShow(options) {
        console.log(options);
        var wangpan = app.globalData.__PlugUnitScreen('eae4f6cbbecdb89ea5a61ec602ec7000');
        if (!wangpan) {
            uni.navigateBack({
                delta: 1
            });
        }
        this.get_my_volume();
        if (this.onload == 1) {
            this.page = 1;
            this.page_k = 1;
            this.search = '';
            this.list = [];
            this.get_my_file();
            this.click();
        }
    },
    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh() {
        //模拟加载
        setTimeout(() => {
            uni.hideNavigationBarLoading(); //完成停止加载
            uni.stopPullDownRefresh(); //停止下拉刷新
        }, 1500);
        this.page = 1;
        this.page_k = 1;
        this.search = '';
        this.list = [];
        this.get_my_file();
    },
    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom() {
        if (this.dir_count >= 15) {
            this.page = this.page + 1;
        } else {
            this.page = this.page + 1;
            this.page_k = this.page_k + 1;
        }
        if (this.is_search) {
            this.get_my_search_list();
        } else {
            this.get_my_file();
        }
    },
    methods: {
        get_open() {
            uni.navigateTo({
                url: '/yl_welore/pages/packageF/netdisc_config/index'
            });
        },
        new_upload_local() {
            uni.navigateTo({
                url: '/yl_welore/pages/packageF/netdisc_local/index?pid=0'
            });
        },
        openForward() {
            var that = this;
            var info = this.file_info;
            if (info['file_status'] == 0) {
                uni.showModal({
                    title: '提示',
                    content: '文件已被屏蔽，现已被删除！',
                    showCancel: false,
                    success: (res) => {}
                });
                that.del_flie_do(info);
                return;
            }
            console.log(info);
            if (info['is_sell'] == 0) {
                uni.showToast({
                    title: '文件禁止分享',
                    icon: 'none'
                });
                return;
            }
            uni.navigateTo({
                url: '/yl_welore/pages/packageA/add/index?type=0&fa_class=0&name=&gambit_name=&gambit_id=0&file_id=' + this.file_info.id
            });
        },
        openFile() {
            var that = this;
            var info = this.file_info;
            if (info['file_status'] == 0) {
                uni.showModal({
                    title: '提示',
                    content: '文件已被屏蔽，现已被删除！',
                    showCancel: false,
                    success: (res) => {}
                });
                that.del_flie_do(info);
                return;
            }
            if (info.suf == 0) {
                uni.setClipboardData({
                    data: info.file_url,
                    success: (res) => {
                        that.modalName = '';
                    }
                });
                return;
            }
            var params = new Object();
            params.url = info.file_url;
            params.type = info.file_suffix;
            params.id = info.id;
            params.name = info.file_name;
            params.is_sell = info.is_sell;
            //var id=this.file_info['id'];
            console.log(params);
            upload.Access(params, yl_file, (a_res) => {
                console.log(a_res);
                this.modalName = 'don_schedule';
                upload.download_file(
                    params,
                    (res) => {
                        console.log(res);
                        if (res.errMsg == 'downloadFile:ok') {
                            //获取文件类型
                            params.url = res.tempFilePath;
                            upload.open_file(params, yl_file);
                        }
                    },
                    (res) => {
                        this.modalName = '';
                        console.log(res);
                    },
                    (res) => {
                        if (res.progress >= 100) {
                            this.modalName = '';
                        }
                        this.loading = res.progress;
                    }
                );
            });
        },
        search_name(d) {
            this.search = d.detail.value;
        },
        search_do() {
            this.page = 1;
            this.page_k = 1;
            this.list = [];
            this.is_search = true;
            if (this.search == '') {
                this.is_search = false;
                this.get_my_file();
            } else {
                this.is_search = true;
                this.get_my_search_list();
            }
        },
        get_my_search_list() {
            var b = app.globalData.api_root + 'Storage/get_my_search_list';
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.page = this.page;
            params.page_k = this.page_k;
            params.search = this.search;
            var allMsg = that.list;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    that.dir_count = res.data.page;
                    if (res.data.code == 0) {
                        for (var i = 0; i < res.data.info.length; i++) {
                            allMsg.push(res.data.info[i]);
                        }
                        that.list = allMsg;
                        if (res.data.info.length == 0 || allMsg.length < 10) {
                            that.di_msg = true;
                        }
                    } else {
                        uni.showModal({
                            title: '提示',
                            content: res.data.msg,
                            showCancel: false,
                            success: (res) => {}
                        });
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => {}
                    });
                }
            });
        },
        DelMod() {
            var that = this;
            var info = this.file_info;
            this.hideMod();
            var msg = '删除文件夹内的所有文件且不可恢复！！';
            var msg1 = '删除文件不可恢复！！';
            uni.showModal({
                title: '确定要删除「' + info.file_name + '」文件吗？',
                content: info['is_dir'] == 1 ? msg : msg1,
                success(res) {
                    if (res.confirm) {
                        that.del_flie_do(info);
                    }
                }
            });
        },
        del_flie_do(info) {
            var that = this;
            var b = app.globalData.api_root + 'Storage/del_folder_dir';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.id = info.id;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.code == 0) {
                        uni.showToast({
                            title: '删除成功！',
                            icon: 'none',
                            duration: 1000
                        });
                        that.hideMod();
                        let cartList = that.list;
                        cartList.splice(that.file_index, 1);
                        that.list = cartList;
                        that.get_my_volume();
                    } else {
                        uni.showModal({
                            title: '提示',
                            content: res.data.msg,
                            showCancel: false,
                            success: (res) => {}
                        });
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => {}
                    });
                }
            });
        },
        open_inside(d) {
            var info = this.list[d.currentTarget.dataset.index];
            if (info['is_dir'] != 1) {
                this.file_info = info;
                this.openFile();
                return;
            } else {
                uni.navigateTo({
                    url: '/yl_welore/pages/packageF/netdisc_inside/index?id=' + info.id
                });
            }
        },
        new_upload() {
            var that = this;
            this.modalName = 'schedule';
            var params = new Object();
            params.pid = 0;
            params.upload_limit_b = this.info.upload_limit_b;
            params.upload_limit = this.info.upload_limit;
            params.user_big_b = this.info.user_big_b;
            params.user_use_b = this.info.user_use_b;
            params.upload_type_limited = this.info.upload_type_limited;
            upload.upload_file(
                params,
                (res) => {
                    //成功
                    console.log(res);
                    if (res.status == 'success') {
                        that.modalName = '';
                        that.page_k = 1;
                        that.page = 1;
                        that.search = '';
                        that.list = [];
                        that.loading = 0;
                        uni.hideLoading();
                        uni.showToast({
                            title: '上传成功',
                            icon: 'none',
                            duration: 1000
                        });
                        that.get_my_file();
                        that.get_my_volume();
                        that.click();
                    } else {
                        uni.showToast({
                            title: res.msg,
                            icon: 'none',
                            duration: 2000
                        });
                        that.modalName = '';
                    }
                },
                (res) => {
                    //取消选择文件
                    console.log(res);
                    // if(res.){

                    // }
                    this.modalName = '';
                },
                (res) => {
                    //失败
                    console.log(res);
                    this.modalName = '';
                    uni.showToast({
                        title: '系统错误！',
                        icon: 'none',
                        duration: 2000
                    });
                },
                (res) => {
                    //失败
                    console.log(res);
                    this.modalName = '';
                },
                (res) => {
                    console.log(res);
                    if (res.progress >= 100) {
                        uni.showLoading({
                            title: '上传中...'
                        });
                    }
                    this.loading = res.progress;
                }
            );
        },
        new_folder() {
            var that = this;
            uni.showModal({
                title: '新建文件夹',
                content: '',
                editable: true,
                placeholderText: '文件名称',
                success(red) {
                    if (red.confirm) {
                        console.log(red);
                        if (red.content == '') {
                            uni.showToast({
                                title: '文件名不能为空！',
                                icon: 'none',
                                duration: 1000
                            });
                            return;
                        }
                        var b = app.globalData.api_root + 'Storage/new_folder_dir';
                        var e = app.globalData.getCache('userinfo');
                        var params = new Object();
                        params.token = e.token;
                        params.openid = e.openid;
                        params.name = red.content;
                        http.POST(b, {
                            params: params,
                            success: (res) => {
                                console.log(res);
                                if (res.data.code == 0) {
                                    uni.showToast({
                                        title: '创建成功！',
                                        icon: 'none',
                                        duration: 1000
                                    });
                                    setTimeout(() => {
                                        that.list = [];
                                        that.page = 1;
                                        that.page_k = 1;
                                        that.dir_index = null;
                                        that.click();
                                        that.get_my_file();
                                    }, 500);
                                } else {
                                    uni.showModal({
                                        title: '提示',
                                        content: res.data.msg,
                                        showCancel: false,
                                        success: (res) => {}
                                    });
                                }
                            },
                            fail: () => {
                                uni.showModal({
                                    title: '提示',
                                    content: '网络繁忙，请稍候重试！',
                                    showCancel: false,
                                    success: (res) => {}
                                });
                            }
                        });
                    }
                }
            });
            return;
        },
        confirm_move(d) {
            if (this.dir_index == null) {
                uni.showToast({
                    title: '请选择一个目录',
                    icon: 'none',
                    duration: 1500
                });
                return;
            }
            var dir_info = this.dir_list[this.dir_index];
            var b = app.globalData.api_root + 'Storage/move_file_dir';
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.id = this.file_info.id;
            params.move_id = dir_info.id;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.code == 0) {
                        uni.showToast({
                            title: '移动成功！',
                            icon: 'none',
                            duration: 1000
                        });
                        that.hideMod();
                        setTimeout(() => {
                            that.list = [];
                            that.page = 1;
                            that.page_k = 1;
                            that.dir_index = null;
                            that.get_my_file();
                        }, 500);
                    } else {
                        uni.showModal({
                            title: '提示',
                            content: res.data.msg,
                            showCancel: false,
                            success: (res) => {}
                        });
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => {}
                    });
                }
            });
        },
        select_dir(d) {
            var index = d.currentTarget.dataset.index;
            //var dir=this.dir_list[index];
            this.dir_index = index;
        },
        get_name(d) {
            var info = this.file_info;
            info.file_name = d.detail.value;
            this.file_info = info;
        },
        update_file_name() {
            var info = this.file_info;
            if (info['file_name'] == '') {
                uni.showToast({
                    title: '文件名不能为空！',
                    icon: 'none',
                    duration: 1500
                });
                return;
            }
            var b = app.globalData.api_root + 'Storage/update_file_name';
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.id = info.id;
            params.name = info.file_name;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.code == 0) {
                        uni.showToast({
                            title: '修改成功！',
                            icon: 'none',
                            duration: 1000
                        });
                        that.list[that.file_index].file_name = info.file_name;
                        that.hideMod();
                    } else {
                        uni.showModal({
                            title: '提示',
                            content: res.data.msg,
                            showCancel: false,
                            success: (res) => {}
                        });
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => {}
                    });
                }
            });
        },
        clean_name() {
            var info = this.file_info;
            info['file_name'] = '';
            this.file_info = info;
        },
        openModMove() {
            this.modalName = 'Move';
            this.dir_page = 1;
            this.dir_list = [];
            this.get_dir_list();
        },
        openModRename(d) {
            this.modalName = 'Rename';
        },
        openMod(d) {
            var index = d.currentTarget.dataset.index;
            var info = this.list[index];
            this.modalName = 'bottomModal';
            this.file_info = info;
            this.file_index = index;
        },
        hideMod() {
            this.modalName = '';
        },
        get_dir_list() {
            var b = app.globalData.api_root + 'Storage/get_dir';
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.dir_page = this.dir_page;
            var allMsg = that.dir_list;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    for (var i = 0; i < res.data.length; i++) {
                        allMsg.push(res.data[i]);
                    }
                    that.dir_list = allMsg;
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => {}
                    });
                }
            });
        },
        get_my_file() {
            var b = app.globalData.api_root + 'Storage/my_netdisc_belong';
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.page = this.page;
            params.page_k = this.page_k;
            params.search = this.search;
            var allMsg = that.list;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    that.dir_count = res.data.page;
                    if (res.data.code == 0) {
                        for (var i = 0; i < res.data.info.length; i++) {
                            allMsg.push(res.data.info[i]);
                        }
                        that.list = allMsg;
                        if (res.data.info.length == 0 || allMsg.length < 10) {
                            that.di_msg = true;
                        }
                    } else {
                        uni.showModal({
                            title: '提示',
                            content: res.data.msg,
                            showCancel: false,
                            success: (res) => {}
                        });
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => {}
                    });
                }
            });
        },
        get_my_volume() {
            var b = app.globalData.api_root + 'Storage/get_my_volume';
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.code == 0) {
                        that.info = res.data.info;
                    } else {
                        uni.showModal({
                            title: '提示',
                            content: res.data.msg,
                            showCancel: false,
                            success: (res) => {}
                        });
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => {}
                    });
                }
            });
        },
        click() {
            this.isShow = !this.isShow;
            if (this.isShow) {
                this.maskAnimation = 'maskClose';
                this.btnAnimation = 'menuClose';
            } else {
                this.maskAnimation = 'maskOpen';
                this.btnAnimation = 'menuOpen';
            }
        }
    }
};
</script>
<style>
page {
    background-color: #fefefe;
}

.mask1 {
    height: 48px;
    width: 48px;
    background-color: #ffffff;
    opacity: 0.85;
    z-index: 1000;
    border-radius: 750rpx;
    transform: scale(0);
    position: fixed;
    bottom: 0;
    left: 50%;
    margin-left: -48rpx;
}

.maskOpen {
    animation: maskO 0.3s both;
}

.maskClose {
    animation: maskC 0.3s both;
}

@keyframes maskO {
    0% {
        transform: scale(0);
    }

    20% {
        transform: scale(4);
    }

    40% {
        transform: scale(18);
    }

    60% {
        transform: scale(24);
    }
    80% {
        transform: scale(38);
    }
    100% {
        transform: scale(48);
    }
}

@keyframes maskC {
    0% {
        transform: scale(48);
    }

    25% {
        transform: scale(24);
    }

    100% {
        transform: scale(0);
    }
}

.btn {
    width: 100%;
    height: 48px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: fixed;
    z-index: 1005;
    bottom: calc(10rpx + env(safe-area-inset-bottom));
}

.btn-main {
    border-radius: 50%;
    z-index: 1005;
    height: 48px;
    font-size: 28px;
    width: 48px;
    line-height: 48px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    color: #fff;
    background: #fbbd08;
}

.menuOpen {
    animation: menuO 0.3s both;
}

.menuClose {
    animation: menuC 0.3s both;
}

@keyframes menuO {
    0% {
        transform: rotate(0deg);
        color: #fff;
        background: #fbbd08;
    }

    100% {
        transform: rotate(45deg);
        color: #000;
        background: #fff;
    }
}

@keyframes menuC {
    0% {
        transform: rotate(45deg);
        color: #000;
        background: #fff;
    }

    100% {
        transform: rotate(-0deg);
        color: #fff;
        background: #fbbd08;
    }
}

.menu-container {
    position: fixed;
    width: 100%;
    z-index: 1002;
    bottom: 0rpx;
}

.add_menu {
    padding-bottom: calc(48px + 40rpx + env(safe-area-inset-bottom));
}

.menu-list {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    width: 100%;
    padding-bottom: 15rpx;
}

.menu-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    animation: bounceInDown 0.45s linear both;
}

.menu-icon {
    width: 110rpx;
    height: 110rpx;
    margin-bottom: 15rpx;
    border-radius: 100%;
}

.menu-name {
    color: #333;
    font-size: 25rpx;
    font-weight: bold;
    margin-bottom: 10rpx;
}

@keyframes bounceInDown {
    0% {
        opacity: 0;
        transform: translateY(100%);
    }

    60% {
        transform: translateY(-10%);
    }

    80% {
        transform: translateY(10%);
    }

    100% {
        opacity: 1;
        transform: translateY(0%);
    }
}
</style>
