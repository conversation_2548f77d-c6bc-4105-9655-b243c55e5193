<template>
    <view>
        <view class="guard-container">
            <cu-custom :isSearch="false" :isBack="true">
                <view slot="backText">返回</view>
                <view slot="content" class="header-title">🛡️ 我的守护榜 🏆</view>
            </cu-custom>
            <view class="ranking-content">
                <view class="top-three-container">
                    <navigator :url="'/yl_welore/pages/packageB/my_home/index?id=' + info[0].id" hover-class="none"
                        v-if="info[0]">
                        <view class="first-place-container">
                            <view class="first-place-card">
                                <view class="crown-emoji">👑</view>
                                <image src="/static/yl_welore/style/icon/aj1.png" mode="widthFix"
                                    class="rank-badge first-badge"></image>
                                <image :src="info[0].user_head_sculpture"
                                    class="avatar first-avatar">
                                </image>
                                <view class="username first-username">{{ info[0].user_nick_name }}</view>
                                <view class="rank-label">🥇 冠军守护者</view>
                            </view>
                        </view>
                    </navigator>

                    <view class="second-third-container">
                        <view class="flex padding justify-between" style="gap: 30rpx;">
                            <view v-if="info[1]" class="second-place-card">
                                <navigator :url="'/yl_welore/pages/packageB/my_home/index?id=' + info[1].id"
                                    hover-class="none">
                                    <view class="medal-emoji">🥈</view>
                                    <image src="/static/yl_welore/style/icon/aj2.png" mode="widthFix"
                                        class="rank-badge second-badge"></image>
                                    <image :src="info[1].user_head_sculpture"
                                        class="avatar second-avatar">
                                    </image>
                                    <view class="username second-username">{{
                                        info[1].user_nick_name }}</view>
                                    <view class="rank-label-small">亚军守护</view>
                                </navigator>
                            </view>
                            <view v-if="info[2]" class="third-place-card">
                                <navigator :url="'/yl_welore/pages/packageB/my_home/index?id=' + info[2].id"
                                    hover-class="none">
                                    <view class="medal-emoji">🥉</view>
                                    <image src="/static/yl_welore/style/icon/aj3.png"
                                        class="rank-badge third-badge"></image>
                                    <image :src="info[2].user_head_sculpture"
                                        class="avatar third-avatar">
                                    </image>
                                    <view class="username third-username">{{
                                        info[2].user_nick_name }}</view>
                                    <view class="rank-label-small">季军守护</view>
                                </navigator>
                            </view>
                        </view>
                    </view>
                </view>

                <!-- 第4-10名优化设计 -->
                <view class="other-ranks-container">
                    <navigator :url="'/yl_welore/pages/packageB/my_home/index?id=' + item.id" hover-class="none"
                        v-for="(item, index) in info.slice(3)" :key="item.id">
                        <view class="rank-card">
                            <view class="rank-number">
                                <text class="rank-emoji">{{ getRankEmoji(index + 4) }}</text>
                                <text class="rank-text">No.{{ index + 4 }}</text>
                            </view>
                            <view class="user-info">
                                <image :src="item.user_head_sculpture" class="user-avatar"></image>
                                <view class="user-details">
                                    <view class="user-name">{{ item.user_nick_name }}</view>
                                    <view class="guard-status">🛡️ 守护中</view>
                                </view>
                            </view>
                            <view class="rank-decoration">✨</view>
                        </view>
                    </navigator>
                </view>

                <view class="bottom-spacing"></view>
            </view>
        </view>
        <view :class="'cu-load ' + (info.length == 0 ? 'over' : '')"></view>
    </view>
</template>

<script>
const app = getApp();
import http from '../../../util/http.js';

export default {
    /**
     * 页面的初始数据
     */
    data() {
        return {
            http_root: app.globalData.http_root,
            page: 1,
            info: [],
            id: null
        };
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        this.id = options.id;
        this.get_user_guard();
    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow() { },
    /**
 * 用户点击右上角分享
 */
    onShareAppMessage() {
        const forward = app.globalData.forward;
        // this.setData({
        //     show: false
        // });
        //设置数据
        if (forward) {
            return {
                title: forward.title,
                path: '/yl_welore/pages/packageB/my_home/index?id=' + this.id,
                imageUrl: forward.reis_img
            };
        } else {
            return {
                title: '您的好友给您发了一条信息',
                path: '/yl_welore/pages/packageB/my_home/index?id=' + this.id
            };
        }
    },
    methods: {
        /**
         * 获取贡献榜
         */
        get_user_guard() {
            const e = app.globalData.getCache('userinfo');
            const params = {
                token: e.token,
                openid: e.openid,
                uid: this.id,
                limit: 10
            };
            const b = app.globalData.api_root + 'User/get_user_guard';
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.status == 'success') {
                        this.info = res.data.info;
                    } else {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) { }
                    });
                }
            });
        },

        /**
         * 获取排名对应的emoji图标
         */
        getRankEmoji(rank) {
            const emojiMap = {
                4: '4️⃣',
                5: '5️⃣',
                6: '6️⃣',
                7: '7️⃣',
                8: '8️⃣',
                9: '9️⃣',
                10: '🔟'
            };
            return emojiMap[rank] || '🏅';
        }
    }
};
</script>
<style>
/* 主容器样式 */
.guard-container {
    background-image: linear-gradient(-225deg, #87CEEB 0%, #E0F6FF 48%, #F0F8FF 100%);
    min-height: 100vh;
    position: relative;
}

/* 头部标题样式 */
.header-title {
    color: #2c2b2b;
    font-weight: 600;
    font-size: 36rpx;
    text-shadow: 0 2px 4px rgba(255, 255, 255, 0.3);
}

/* 排行榜内容容器 */
.ranking-content {
    padding: 20rpx;
}

/* 前三名容器 */
.top-three-container {
    text-align: center;
    margin-bottom: 40rpx;
}

/* 第一名样式 */
.first-place-container {
    margin-bottom: 30rpx;
}

.first-place-card {
    width: 60%;
    margin: 0 auto;
    text-align: center;
    color: #2c3e50;
    position: relative;
    padding: 20rpx;
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.3), rgba(255, 255, 255, 0.4));
    border-radius: 20rpx;
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10rpx);
    border: 2rpx solid rgba(255, 215, 0, 0.4);
}

.crown-emoji {
    font-size: 60rpx;
    position: absolute;
    top: -30rpx;
    left: 50%;
    transform: translateX(-50%);
    animation: bounce 2s infinite;
    z-index: 100;
}

.first-badge {
    z-index: 10;
    height: 89.7px;
    width: 100px;
    position: absolute;
    top: 20rpx;
    left: 50%;
    transform: translateX(-50%);
}

.first-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    margin-top: 15rpx;
    border: 4rpx solid rgba(255, 215, 0, 0.8);
    box-shadow: 0 4rpx 16rpx rgba(255, 215, 0, 0.4);
}

.first-username {
    color: #2c3e50;
    font-size: 32rpx;
    font-weight: bold;
    margin-top: 20rpx;
    text-shadow: 0 2rpx 8rpx rgba(255, 255, 255, 0.8);
}

.rank-label {
    font-size: 24rpx;
    color: #ffd700;
    margin-top: 10rpx;
    font-weight: 600;
}

/* 第二、三名容器 */
.second-third-container {
    width: 100%;
    margin: 0 auto;
    text-align: center;
    color: #34495e;
}

.second-place-card, .third-place-card {
    width: 50%;
    position: relative;
    padding: 20rpx;
    background: linear-gradient(135deg, rgba(192, 192, 192, 0.3), rgba(255, 255, 255, 0.4));
    border-radius: 16rpx;
    box-shadow: 0 6rpx 24rpx rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(8rpx);
    border: 1rpx solid rgba(255, 255, 255, 0.4);
}

.third-place-card {
    background: linear-gradient(135deg, rgba(205, 127, 50, 0.3), rgba(255, 255, 255, 0.4));
    border: 1rpx solid rgba(205, 127, 50, 0.4);
}

.medal-emoji {
    font-size: 40rpx;
    position: absolute;
    top: -20rpx;
    left: 50%;
    transform: translateX(-50%);
    z-index: 100;
}

.second-badge, .third-badge {
    z-index: 10;
    width: 90px;
    height: 71px;
    position: absolute;
    top: 10rpx;
    left: 50%;
    transform: translateX(-50%);
}

.second-avatar, .third-avatar {
    width: 65px;
    height: 65px;
    border-radius: 50%;
    margin-top: 0rpx;
    border: 3rpx solid rgba(255, 255, 255, 0.6);
    box-shadow: 0 3rpx 12rpx rgba(0, 0, 0, 0.3);
}

.second-username, .third-username {
    color: #34495e;
    font-size: 28rpx;
    font-weight: 600;
    margin-top: 15rpx;
    text-shadow: 0 2rpx 6rpx rgba(255, 255, 255, 0.6);
}

.rank-label-small {
    font-size: 20rpx;
    color: #5a6c7d;
    margin-top: 8rpx;
}

/* 其他排名容器 */
.other-ranks-container {
    padding: 0 30rpx;
}

.rank-card {
    margin: 30rpx 0;
    padding: 20rpx;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.2));
    border-radius: 16rpx;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(5rpx);
    border: 1rpx solid rgba(255, 255, 255, 0.3);
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.rank-card:active {
    transform: scale(0.98);
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}

.rank-number {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-right: 20rpx;
    min-width: 60rpx;
}

.rank-emoji {
    font-size: 32rpx;
    margin-bottom: 5rpx;
}

.rank-text {
    font-size: 24rpx;
    color: #34495e;
    font-weight: 600;
}

.user-info {
    display: flex;
    align-items: center;
    flex: 1;
}

.user-avatar {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    border: 2rpx solid rgba(255, 255, 255, 0.4);
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}

.user-details {
    margin-left: 20rpx;
}

.user-name {
    font-size: 28rpx;
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 5rpx;
}

.guard-status {
    font-size: 22rpx;
    color: #5a6c7d;
}

.rank-decoration {
    position: absolute;
    right: 20rpx;
    font-size: 36rpx;
    opacity: 0.6;
}

.bottom-spacing {
    height: 100rpx;
}

/* 动画效果 */
@keyframes bounce {
    0%, 100% {
        transform: translateX(-50%) translateY(0);
    }
    50% {
        transform: translateX(-50%) translateY(-10rpx);
    }
}
</style>
