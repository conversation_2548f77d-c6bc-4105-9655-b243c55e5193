<template>
    <view>
        <cu-custom bgColor="bg-white" :isSearch="false" :isBack="true">
            <view slot="backText">返回</view>
            <view slot="content" style="color: #2c2b2b; font-weight: 600; font-size: 36rpx">活动规则</view>
        </cu-custom>

        <view style="margin: 10px; background-color: #fff; border-radius: 5px; padding-bottom: 20px">
            <view style="padding: 10px">
                <mp-html @linktap="linktap" :selectable="true" :copy-link="false" :lazy-load="true" :content="article" />
            </view>
        </view>
    </view>
</template>

<script>
const app = getApp();
const http = require('../../../util/http.js');
export default {
    components: {},
    data() {
        return {
            article: ''
        };
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        this.get_raffle();
    },
    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {},
    methods: {
        //获取活动
        get_raffle() {
            const b = app.globalData.api_root + 'Subscribe/get_raffle';
            const e = app.globalData.getCache('userinfo');
            const params = {
                token: e.token,
                openid: e.openid
            };
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.status == 'success') {
                        this.article = res.data.info.illustrate;
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => {}
                    });
                }
            });
        },

        linktap() {
            console.log('占位：函数 linktap 未声明');
        }
    }
};
</script>
<style></style>
