<template>
    <view>
        <Index1 v-if="$state.diy.mod.goods == 0" :parentData="currentInstance" @handleChangeScroll="handleChangeScroll" @get_url="get_url"></Index1>
        <Index2 v-if="$state.diy.mod.goods == 'dac2a920-6d36-3ba6-f6d7-582dac33f466'" :parentData="currentInstance" @handleChangeScroll="handleChangeScroll" @get_url="get_url"></Index2>
        <Index3 v-if="$state.diy.mod.goods == '773fc201-3510-4cc2-ba13-63afe5a79e7f'" :parentData="currentInstance" @handleChangeScroll="handleChangeScroll" @get_url="get_url"></Index3>

        <tabbar id="tabbar" v-if="$state.diy.elect_sheathe == 0" :tabbar="tabbar"></tabbar>
    </view>
</template>
<script>
import tabbar from '@/yl_welore/util/tabbarComponent/tabbar';
import Index1 from '@/yl_welore/pages/shell_mall/index1.vue';
import Index2 from '@/yl_welore/pages/shell_mall/index2.vue';
import Index3 from '@/yl_welore/pages/shell_mall/index3.vue';
const app = getApp();
var http = require('../../util/http.js');
export default {
    components: {
        tabbar,
        Index1,
        Index2,
        Index3
    },
    /**
     * 页面的初始数据
     */
    data() {
        return {
            currentInstance:this,
            http_root: app.globalData.http_root,
            tabbar: {},
            user_info: {},
            goods_current: 'goods',
            current_scroll: '0',
            isIpx: app.globalData.isIpx,
            nvabarData: {
                showCapsule: 1,
                //是否显示左上角图标
                height: app.globalData.height * 2 + 20
            },
            title: '',
            //导航栏 中间的标题
            page: 1,
            type_list: [],
            type_id: '',
            shop_list: [],
            di_msg: false,
            show: true,
            diy: {},
            isPopping: false,
            //是否已经弹出
            animPlus: {},
            //旋转动画
            animCollect1: {},
            animCollect: {},
            //item位移,透明度
            animTranspond: {},
            //item位移,透明度
            animInput: {},
            //item位移,透明
            animBack: {},
            version: 0,
            mod: {},
            user: {}
        };
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        var that = this;
        var design = uni.getStorageSync('is_diy');
        this.height = app.globalData.height;
        this.title = design.mall;
        this.page = 1;
        this.shop_list = [];
        this.di_msg = false;
        this.get_shop_type();
    },
    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {
        //wx.hideTabBar();
        app.globalData.editTabbar();
        var dd = uni.getStorageSync('is_diy');
        console.log(dd);

        // if (dd) {
        //   this.setData({
        //     //version: dd.version,
        //     mod: dd.mod,
        //     diy: dd,
        //   })
        //   app.editTabbar();
        // } else {
        //   this.get_diy();
        // }
        this.isPopping = false;
        var diy = getApp().globalData.store.getState().diy;
        console.log(diy);
        //{ { diy.elect_sheathe == 0 } }
        // if (diy['elect_sheathe'] == 0) {
        //   this.selectComponent('#tabbar').get_user();
        // }
    },
    /**
     * 加载下一页
     */
    onReachBottom() {
        this.page = this.page + 1;
        this.get_shop_list();
    },
    /**
     * 下拉刷新
     */
    onPullDownRefresh() {
        //模拟加载
        setTimeout(() => {
            uni.hideNavigationBarLoading(); //完成停止加载
            uni.stopPullDownRefresh(); //停止下拉刷新
        }, 1500);
        this.page = 1;
        this.shop_list = [];
        this.di_msg = false;
        this.get_shop_list();
    },
    /**
     * 用户点击右上角分享
     */
    onShareAppMessage() {
        var forward = app.globalData.forward;
        console.log(forward);
        if (forward) {
            return {
                title: forward.title,
                path: '/yl_welore/pages/index/index',
                imageUrl: forward.reis_img
            };
        } else {
            return {
                title: '您的好友给您发了一条信息',
                path: '/yl_welore/pages/index/index'
            };
        }
    },
    methods: {
        get_url(item) {
            var id = item.currentTarget.dataset.id;
            uni.navigateTo({
                url: '/yl_welore/pages/packageA/good_info/index?id=' + id
            });
        },
        //获取用户信息
        get_user_info() {
            var b = app.globalData.api_root + 'User/get_user_info';
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.status == 'success') {
                        this.user_info = res.data.info;
                    } else {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => {}
                    });
                }
            });
        },
        handleChangeScroll(detail) {
            var key = detail.currentTarget.dataset.key;
            console.log(this.type_list[key]);
            this.current_scroll = key;
            this.type_id = this.type_list[key].id;
            this.page = 1;
            this.shop_list = [];
            this.di_msg = false;
            this.get_shop_list();
        },
        /**
         * 商品类型
         */
        get_shop_type() {
            var b = app.globalData.api_root + 'User/get_shop_type';
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.uid = e.uid;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.status == 'success') {
                        if (res.data.info.length > 0) {
                            this.type_id = res.data.info[0].id;
                            this.get_shop_list();
                        }
                        this.type_list = res.data.info;
                        this.user = res.data.user;
                    } else {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => {}
                    });
                }
            });
        },
        /**
         * 商品列表
         */
        get_shop_list() {
            var b = app.globalData.api_root + 'User/get_shop_list';
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.type_id = this.type_id;
            params.page = this.page;
            var allMsg = this.shop_list;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.status == 'success') {
                        for (var i = 0; i < res.data.info.length; i++) {
                            allMsg.push(res.data.info[i]);
                        }
                        this.shop_list = allMsg;
                        if (res.data.info.length == 0 || allMsg.length < 6) {
                            this.di_msg = true;
                        }
                    } else {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => {}
                    });
                }
            });
        },
        _navback() {
            var pages = getCurrentPages();
            var Page = pages[pages.length - 1]; //当前页
            var prevPage = pages[pages.length - 2]; //上一个页面
            if (pages.length == 1) {
                this._backhome();
                return;
            }
            uni.navigateBack();
        },
        _backhome() {
            uni.switchTab({
                url: '/yl_welore/pages/user/index'
            });
        }
    }
}
</script>
<style>
._no_index5 {
    color: #9c9ea9;
}
._this_index5 {
    color: #000000;
}
._no_index5_1 {
    color: #000000;
}
._this_index5_1 {
    color: #ffffff;
}

page {
    background-color: #ffffff;
}
.di_msg_syle {
    padding-top: 20px;
}

.get_page {
    min-height: 60em;
    width: 100%;
    height: 100%;
    padding-bottom: 40px;
}

.back {
    background-color: transparent !important;
    position: relative;
}

.font_color {
    color: #000 !important;
    margin: 0px 20px;
}

.nav-wrap {
    width: 100%;
    top: 0;
    color: #000;
    z-index: 9999999;
}

/* 标题要居中 */

.nav-title {
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    font-size: 36rpx;
    color: #2c2b2b;
    font-weight: 600;
}

.nav-capsule {
    display: flex;
    align-items: center;
    margin-left: 20px;
    justify-content: space-around;
    border-radius: 50%;
    margin-top: 54px;
    z-index: 999999999;
}

.navbar-v-line {
    width: 1px;
    height: 32rpx;
    background-color: #f3f3f3;
}

.back-pre {
    width: 32rpx;
    height: 32rpx;
    margin-top: 11rpx;
    margin-left: 0rpx;
}

.class_img {
    width: 130rpx;
    height: 130rpx;
    border-radius: 100%;
}

.classify {
    height: 100%;
    margin: 25rpx;
    text-align: center;
    border-radius: 25rpx;
    box-sizing: border-box;
    display: inline-block;
    position: relative;
}
.img-style {
    height: 0rpx;
    width: 0rpx;
    position: absolute;
    right: 50%;
    opacity: 0;
    bottom: 0px;
}
.weui-tabbar_boo {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    position: fixed;
    z-index: 1;
    bottom: 0;
    width: 100%;
    border-top-left-radius: 30rpx;
    border-top-right-radius: 30rpx;
}

/**
     * 弹窗
     */
.show-btn {
    margin-top: 100rpx;
    color: #22cc22;
}

.modal-mask {
    width: 100%;
    height: 100%;
    position: fixed;
    top: 0;
    left: 0;
    background: #000;
    opacity: 0.5;
    overflow: hidden;
    z-index: 888;
    color: #fff;
}

.modal-dialog {
    width: 540rpx;
    position: fixed;
    top: 45%;
    left: 0;
    z-index: 999;
    background: #f9f9f9;
    margin: -180rpx 105rpx;
    border-radius: 10px;
}

.modal-title {
    padding-top: 50rpx;
    font-size: 36rpx;
    color: #030303;
    text-align: center;
}

.modal-content {
    padding: 20rpx 32rpx;
}

.modal-input {
    display: flex;
    background: #fff;
    border: 2rpx solid #ddd;
    border-radius: 4rpx;
    font-size: 28rpx;
}

.input {
    width: 100%;
    height: 82rpx;
    font-size: 28rpx;
    line-height: 28rpx;
    padding: 0 20rpx;
    box-sizing: border-box;
    color: #333;
}

input-holder {
    color: #666;
    font-size: 28rpx;
}

.modal-footer {
    display: flex;
    flex-direction: row;
    height: 86rpx;
    border-top: 1px solid #dedede;
    font-size: 34rpx;
    line-height: 86rpx;
}

.btn-cancel {
    width: 50%;
    color: #666;
    text-align: center;
    border-right: 1px solid #dedede;
}

.btn-confirm {
    width: 50%;
    color: #cc3333;
    text-align: center;
}

button::after {
    line-height: normal;
    font-size: 30rpx;
    width: 0;
    height: 0;
    top: 0;
    left: 0;
}

button {
    line-height: normal;
    display: block;
    padding-left: 0px;
    padding-right: 0px;
    background-color: rgba(255, 255, 255, 0);
    font-size: 30rpx;
    overflow: inherit;
}
</style>
