<template>
    <view>
        <cu-custom bgColor="bg-white" :isSearch="false" :isBack="$state.diy.elect_sheathe != 0">
            <view slot="backText">返回</view>
            <view slot="content" style="color: #2c2b2b; font-weight: 600; font-size: 36rpx">{{ title }}</view>
        </cu-custom>
        <view class="page" :style="'height: ' + (height * 2 + 20) + 'px'"></view>
        <scroll-view scroll-x class="nav" style="padding: 20px 0px 0px 20px; height: 65px; position: fixed; z-index: 100; background-color: #ffffff">
            <view :data-key="t_index" @tap="handleChangeScroll" class="cu-item" style="height: 60rpx; line-height: 60rpx" v-for="(item, t_index) in type_list" :key="t_index">
                <text :class="current_scroll == t_index ? '_this_index5' : '_no_index5'">{{ item.name }}</text>

                <view v-if="current_scroll == t_index" style="width: 50%; height: 2px; background-color: #55bbbc; margin-top: 18px; border-radius: 10px; margin: 0 auto"></view>
            </view>
        </scroll-view>
        <view style="padding: 10px 15px; margin-top: 55px">
            <view class="grid col-2">
                <block v-for="(item, index) in shop_list" :key="index">
                    <view class="padding-sm" @tap="get_url" :data-id="item.id">
                        <image class="now_level" :src="item.product_img[0]" mode="widthFix" style="width: 100%"></image>
                        <view style="font-size: 11px; font-weight: 400; letter-spacing: 1.5px; color: #383d53; margin-top: 5px">{{ item.product_synopsis }}</view>
                        <view style="font-size: 15px; font-weight: 400; letter-spacing: 1.5px; color: #3b3f55; margin-top: 5px">{{ item.product_name }}</view>
                        <view style="font-size: 14px; font-weight: 400; color: #54b9ba; margin-top: 15px">
                            <text :class="item.pay_type == 2 ? 'text-price' : ''">{{ item.product_price }}</text>
                            <text v-if="item.pay_type == 0 || item.pay_type == 1" style="vertical-align: bottom; font-size: 20rpx">
                                ({{ item.pay_type == 0 ? $state.diy.currency : $state.diy.confer }})
                            </text>
                        </view>
                        <view v-if="item.open_discount == 1" style="font-size: 13px; font-weight: 400; color: #cecfd4; margin-top: 8px">
                            <text style="font-size: 12px">会员价</text>
                            <text style="font-size: 11px; margin: 0px 10rpx">￥</text>
                            <text>{{ toFix(item.product_price * item.noble_discount) }}</text>
                        </view>
                    </view>
                </block>
            </view>
            <view style="clear: both; height: 0"></view>
            <view :class="'cu-load ' + (!di_msg ? 'loading' : 'over')"></view>
        </view>
    </view>
</template>

<script>
export default {
    props: ['data', 'compName'],
    computed: {
        $state() {
            return this.$parent.$data.$state;
        },
        title() {
            return this.$parent.$data.title;
        },
        type_list() {
            return this.$parent.$data.type_list;
        },
        current_scroll() {
            return this.$parent.$data.current_scroll;
        },
        shop_list() {
            return this.$parent.$data.shop_list;
        },
        di_msg() {
            return this.$parent.$data.di_msg;
        }
       
    },
    methods: {
        toFix(value) {
            var na = (parseInt(value * 100) / 100).toFixed(2);
            return na;
        },
        handleChangeScroll(e) {
            this.$emit('handleChangeScroll', e);
        },
        get_url(e) {
            this.$emit('get_url', e);
        },
    }
};
</script>
<style></style>
