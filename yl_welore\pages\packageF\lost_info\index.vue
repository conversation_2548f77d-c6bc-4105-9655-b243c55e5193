<template>
    <view>
        <cu-custom bgColor="bg-white" :isSearch="false" :isBack="true">
            <view slot="backText">返回</view>
            <view slot="content" style="color: #000000; font-weight: 600; font-size: 36rpx">详情</view>
        </cu-custom>
        <block v-if="scene != 1154">
            <view class="user-info-container" @tap="openUrl" :data-uid="user.id">
                <view>
                    <image class="round" :src="info.user_info.user_head_sculpture" style="height: 40px; width: 40px"></image>
                </view>
                <view>
                    <text style="font-weight: 600; font-size: 32rpx; color: #333333">👤 {{ info.user_info.user_nick_name }}</text>
                </view>
            </view>
            <view class="info-card" v-if="info">
                <view class="info-item">
                    <view class="info-label">
                        <text class="info-emoji">🏷️</text>
                        <text>发布类型</text>
                    </view>
                    <view class="info-value">
                        <text :class="'release-type-tag ' + (info.release_type == 0 ? 'release-type-lost' : 'release-type-found')">
                            <text>{{ info.release_type == 0 ? '😢' : '🎯' }}</text>
                            <text>{{ info.release_type == 0 ? '遗失' : '捡拾' }}</text>
                        </text>
                    </view>
                </view>
                <view class="info-item">
                    <view class="info-label">
                        <text class="info-emoji">📂</text>
                        <text>物品类别</text>
                    </view>
                    <view class="info-value">
                        <text>{{ info.name }}</text>
                    </view>
                </view>
                <view class="info-item">
                    <view class="info-label">
                        <text class="info-emoji">📝</text>
                        <text>物品名称</text>
                    </view>
                    <view class="info-value">
                        <text>{{ info.item_name }}</text>
                    </view>
                </view>
                <view class="info-item">
                    <view class="info-label">
                        <text class="info-emoji">📍</text>
                        <text>{{ info.release_type == 0 ? '遗失' : '捡拾' }}地点</text>
                    </view>
                    <view class="info-value">
                        <text>{{ info.lost_address }}</text>
                    </view>
                </view>
                <view class="info-item">
                    <view class="info-label">
                        <text class="info-emoji">⏰</text>
                        <text>{{ info.release_type == 0 ? '遗失' : '捡拾' }}时间</text>
                    </view>
                    <view class="info-value">
                        <text>{{ info.lost_time }}</text>
                    </view>
                </view>
                <view class="info-item">
                    <view class="info-label">
                        <text class="info-emoji">📞</text>
                        <text>联系方式</text>
                    </view>
                    <view class="info-value" @tap="copyBtn" :data-no="info.contact_details">
                        <text :user-select="true">{{ info.contact_details }}</text>
                    </view>
                </view>
                <view class="info-item">
                    <view class="info-label">
                        <text class="info-emoji">📄</text>
                        <text>详情描述</text>
                    </view>
                    <view class="info-value">
                        <rich-text :user-select="true" :nodes="info.item_detail"></rich-text>
                    </view>
                </view>
                <view v-if="info.image_part.length > 0" class="info-item">
                    <view class="info-label">
                        <text class="info-emoji">🖼️</text>
                        <text>详情图片</text>
                    </view>
                    <view class="info-value">
                        <view class="image-gallery">
                            <image
                                :src="item"
                                @tap="ViewImage"
                                :data-url="item"
                                mode="aspectFill"
                                class="gallery-image"
                                v-for="(item, index) in info.image_part"
                                :key="index"
                            ></image>
                        </view>
                    </view>
                </view>
                <view v-if="info.item_status == 1 && info.user_id == user.id" style="text-align: center; margin: 40rpx 0">
                    <view
                        @tap="SetStatus"
                        :class="'status-button ' + (info.release_type == 0 ? 'status-button-found' : '')"
                    >
                        <text>{{ info.release_type == 0 ? '✅ 已找到' : '✨ 已归还' }}</text>
                    </view>
                </view>
                <view
                    v-if="config.top_twig == 1 && info.user_id == user.id && info.item_status == 1"
                    class="top-feature"
                >
                    <view class="flex justify-between align-center">
                        <view style="color: #666666; display: flex; align-items: center">
                            <!-- <text class="top-feature-icon cicon-shengji"></text> -->
                            <text style="margin-right: 12rpx; font-size: 28rpx; font-weight: 600">🔝 置顶</text>
                            <text style="font-size: 24rpx">(</text>
                            <text style="font-size: 24rpx; color: #1cbbb4; font-weight: 600" :class="config.price_type == 2 ? 'text-price' : ''">{{ config.top_price }}</text>
                            <text style="font-size: 24rpx" v-if="config.price_type == 0 || config.price_type == 1">
                                {{ config.price_type == 0 ? config.design.currency : config.design.confer }}
                            </text>
                            <text style="font-size: 24rpx">/天)</text>
                        </view>
                        <view>
                            <picker :value="index" :range="ToTop" @change="bindTopChange">
                                <view class="picker" style="display: flex; align-items: center; gap: 8rpx">
                                    <text style="font-weight: 600; color: #333333; font-size: 28rpx">{{ info.top_time == 0 ? ToTop[TopIndex] : '✅ 已置顶' }}</text>
                                    <text class="cicon-forward" style="color: #1cbbb4"></text>
                                </view>
                            </picker>
                        </view>
                    </view>
                </view>
                <view
                    v-if="config.top_twig == 1 && info.top_time != 0 && info.user_id == user.id && info.item_status == 1"
                    class="text-center"
                    style="font-size: 24rpx; margin: 20rpx; color: #666666"
                >
                    <text>⏰ 置顶到期：{{ info.top_time }}</text>
                </view>
            </view>

            <view class="comment-header">
                <text class="comment-title">💬 评论区</text>
            </view>
            <view class="comment-card">
                <view class="cu-list menu-avatar comment">
                    <view class="cu-item" v-for="(item, r_index) in reply" :key="r_index">
                        <view
                            @tap="openUrl"
                            :data-uid="item.user_info.id"
                            class="cu-avatar round"
                            :style="'background-image:url(' + item.user_info.user_head_sculpture + ');'"
                        ></view>

                        <view class="content flex-sub">
                            <view class="text-gray">
                                <text>{{ item.user_info.user_nick_name }}</text>
                            </view>
                            <view class="text-gray" style="font-size: 12px">
                                <block v-if="item.is_secrecy == 0">
                                    <text class="cicon-eye-o"></text>
                                    <text style="margin-left: 10rpx">公开评论</text>
                                </block>
                                <block v-if="item.is_secrecy == 1">
                                    <text class="cicon-eye-off-o"></text>
                                    <text style="margin-left: 10rpx">楼主可见</text>
                                </block>
                                <block v-if="user.uid == item.user_id || user.id == item.user_id">
                                    <text @tap="UpdateIsSecrecy" :data-index="r_index" :data-id="item.id" :data-key="item.is_secrecy" style="margin-left: 10rpx">· 更改</text>
                                </block>
                            </view>
                            <view class="text-black text-content text-df" style="margin-top: 10px">
                                <rich-text :user-select="true" :nodes="item.content"></rich-text>
                                <image
                                    v-if="item.image_part.length > 0"
                                    :data-src="item.image_part[0]"
                                    @tap.stop.prevent="PreviewViewImage"
                                    :src="item.image_part[0]"
                                    style="width: 70px; height: 70px"
                                ></image>
                            </view>
                            <view class="margin-top-sm flex justify-between">
                                <view class="text-gray text-df" @tap="DelReply" :data-id="item.id">
                                    <text>{{ item.reply_time }}</text>
                                    <text v-if="user.uid == item.user_id || user.id == item.user_id || user.uid == info.user_id">· 删除</text>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
                <view :class="'cu-load ' + (!di_msg ? 'loading' : 'over')"></view>
            </view>
            <view @tap="openHuiFu" v-if="!huifu" class="input-container" style="position: fixed; bottom: 0; left: 0; right: 0; z-index: 1000">
                <view class="input-box">
                    <text class="input-placeholder">💭 发布评论...</text>
                </view>
                <!-- <view class="action">
                    <text class="emoji-button cuIcon-emojifill"></text>
                </view> -->
            </view>

            <view :class="'cu-modal bottom-modal ' + (huifu ? 'show' : '')" @tap="hideMode">
                <view class="cu-dialog bg-white" style="border-top-left-radius: 10px; border-top-right-radius: 10px; height: auto">
                    <view class="comment-modal-content">
                        <view v-if="huifu" class="comment-input-section">
                            <view class="comment-header-bar">
                                <text class="comment-modal-title">💬 发表评论</text>
                                <view class="privacy-indicator">
                                    <text class="privacy-text">{{ !is_secrecy ? '🌍 公开' : '🔒 私密' }}</text>
                                </view>
                            </view>
                            <view class="comment-textarea-container">
                                <textarea
                                    :focus="hui_focus"
                                    @tap.stop.prevent="close_emoji"
                                    cursor-spacing="300rpx"
                                    class="comment-textarea"
                                    @input="get_text"
                                    :value="text"
                                    maxlength="300"
                                    :placeholder="!is_secrecy ? '💭 留下你精彩的评论吧...' : '🔒 仅发帖人可见的评论...'"
                                />
                                <view class="char-count">{{ text.length }}/300</view>
                            </view>
                            <view class="comment-images" v-if="img_arr.length > 0">
                                <view class="image-grid">
                                    <view class="image-item" v-for="(item, vido_index) in img_arr" :key="vido_index">
                                        <image :data-src="item" @tap.stop.prevent="PreviewViewImage" :src="item" mode="aspectFill" class="preview-image"></image>
                                        <view class="delete-image-btn" @tap.stop.prevent="clearOneImage" :data-index="vido_index">
                                            <text class="cuIcon-close"></text>
                                        </view>
                                    </view>
                                </view>
                            </view>
                            <view class="comment-actions">
                                <view class="action-buttons">
                                    <view @tap.stop.prevent="openEmoji" :class="'action-btn emoji-btn ' + (emoji ? 'active' : '')">
                                        <text class="action-text">😊</text>
                                    </view>
                                    <view @tap.stop.prevent="previewOneImage" class="action-btn image-btn">
                                        <text class="action-text">📷</text>
                                    </view>
                                    <view @tap.stop.prevent="SetSecrecy" class="action-btn privacy-btn">
                                        <text class="action-text">{{ is_secrecy ? '🔒' : '🌍' }}</text>
                                    </view>
                                </view>
                                <view class="submit-section">
                                    <button class="submit-btn" @tap.stop.prevent="submit">
                                        <text class="submit-text">🚀 发布</text>
                                    </button>
                                </view>
                            </view>
                        </view>
                        <view v-if="emoji" class="emoji-picker">
                            <view class="emoji-picker-header">
                                <text class="emoji-picker-title">😊 选择表情</text>
                            </view>
                            <swiper :indicator-dots="true" class="emoji-swiper" indicator-color="rgba(28, 187, 180, 0.3)" indicator-active-color="#1cbbb4">
                                <block v-for="(emojis, e_index) in emj_list" :key="e_index">
                                    <swiper-item>
                                        <view class="emoji-grid">
                                            <view
                                                @tap.stop.prevent="set_emoji"
                                                :data-key="e_index"
                                                :data-index="n_index"
                                                class="emoji-item"
                                                v-for="(n_item, n_index) in emojis"
                                                :key="n_index"
                                            >
                                                <image :src="http_root + 'addons/yl_welore/web/static/expression/' + n_item" class="emoji-image"></image>
                                            </view>
                                        </view>
                                    </swiper-item>
                                </block>
                            </swiper>
                        </view>
                    </view>
                </view>
            </view>
            <view v-if="info.item_status == 2" class="seal" style="position: absolute; right: 80rpx; top: 220rpx">
                <view class="seal-son">
                    <text style="position: absolute; top: 50rpx; text-align: center; font-size: 28rpx; transform: rotate(-45deg); right: 20rpx; color: #1cbbb4; font-weight: 900">
                        {{ info.release_type == 0 ? '✅ 已找到' : '✨ 已归还' }}
                    </text>
                </view>
            </view>
            <view class="cu-load load-modal" v-if="loadModal">
                <view class="gray-text">上传中...</view>
            </view>
        </block>
    </view>
</template>

<script>
const app = getApp();
var http = require('../../../util/http.js');
var md5 = require('../../../util/md5.js');
import regeneratorRuntime from '../../../util/runtime';

export default {
    /**
     * 页面的初始数据
     */
    data() {
        return {
            http_root: app.globalData.http_root,
            user: {},
            reply: [],
            id: 0,
            page: 1,
            is_secrecy: false,
            img_arr: [],
            huifu: false,
            emj_list: [],
            emoji: false,
            text: '',
            config: {},
            TopIndex: 0,
            ToTop: ['未设置', '1天', '2天', '3天', '4天', '5天', '6天', '7天', '8天', '9天', '10天'],
            scene: 0,
            info: {},
            di_msg: false,
            hui_focus: false,
            loadModal: false,
            img_botton: true
        }
    },
    methods: {
        UpdateIsSecrecy(d) {
            var that = this;
            var id = d.currentTarget.dataset.id;
            var key = d.currentTarget.dataset.key;
            var index = d.currentTarget.dataset.index;
            var msg = key == 1 ? '确定更改为公开评论吗？' : '确定更改为仅楼主可见吗？';
            uni.showModal({
                title: '提示',
                content: msg,
                success: (res) => {
                    if (res.confirm) {
                        that.UpdateIsSecrecyDo(id, key, index);
                    }
                }
            });
        },
        UpdateIsSecrecyDo(id, key, index) {
            var b = app.globalData.api_root + 'Lost/UpdateIsSecrecyDo';
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.key = key;
            params.id = id;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.code == 1) {
                        uni.showModal({
                            title: '提示',
                            content: res.data.msg,
                            showCancel: false,
                            success: (res) => {}
                        });
                    } else {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                        that.reply[index].is_secrecy = key == 0 ? 1 : 0;
                    }

                    //that.InfoReply();
                },

                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => {}
                    });
                }
            });
        },
        bindTopChange(d) {
            this.TopIndex = d.detail.value;
            console.log('确定');
            if (d.detail.value == 0) {
                return;
            }
            var that = this;
            if (this.config.price_type == 0 || this.config.price_type == 1) {
                uni.showModal({
                    title: '提示',
                    content: '确定要置顶' + d.detail.value + '天吗？',
                    success: (res) => {
                        if (res.confirm) {
                            that.pay_submit(that.id, d.detail.value);
                        }
                    }
                });
            }
            if (this.config.price_type == 2) {
                this.pay_submit_wx(this.id, d.detail.value);
            }
        },
        pay_submit(id, top_day) {
            var b = app.globalData.api_root + 'Lost/PaySubmit';
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.top_day = top_day;
            params.id = id;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.code == 1) {
                        uni.showModal({
                            title: '提示',
                            content: res.data.msg,
                            showCancel: false,
                            success: (res) => {}
                        });
                    } else {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                    that.getInfo();
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => {}
                    });
                }
            });
        },
        /**
         * 充值
         */
        pay_submit_wx(id, top_day) {
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.lost_id = id;
            params.top_day = top_day;
            params.uid = e.id;
            var b = app.globalData.api_root + 'Pay/do_lost_pay';
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.return_msg == 'OK') {
                        var timeStamp = (Date.parse(new Date()) / 1000).toString();
                        var pkg = 'prepay_id=' + res.data.prepay_id;
                        var nonceStr = res.data.nonce_str;
                        var paySign = md5
                            .hexMD5(
                                'appId=' +
                                    res.data.appid +
                                    '&nonceStr=' +
                                    nonceStr +
                                    '&package=' +
                                    pkg +
                                    '&signType=MD5&timeStamp=' +
                                    timeStamp +
                                    '&key=' +
                                    res.data.app_info['app_key']
                            )
                            .toUpperCase(); //此处用到hexMD5插件
                        //发起支付
                        uni.requestPayment({
                            timeStamp: timeStamp,
                            nonceStr: nonceStr,
                            package: pkg,
                            signType: 'MD5',
                            paySign: paySign,
                            success: (res) => {
                                uni.showModal({
                                    title: '提示',
                                    content: '支付成功！',
                                    showCancel: false,
                                    success: (res) => {}
                                });
                                that.getInfo();
                            },
                            complete: () => {
                                uni.hideLoading();
                            }
                        });
                    } else {
                        uni.showModal({
                            title: '提示',
                            content: '支付参数错误！',
                            showCancel: false,
                            success: (res) => {}
                        });
                        uni.hideLoading();
                    }
                    that.TopIndex = 0;
                },
                fail: () => {
                    uni.hideLoading();
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => {}
                    });
                }
            });
        },
        getLostConfig() {
            return new Promise((resolve, reject) => {
                var b = app.globalData.api_root + 'Lost/getLostConfig';
                var that = this;
                var e = app.globalData.getCache('userinfo');
                var params = new Object();
                params.token = e.token;
                params.openid = e.openid;
                http.POST(b, {
                    params: params,
                    success: (res) => {
                        console.log(res);
                        resolve(res);
                        that.config = res.data;
                    },
                    fail: () => {
                        uni.showModal({
                            title: '提示',
                            content: '网络繁忙，请稍候重试！',
                            showCancel: false,
                            success: (res) => {}
                        });
                    }
                });
            });
        },
        SetStatus() {
            var that = this;
            var info = this.info;
            var msg = info.release_type == 0 ? '已找到了' : '已归还了';
            uni.showModal({
                title: '提示',
                content: '确定' + msg + '吗？',
                success: (res) => {
                    if (res.confirm) {
                        that.SetStatusDo();
                    }
                }
            });
        },
        SetStatusDo() {
            var b = app.globalData.api_root + 'Lost/SetStatusDo';
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.id = this.id;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.code == 0) {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    } else {
                        uni.showModal({
                            title: '提示',
                            content: res.data.msg,
                            showCancel: false,
                            success: (res) => {}
                        });
                    }
                    that.getInfo();
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => {}
                    });
                }
            });
        },
        DelReply(d) {
            var that = this;
            var id = d.currentTarget.dataset.id;
            uni.showModal({
                title: '提示',
                content: '确定要删除这个评论吗？',
                success: (res) => {
                    if (res.confirm) {
                        that.DelReplyDo(id);
                    }
                }
            });
        },
        DelReplyDo(id) {
            var b = app.globalData.api_root + 'Lost/DelReplyDo';
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.id = this.id;
            params.reply_id = id;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.code == 0) {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    } else {
                        uni.showModal({
                            title: '提示',
                            content: res.data.msg,
                            showCancel: false,
                            success: (res) => {}
                        });
                    }
                    that.page = 1;
                    that.reply = [];
                    that.InfoReply();
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => {}
                    });
                }
            });
        },
        submit() {
            var b = app.globalData.api_root + 'Lost/LostDoSubmit';
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.id = this.id;
            params.content = this.text;
            params.img_arr = JSON.stringify(this.img_arr);
            params.is_secrecy = this.is_secrecy ? 1 : 0;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.code == 0) {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    } else {
                        uni.showModal({
                            title: '提示',
                            content: res.data.msg,
                            showCancel: false,
                            success: (res) => {}
                        });
                    }
                    that.page = 1;
                    that.reply = [];
                    that.emoji = false;
                    that.huifu = false;
                    that.text = '';
                    that.InfoReply();
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => {}
                    });
                }
            });
        },
        get_text(e) {
            var length = e.detail.value;
            this.text = length;
        },
        set_emoji(d) {
            var index = d.currentTarget.dataset.index;
            var t_index = d.currentTarget.dataset.key;
            var str = this.emj_list[t_index][index];
            var k = str.split('.')[0];
            console.log(k);
            this.text = this.text + '[#:' + k + ']';
        },
        close_emoji() {
            this.emoji = false;
            this.hui_focus = true;
        },
        SetSecrecy() {
            this.is_secrecy = !this.is_secrecy;
        },
        openHuiFu() {
            this.huifu = true;
        },
        hideMode() {
            this.huifu = false;
        },
        openUrl(d) {
            var uid = d.currentTarget.dataset.uid;
            uni.navigateTo({
                url: '/yl_welore/pages/packageB/my_home/index?id=' + uid
            });
        },
        async doIt() {
            var e = app.globalData.getCache('userinfo');
            const do0 = await this.checkToken();
            const do123 = await this.claerStor();
            console.log(do123);
            if (!e || do0.data.status == 'no' || do123 == -1) {
                //缓存为空执行登陆
                const do1 = await this.getLogin();
            } else {
                //字段为空执行登陆
                if (typeof e.token_impede == 'undefined') {
                    const do1 = await this.getLogin();
                } else {
                    //字段0或者小于当前时间执行登陆
                    var t = parseInt(+new Date() / 1000);
                    if (e.token_impede == 0 || t >= e.token_impede) {
                        const do1 = await this.getLogin();
                    }
                }
            }
            const do3 = await this.getLostConfig();
            const do4 = await this.getInfo();
            const do5 = await this.InfoReply();
            const do6 = await this.get_emj_list();
            this.user = e;
        },
        /**
         * 登陆
         */
        getLogin() {
            return new Promise((resolve, reject) => {
                uni.login({
                    success: (res) => {
                        console.log(res);
                        var params = new Object();
                        params.code = res.code;
                        http.POST(app.globalData.api_root + 'Login/index', {
                            params: params,
                            success: (open) => {
                                console.log(open);
                                if (open.data['code'] != 0) {
                                    uni.showModal({
                                        title: '系统提示',
                                        content: '您设置的小程序参数有误，请自行检查！'
                                    });
                                    return;
                                }
                                var data = new Object();
                                data.openid = open.data.info.openid;
                                data.session_key = open.data.info.session_key;
                                console.log(data);
                                http.POST(app.globalData.api_root + 'Login/add_tourist', {
                                    params: data,
                                    success: (d) => {
                                        console.log(d.data.info);
                                        app.globalData.setCache('userinfo', d.data.info);
                                        resolve(d);
                                    }
                                });
                            }
                        });
                    }
                });
            });
        },
        claerStor() {
            return new Promise((resolve, reject) => {
                var v = app.globalData.getCache('yl_version');
                console.log(v);
                if (v == '') {
                    var v2 = -1;
                } else {
                    var v2 = app.globalData.compareVersion(v, app.globalData.version); //相等=0  小于-1  大于1
                }

                app.globalData.setCache('yl_version', app.globalData.version);
                console.log(v2);
                resolve(v2);
                return v2; //执行登陆
            });
        },

        checkToken() {
            return new Promise((resolve, reject) => {
                var e = app.globalData.getCache('userinfo');
                if (!e) {
                    resolve(e);
                    return 'no'; //执行登陆
                } else {
                    var b = app.globalData.api_root + 'Check/check_token';
                    var that = this;
                    var e = app.globalData.getCache('userinfo');
                    var params = new Object();
                    params.token = e.token;
                    params.openid = e.openid;
                    http.POST(b, {
                        params: params,
                        success: (res) => {
                            resolve(res);
                        },
                        fail: () => {
                            uni.showModal({
                                title: '提示',
                                content: '网络繁忙，请稍候重试！',
                                showCancel: false,
                                success: (res) => {}
                            });
                        }
                    });
                }
            });
        },
        InfoReply() {
            return new Promise((resolve, reject) => {
                var b = app.globalData.api_root + 'Lost/LostInfoReply';
                var that = this;
                var e = app.globalData.getCache('userinfo');
                var params = new Object();
                params.token = e.token;
                params.openid = e.openid;
                params.id = this.id;
                params.page = this.page;
                http.POST(b, {
                    params: params,
                    success: (res) => {
                        resolve(res);
                        console.log(res);
                        if (res.data.length == 0 || res.data.length < 5) {
                            that.di_msg = true;
                        }
                        var reply = that.reply;
                        reply.push(...res.data);
                        that.reply = reply;
                    },
                    fail: () => {
                        uni.showModal({
                            title: '提示',
                            content: '网络繁忙，请稍候重试！',
                            showCancel: false,
                            success: (res) => {}
                        });
                    }
                });
            });
        },
        getInfo() {
            return new Promise((resolve, reject) => {
                var b = app.globalData.api_root + 'Lost/LostInfo';
                var that = this;
                var e = app.globalData.getCache('userinfo');
                var params = new Object();
                params.token = e.token;
                params.openid = e.openid;
                params.id = this.id;
                http.POST(b, {
                    params: params,
                    success: (res) => {
                        resolve(res);
                        if (res.data.code == 0) {
                            that.info = res.data.info;
                        } else {
                            uni.showModal({
                                title: '提示',
                                content: res.data.msg,
                                showCancel: false,
                                success: (res) => {
                                    uni.navigateBack({
                                        delta: 1
                                    });
                                }
                            });
                        }
                    },
                    fail: () => {
                        uni.showModal({
                            title: '提示',
                            content: '网络繁忙，请稍候重试！',
                            showCancel: false,
                            success: (res) => {}
                        });
                    }
                });
            });
        },
        /**
         * 一键复制
         */
        copyBtn(e) {
            var that = this;
            uni.setClipboardData({
                data: e.currentTarget.dataset.no,
                success: (res) => {}
            });
        },
        ViewImage(e) {
            uni.previewImage({
                urls: this.info['image_part'],
                current: e.currentTarget.dataset.url
            });
        },
        PreviewViewImage(e) {
            uni.previewImage({
                urls: [e.currentTarget.dataset.src],
                current: e.currentTarget.dataset.src
            });
        },
        /**
         * 删除图片
         */
        clearOneImage(e) {
            var that = this;
            var index = e.target.dataset['index'];
            var notes = that.img_arr;
            notes.splice(index, 1);
            that.img_arr = notes;
            that.img_botton = true;
        },
        openEmoji() {
            this.emoji = !this.emoji;
            this.hui_focus = false;
        },
        get_emj_list() {
            return new Promise((resolve, reject) => {
                var that = this;
                var e = app.globalData.getCache('userinfo');
                var params = new Object();
                params.token = e.token;
                params.openid = e.openid;
                var b = app.globalData.api_root + 'Polls/get_emj_list';
                http.POST(b, {
                    params: params,
                    success: (res) => {
                        that.emj_list = res.data;
                        //
                        resolve(res);
                    },
                    fail: () => {
                        uni.showModal({
                            title: '提示',
                            content: '网络繁忙，请稍候重试！',
                            showCancel: false,
                            success: (res) => {}
                        });
                    }
                });
            });
        },
        /**
         * 回复上传主图
         */
        previewOneImage() {
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var b = app.globalData.api_root + 'User/img_upload';
            uni.chooseImage({
                count: 1,
                sizeType: ['original', 'compressed'],
                // 可以指定是原图还是压缩图，默认二者都有
                sourceType: ['album', 'camera'],
                // 可以指定来源是相册还是相机，默认二者都有
                success: (res) => {
                    console.log(res);
                    that.loadModal = true;
                    var tempFilePaths = res.tempFilePaths;
                    uni.uploadFile({
                        url: b,
                        filePath: tempFilePaths[0],
                        name: 'sngpic',
                        header: {
                            'content-type': 'multipart/form-data'
                        },
                        formData: {
                            'content-type': 'multipart/form-data',
                            token: e.token,
                            openid: e.openid,
                            much_id: app.globalData.siteInfo.uniacid
                        },
                        success: (res) => {
                            console.log(res);
                            var data = JSON.parse(res.data);
                            console.log(data);
                            if (data.status == 'error') {
                                that.loadModal = false;
                                uni.showModal({
                                    title: '提示',
                                    content: data.msg
                                });
                                return;
                            } else {
                                that.img_arr = that.img_arr.concat(data.url);
                                that.img_botton = false;
                            }
                            that.loadModal = false;
                        },
                        fail: (res) => {
                            uni.showModal({
                                title: '提示',
                                content: '上传错误！'
                            });
                        }
                    });
                }
            });
        }
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        var op = uni.getLaunchOptionsSync();
        this.scene = op.scene;
        if (op.scene == 1154) {
            uni.showToast({
                title: '前往小程序查看！',
                icon: 'none',
                duration: 666000
            });
            return;
        }
        var lost = app.globalData.__PlugUnitScreen('27bc52a3b4dcfd099671fb09706f02d8');
        if (!lost) {
            uni.showToast({
                title: '未开通插件',
                icon: 'none',
                duration: 2000
            });
            setTimeout(() => {
                var pages = getCurrentPages();
                if (pages.length == 1) {
                    uni.reLaunch({
                        url: '/yl_welore/pages/index/index'
                    });
                    return;
                }
                uni.navigateBack();
            }, 1000);
            return;
        }
        this.id = options.id;
        this.doIt();
        // var e = app.getCache("userinfo");
    },
    onReachBottom() {
        this.page = this.page + 1;
        this.InfoReply();
    },
    /**
     * 用户点击右上角分享
     */
    onShareTimeline() {
        var info = this.info;
        var msg = info.release_type == 0 ? '我遗失了' : '我捡到了';
        var img = info.image_part[0];
        return {
            title: msg + ':' + info.item_name,
            path: '/yl_welore/pages/packageF/lost_info/index?id=' + this.id,
            imageUrl: img
        };
    },
    /**
     * 用户点击右上角分享
     */
    onShareAppMessage() {
        var info = this.info;
        var msg = info.release_type == 0 ? '我遗失了' : '我捡到了';
        var img = info.image_part[0];
        return {
            title: msg + ':' + info.item_name,
            path: '/yl_welore/pages/packageF/lost_info/index?id=' + this.id,
            imageUrl: img
        };
    }
};
</script>
<style>
page {
    background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 50%, #f0f8ff 100%);
    min-height: 100vh;
    padding-bottom: 200rpx;
}

/* 用户信息区域样式 */
.user-info-container {
    background: #ffffff;
    margin: 20rpx;
    border-radius: 24rpx;
    padding: 24rpx;
    box-shadow: 0 8rpx 32rpx rgba(28, 187, 180, 0.15);
    display: flex;
    align-items: center;
    gap: 20rpx;
}

.user-info-container .round {
    border: 4rpx solid #1cbbb4;
    box-shadow: 0 4rpx 12rpx rgba(28, 187, 180, 0.3);
}

/* 信息卡片样式 */
.info-card {
    background: #ffffff;
    margin: 0 20rpx 24rpx;
    border-radius: 24rpx;
    overflow: hidden;
    box-shadow: 0 12rpx 40rpx rgba(28, 187, 180, 0.12);
    position: relative;
}

.info-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6rpx;
    background: linear-gradient(135deg, #1cbbb4 0%, #36cfc9 100%);
}

/* 信息项样式 */
.info-item {
    display: flex;
    align-items: center;
    padding: 35rpx 32rpx;
    border-bottom: 1rpx solid #f0eeee;
    transition: all 0.3s ease;
    justify-content: space-between;
}

.info-item:last-child {
    border-bottom: none;
}
.info-label {
    display: flex;
    align-items: center;
    gap: 12rpx;
    min-width: 160rpx;
    font-size: 28rpx;
    color: #666666;
    font-weight: 500;
}

.info-emoji {
    font-size: 32rpx;
    line-height: 1;
}

.info-value {
    font-size: 28rpx;
    color: #333333;
    font-weight: 600;
}

/* 发布类型标签样式 */
.release-type-tag {
    padding: 12rpx 24rpx;
    border-radius: 20rpx;
    font-size: 24rpx;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
    animation: pulse-tag 2s infinite;
}

.release-type-lost {
    background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
    color: #ffffff;
}

.release-type-found {
    background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
    color: #ffffff;
}

@keyframes pulse-tag {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.2);
    }
}

/* 图片展示区域样式 */
.image-gallery {
    display: flex;
    flex-wrap: wrap;
    gap: 16rpx;
    margin-top: 16rpx;
}

.gallery-image {
    width: 140rpx;
    height: 140rpx;
    border-radius: 16rpx;
    object-fit: cover;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.gallery-image:hover {
    transform: scale(1.05);
    box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.2);
}

/* 状态按钮样式 */
.status-button {
    background: linear-gradient(135deg, #1cbbb4 0%, #36cfc9 100%);
    color: #ffffff;
    border-radius:20rpx;
    width: 400rpx;
    height: 130rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28rpx;
    font-weight: 600;
    box-shadow: 0 12rpx 30rpx rgba(28, 187, 180, 0.4);
    transition: all 0.3s ease;
    margin: 40rpx auto;
}

.status-button:active {
    transform: scale(0.95);
    box-shadow: 0 8rpx 20rpx rgba(28, 187, 180, 0.5);
}

.status-button-found {
    background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
    box-shadow: 0 12rpx 30rpx rgba(82, 196, 26, 0.4);
}

.status-button-found:active {
    box-shadow: 0 8rpx 20rpx rgba(82, 196, 26, 0.5);
}

/* 置顶功能区域样式 */
.top-feature {
    background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 100%);
    border-radius: 20rpx;
    padding: 24rpx 32rpx;
    margin: 20rpx;
    box-shadow: 0 8rpx 24rpx rgba(28, 187, 180, 0.1);
    border: 2rpx solid rgba(28, 187, 180, 0.1);
}

.top-feature-icon {
    color: #1cbbb4;
    font-size: 32rpx;
    margin-right: 12rpx;
}

/* 评论区域样式 */
.comment-header {
    background: #ffffff;
    margin: 0 20rpx;
    border-radius: 24rpx 24rpx 0 0;
    padding: 24rpx 32rpx;
    box-shadow: 0 8rpx 32rpx rgba(28, 187, 180, 0.15);
    display: flex;
    align-items: center;
    gap: 16rpx;
}

.comment-icon {
    color: #1cbbb4;
    font-size: 36rpx;
}

.comment-title {
    font-size: 32rpx;
    font-weight: 700;
    color: #333333;
}

.comment-card {
    background: #ffffff;
    margin: 0 20rpx;
    padding-bottom: 60rpx;
    border-radius: 0 0 24rpx 24rpx;
}

.comment-card:last-child {
    border-radius: 0 0 24rpx 24rpx;
    margin-bottom: 200rpx;
}

/* 底部输入框样式 */
.input-container {
    background: linear-gradient(135deg, #ffffff 0%, #f8fbff 100%);
    border-top: 1rpx solid #e3f2fd;
    box-shadow: 0 -8rpx 25rpx rgba(33, 150, 243, 0.1);
    padding: 20rpx 20rpx 60rpx 20rpx;
    border-radius: 24rpx 24rpx 0 0;
}

.input-box {
    background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 100%);
    border-radius: 24rpx;
    padding: 0 24rpx;
    display: flex;
    align-items: center;
    height: 80rpx;
    box-shadow: 0 4rpx 12rpx rgba(28, 187, 180, 0.1);
}

.input-placeholder {
    color: #999999;
    font-size: 28rpx;
}

.emoji-button {
    color: #1cbbb4;
    font-size: 36rpx;
    margin-left: 16rpx;
}

/* 印章样式优化 */
.seal {
    width: 180rpx;
    height: 180rpx;
    border: solid 6rpx #1cbbb4;
    border-radius: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 249, 255, 0.9) 100%);
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 0 12rpx 30rpx rgba(28, 187, 180, 0.3);
    animation: seal-pulse 3s infinite;
}

.seal-son {
    width: 155rpx;
    height: 155rpx;
    border: solid 2rpx #1cbbb4;
    border-radius: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(248, 249, 255, 0.8) 100%);
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

@keyframes seal-pulse {
    0%, 100% {
        transform: scale(1) rotate(0deg);
        box-shadow: 0 12rpx 30rpx rgba(28, 187, 180, 0.3);
    }
    50% {
        transform: scale(1.05) rotate(2deg);
        box-shadow: 0 16rpx 40rpx rgba(28, 187, 180, 0.4);
    }
}

/* 评论弹窗样式 */
.comment-modal-dialog {
    background: linear-gradient(135deg, #ffffff 0%, #f8fbff 100%);
    border-top-left-radius: 32rpx;
    border-top-right-radius: 32rpx;
    box-shadow: 0 -12rpx 40rpx rgba(28, 187, 180, 0.2);
    height: auto;
    max-height: 60vh;
    overflow-y: auto;
    position: relative;
}

.comment-modal-content {
    width: 100%;
    padding: 0;
    display: flex;
    flex-direction: column;
    max-height: 80vh;
}

.comment-input-section {
    padding: 32rpx;
    flex-shrink: 0;
}

.comment-header-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24rpx;
    padding-bottom: 20rpx;
    border-bottom: 2rpx solid #f0f0f0;
}

.comment-modal-title {
    font-size: 32rpx;
    font-weight: 700;
    color: #333333;
}

.privacy-indicator {
    background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 100%);
    padding: 8rpx 16rpx;
    border-radius: 16rpx;
    border: 2rpx solid rgba(28, 187, 180, 0.2);
}

.privacy-text {
    font-size: 24rpx;
    color: #1cbbb4;
    font-weight: 600;
}

.comment-textarea-container {
    position: relative;
    margin-bottom: 24rpx;
}

.comment-textarea {
    width: 100%;
    min-height: 160rpx;
    max-height: 240rpx;
    background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 100%);
    border: 2rpx solid rgba(28, 187, 180, 0.2);
    border-radius: 20rpx;
    padding: 24rpx;
    font-size: 28rpx;
    color: #333333;
    line-height: 1.6;
    box-shadow: 0 4rpx 12rpx rgba(28, 187, 180, 0.1);
}

.char-count {
    position: absolute;
    bottom: 12rpx;
    right: 20rpx;
    font-size: 22rpx;
    color: #999999;
    background: rgba(255, 255, 255, 0.8);
    padding: 4rpx 8rpx;
    border-radius: 8rpx;
}

.comment-images {
    margin-bottom: 24rpx;
}

.image-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 16rpx;
}

.image-item {
    position: relative;
    width: 140rpx;
    height: 140rpx;
    border-radius: 16rpx;
    overflow: hidden;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.preview-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.delete-image-btn {
    position: absolute;
    top: 8rpx;
    right: 8rpx;
    width: 40rpx;
    height: 40rpx;
    background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    font-size: 24rpx;
    box-shadow: 0 4rpx 12rpx rgba(255, 77, 79, 0.4);
    z-index: 100;
}

.comment-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20rpx;
}

.action-buttons {
    display: flex;
    gap: 20rpx;
    flex: 1;
}

.action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8rpx;
    padding: 16rpx;
    border-radius: 16rpx;
    background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 100%);
    border: 2rpx solid rgba(28, 187, 180, 0.2);
    transition: all 0.3s ease;
    min-width: 80rpx;
}

.action-btn.active {
    background: linear-gradient(135deg, #1cbbb4 0%, #36cfc9 100%);
    color: #ffffff;
    box-shadow: 0 6rpx 20rpx rgba(28, 187, 180, 0.3);
}

.action-btn:active {
    transform: scale(0.95);
}

.action-icon {
    font-size: 32rpx;
    color: #1cbbb4;
}

.action-btn.active .action-icon {
    color: #ffffff;
}

.action-text {
    font-size: 20rpx;
    color: #666666;
    font-weight: 600;
}

.action-btn.active .action-text {
    color: #ffffff;
}

.submit-section {
    flex-shrink: 0;
}

.submit-btn {
    background: linear-gradient(135deg, #1cbbb4 0%, #36cfc9 100%);
    color: #ffffff;
    border: none;
    border-radius: 24rpx;
    padding: 20rpx 40rpx;
    font-size: 28rpx;
    font-weight: 600;
    box-shadow: 0 8rpx 24rpx rgba(28, 187, 180, 0.4);
    transition: all 0.3s ease;
}

.submit-btn:active {
    transform: scale(0.95);
    box-shadow: 0 6rpx 20rpx rgba(28, 187, 180, 0.5);
}

.submit-text {
    color: #ffffff;
    font-weight: 600;
}

/* Emoji选择器样式 */
.emoji-picker {
    background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 100%);
    border-top: 2rpx solid #e3f2fd;
    flex-shrink: 0;
    height: 450rpx;
}

.emoji-picker-header {
    padding: 24rpx 32rpx 16rpx;
    border-bottom: 2rpx solid #e3f2fd;
}

.emoji-picker-title {
    font-size: 28rpx;
    font-weight: 600;
    color: #333333;
}

.emoji-swiper {
    height: 300rpx;
}

.emoji-grid {
    display: grid;
    grid-template-columns: repeat(9, 1fr);
    gap: 16rpx;
    padding: 24rpx;
    justify-items: center;
}

.emoji-item {
    width: 60rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 12rpx;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.6);
}

.emoji-item:active {
    transform: scale(1.2);
    background: rgba(28, 187, 180, 0.2);
}

.emoji-image {
    width: 48rpx;
    height: 48rpx;
}

.ui-form-group {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    padding: 0.5em 30rpx;
}
.ui-form-group .ui-form-title {
    text-align: justify;
    font-size: 1.1em;
    position: relative;
    padding-left: 0;
    display: flex;
    align-items: center;
    margin-right: 30rpx;
}
.ui-form-group .ui-form-content {
    flex: 1;
    border-radius: 10rpx;
    display: flex;
    align-items: center;
    min-height: 3em;
}
</style>
