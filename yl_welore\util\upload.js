module.exports = {
    upload_file: upload_file,
    download_file: download_file,
    open_file: openFileDo,
    Access: access
};
//查询本地文件
function access(data, yl_file, Fail) {
    console.log(data);
    var filePath = `${uni.env.USER_DATA_PATH}` + '/' + data.name + '.' + data.type;
    yl_file.access({
        path: filePath,
        success(res) {
            console.log(res);
            open_type_file(data.type, filePath, data);
        },
        fail(res) {
            console.log(res);
            switch (data.type) {
                case 'doc':
                case 'docx':
                case 'xls':
                case 'xlsx':
                case 'ppt':
                case 'pptx':
                case 'pdf':
                    Fail(res);
                    break;
                default:
                    open_type_file(data.type, filePath, data);
            }
        }
    });
}
//移动文件到本地
function openFileDo(data, yl_file) {
    console.log(data);
    var filePath = `${uni.env.USER_DATA_PATH}` + '/' + data.name + '.' + data.type;
    yl_file.saveFile({
        tempFilePath: data.url,
        filePath: filePath,
        success(res) {
            console.log(res);
            if ((res.errMsg = 'saveFile:ok')) {
                open_type_file(data.type, filePath, data);
            } else {
                open_type_file(data.type, data.url, data);
            }
        },
        fail(res) {
            console.log(res);
            open_type_file(data.type, data.url, data);
        }
    });
}
//打开文件
function open_type_file(type, url, data) {
    console.log(data);
    switch (type) {
        case 'jpg':
        case 'jpeg':
        case 'bmp':
        case 'png':
        case 'gif':
            uni.previewMedia({
                sources: [
                    {
                        url: data.url,
                        type: 'image'
                    }
                ],
                fail() {
                    console.log(res);
                    uni.setClipboardData({
                        data: data.url,
                        success: function (res) {}
                    });
                }
            });
            break;
        case 'mp4':
        case 'avi':
        case 'mpeg':
        case 'wav':
        case 'ogg':
        case 'mkv':
            uni.previewMedia({
                sources: [
                    {
                        url: data.url,
                        type: 'video'
                    }
                ],
                fail(res) {
                    console.log(res);
                    uni.setClipboardData({
                        data: data.url,
                        success: function (res) {}
                    });
                }
            });
            break;
        case 'm4a':
        case 'aac':
        case 'mp3':
        case 'wav':
            uni.playBackgroundAudio({
                dataUrl: data.url,
                title: data.name,
                coverImgUrl: '',
                success: function (res) {
                    console.log('playBackgroundAudio success');
                },
                fail: function (res) {
                    console.log(res);
                    uni.setClipboardData({
                        data: data.url,
                        success: function (res) {}
                    });
                }
            });
            break;
        case 'doc':
        case 'docx':
        case 'xls':
        case 'xlsx':
        case 'ppt':
        case 'pptx':
        case 'pdf':
            uni.openDocument({
                filePath: url,
                showMenu: data.is_sell == 1 ? true : false,
                success: function (res) {
                    console.log('打开文档成功');
                }
            });
            break;
        default:
            uni.showModal({
                title: '提示',
                confirmText: '下载链接',
                content: '当前文件格式不支持预览！',
                success(res) {
                    if (res.confirm) {
                        uni.setClipboardData({
                            data: data.url,
                            success: function (res) {}
                        });
                    }
                }
            });
            break;
    }
}
//下载文件
function download_file(data, Success, Fail, down_task) {
    var download = uni.downloadFile({
        url: data.url,
        success(res) {
            Success(res);
        },
        fail(res) {
            Fail(res);
        }
    });
    download.onProgressUpdate((res) => {
        down_task(res);
    });
}
//上传文件
function upload_file(data, Success, Fail, Rrror, Merror, upload_task) {
    console.log(data);
    var app = getApp();
    var e = app.globalData.getCache('userinfo');
    var b = app.globalData.api_root + 'Storage/file_upload';
    uni.chooseMessageFile({
        count: 1,
        type: 'all',
        success(red) {
            console.log(red);
            var tempFilePaths = red.tempFiles;
            if (tempFilePaths[0].size > data.upload_limit_b) {
                uni.showToast({
                    title: '上传限制' + data.upload_limit,
                    icon: 'none',
                    duration: 2000
                });
                Merror(red);
                return;
            }
            var fileExtension = tempFilePaths[0].name.substring(tempFilePaths[0].name.lastIndexOf('.') + 1);
            console.log(fileExtension);
            for (var i = 0; i <= data.upload_type_limited.length; i++) {
                if (data.upload_type_limited[i] == fileExtension) {
                    uni.showToast({
                        title: '禁止上传' + fileExtension + '文件',
                        icon: 'none',
                        duration: 2000
                    });
                    Merror(red);
                    return;
                }
            }
            var zong = parseInt(data['user_use_b']) + parseInt(tempFilePaths[0].size);
            if (zong > parseInt(data['user_big_b'])) {
                uni.showToast({
                    title: '网盘容量超限！',
                    icon: 'none',
                    duration: 2000
                });
                Merror(red);
                return;
            }
            var uploadTask = uni.uploadFile({
                url: b,
                filePath: tempFilePaths[0].path,
                name: 'sngpic',
                header: {
                    'content-type': 'multipart/form-data'
                },
                formData: {
                    'content-type': 'multipart/form-data',
                    token: e.token,
                    openid: e.openid,
                    much_id: app.globalData.siteInfo.uniacid,
                    file_name: tempFilePaths[0].name,
                    file_size: tempFilePaths[0].size,
                    pid: data.pid
                },
                success: function (res) {
                    console.log(res);
                    try {
                        var data = JSON.parse(res.data);
                        Success(data);
                    } catch (error) {
                        console.log('CatchClause', error);
                        console.log('CatchClause', error);
                        Rrror(res);
                    }
                },
                fail: function (res) {
                    Rrror(res);
                }
            });
            uploadTask.onProgressUpdate((res) => {
                upload_task(res);
            });
        },
        fail(res) {
            Fail(res);
        }
    });
}
