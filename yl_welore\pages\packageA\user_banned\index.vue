<template>
    <view>
        <cu-custom bgColor="bg-white" :isBack="true" :isSearch="false">
            <view slot="backText">返回</view>
            <view slot="content" style="color: #2c2b2b; font-weight: 600; font-size: 36rpx">禁言列表</view>
        </cu-custom>

        <view>
            <block v-for="(item, index) in user_list" :key="index">
                <view class="cu-list menu-avatar">
                    <view class="cu-item">
                        <view class="cu-avatar round lg" :style="'background-image:url(' + item.user_head_sculpture + ');'"></view>
                        <view class="content flex-sub">
                            <view class="text-grey">{{ item.user_nick_name }}</view>
                            <view class="text-gray text-sm flex justify-between">解除禁言时间：{{ item.refer_time }}</view>
                            <view v-if="item.mutter.length > 0" class="text-gray text-sm flex justify-between">申诉时间：{{ item.mutter.mute_time }}</view>
                        </view>
                        <view class="flex align-end" style="z-index: 10">
                            <button @tap="add_envite_sulord" :data-id="item.id" class="cu-btn round bg-orange sm" role="button" :aria-disabled="false">解除禁言</button>
                        </view>
                    </view>
                    <view style="padding: 0px 20px">原因：{{ item.beget }}</view>
                    <view v-if="item.mutter.length > 0" style="padding: 0px 20px">申诉：{{ item.mutter.beget }}</view>
                </view>
            </block>
        </view>
        <view :class="'cu-load ' + (user_list.length == 0 ? 'over' : '')"></view>
    </view>
</template>

<script>
const app = getApp();
var http = require('../../../util/http.js');
export default {
    data() {
        return {
            page: 1,
            user_list: [],
            id: ''
        };
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        this.page = 1;
        this.id = options.id;
        this.get_my_rec();
    },
    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {},
    /**
     * 下拉刷新
     */
    onPullDownRefresh() {
        //wx.showNavigationBarLoading() //在标题栏中显示加载
        //模拟加载
        setTimeout(() => {
            uni.hideNavigationBarLoading(); //完成停止加载
            uni.stopPullDownRefresh(); //停止下拉刷新
        }, 1500);
        this.user_list = [];
        this.get_my_rec();
    },
    methods: {
        /**
         * 申请列表
         */
        get_my_rec() {
            const e = app.globalData.getCache('userinfo');
            const params = {
                token: e.token,
                openid: e.openid,
                uid: e.uid,
                id: this.id
            };
            var b = app.globalData.api_root + 'User/get_user_banned_qq';
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.status == 'success') {
                        this.user_list = res.data.info;
                    } else {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => {}
                    });
                }
            });
        },

        /**
         * 解除禁言
         */
        add_envite_sulord(data) {
            const e = app.globalData.getCache('userinfo');
            const params = {
                token: e.token,
                openid: e.openid,
                uid: e.uid,
                tory_id: this.id,
                id: data.currentTarget.dataset.id
            };
            var b = app.globalData.api_root + 'User/jie_user_banned';
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.status == 'success') {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                        this.get_my_rec();
                    } else {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => {}
                    });
                }
            });
        }
    }
};
</script>
<style>
page {
    background-color: #fff;
}
</style>
