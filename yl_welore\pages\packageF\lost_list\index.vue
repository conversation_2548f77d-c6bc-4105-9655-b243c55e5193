<template>
    <view>
        <cu-custom bgColor="none" :isSearch="false" :isBack="true">
            <view slot="backText">返回</view>
            <view slot="content" style="color: #000000; font-weight: 600; font-size: 36rpx">失物招领</view>
        </cu-custom>
        <view class="search-container">
            <view class="search-form-modern">
                <text class="cuIcon-search search-icon"></text>
                <input @input="get_ser_name" :value="content" type="text" placeholder="输入物品名搜搜看 ..."
                    class="search-input" />
            </view>
            <view class="search-action">
                <button @tap="sou" class="search-btn">搜索</button>
            </view>
        </view>
        <scroll-view scroll-x class="nav-container" scroll-with-animation>
            <view :class="'nav-item ' + (type_index == -1 ? 'nav-item-active' : '')" @tap="tabSelect" data-index="-1"
                data-id="0">全部</view>
            <view :class="'nav-item ' + (type_index == index ? 'nav-item-active' : '')" @tap="tabSelect"
                :data-index="index" :data-id="item.id" v-for="(item, index) in type_list" :key="index">
                {{ item.name }}
            </view>
        </scroll-view>
        <view class="lost-item-card" v-for="(item, list_index) in list" :key="list_index">
            <view class="card-content" @tap="LostInfo" :data-id="item.id">
                <view class="item-content">
                    <view class="item-header">
                        <view v-if="item.is_top == 1" class="top-tag">
                            <text class="cicon-shengji"></text>
                            <text>置顶</text>
                        </view>
                        <text class="item-title">{{ item.item_name }}</text>
                    </view>
                    <view class="item-description">
                        <rich-text :nodes="item.item_detail"></rich-text>
                    </view>
                    <view class="item-images" v-if="item.image_part && item.image_part.length > 0">
                        <image :src="img" @tap.stop.prevent="ViewImage" :data-index="list_index" :data-url="img"
                            mode="aspectFill" class="item-image" v-for="(img, index) in item.image_part" :key="index">
                        </image>
                    </view>
                    <view class="user-info">
                        <image :src="item.user_info.user_head_sculpture" class="user-avatar"></image>
                        <text class="user-name">{{ item.user_info.user_nick_name }}</text>
                    </view>
                    <view class="publish-time">发布时间：{{ item.create_time }}</view>
                    <view v-if="current == 'tab2'" class="audit-status">
                        <text class="status-pending" v-if="item.audit_status == 0">待审核</text>
                        <text class="status-approved" v-if="item.audit_status == 1">审核通过</text>
                        <text class="status-rejected" v-if="item.audit_status == 2">审核未通过：</text>
                        <view v-if="item.audit_status == 2" class="reject-reason">{{ item.audit_reason }}</view>
                    </view>
                    <view v-if="current == 'tab2'" class="delete-action">
                        <view @tap.stop.prevent="DelInfo" :data-id="item.id" class="delete-btn">
                            <text class="delete-icon">🗑️</text>
                            <text class="delete-text">删除</text>
                        </view>
                    </view>
                </view>
                <view class="status-tags">
                    <view v-if="item.release_type == 0 && item.item_status == 1" class="status-tag status-lost">
                        <text class="status-text">遗失</text>
                    </view>
                    <view v-if="item.release_type == 1 && item.item_status == 1" class="status-tag status-found">
                        <text class="status-text">捡拾</text>
                    </view>
                    <view v-if="item.item_status == 2 && item.release_type == 0" class="status-tag status-completed">
                        <text class="status-text">已找到</text>
                    </view>
                    <view v-if="item.item_status == 2 && item.release_type == 1" class="status-tag status-completed">
                        <text class="status-text">已归还</text>
                    </view>
                </view>
            </view>
        </view>
        <view :class="'cu-load ' + (!di_msg ? 'loading' : 'over')"></view>
        <view style="padding-bottom: 100px"></view>
        <!-- <view class="btn">
  <view catchtap="click" class="btn-main {{btnAnimation}}">+</view>
</view> -->
        <!-- <view class="mask1 {{maskAnimation}}" catchtouchmove="preventdefault"></view>
<view class='menu-container' catchtouchmove="preventdefault" hidden='{{isShow}}'>
  <view class='add_menu'>
    <view class='menu-list'>
      <view class='menu-item' bindtap="add_local" data-key="0" style="width:50%;animation-delay: 0.1s">
        <image mode='aspectFill' src='{{http_root}}addons/yl_welore/web/static/applet_icon/lost1.png' class="menu-icon"></image>
        <text class='menu-name'>遗失</text>
      </view>
      <view class='menu-item' bindtap="add_local" data-key="1" style="width:50%;animation-delay: 0.1s">
        <image mode='aspectFill' src='{{http_root}}addons/yl_welore/web/static/applet_icon/lost2.png' class="menu-icon"></image>
        <text class='menu-name'>捡拾</text>
      </view>
    </view>
  </view>
</view> -->
        <view class="bottom-tabbar">
            <view @tap="handleChange" data-key="tab1"
                :class="'tab-action ' + (current == 'tab1' ? 'tab-active' : 'tab-inactive')">
                <view class="tab-icon">
                    <text class="cuIcon-home"></text>
                </view>
                <text class="tab-text">首页</text>
            </view>
            <view class="add-action-container">
                <button @tap="add_local" data-key="0" class="add-btn">
                    <text class="cuIcon-add"></text>
                </button>
            </view>
            <view @tap="handleChange" data-key="tab2"
                :class="'tab-action ' + (current == 'tab2' ? 'tab-active' : 'tab-inactive')">
                <view class="tab-icon">
                    <text class="cuIcon-my"></text>
                </view>
                <text class="tab-text">我的</text>
            </view>
        </view>
    </view>
</template>

<script>
const app = getApp();
var http = require('../../../util/http.js');

export default {
    /**
     * 页面的初始数据
     */
    data() {
        return {
            http_root: app.globalData.http_root,
            type_list: [],
            current: 'tab1',
            list: [],
            page: 1,
            type_index: -1,
            type_id: 0,
            isShow: true,
            Refresh: false,
            content: '',
            di_msg: false,
            maskAnimation: '',
            btnAnimation: ''
        }
    },
    methods: {
        get_ser_name(d) {
            this.content = d.detail.value;
        },
        sou() {
            this.list = [];
            this.page = 1;
            this.type_id = 0;
            this.di_msg = false;
            this.get_list();
        },
        handleChange(detail) {
            var key = detail.currentTarget.dataset.key;
            console.log(key);
            this.list = [];
            this.page = 1;
            this.current = key;
            this.di_msg = false;
            this.get_list();
        },
        ViewImage(e) {
            uni.previewImage({
                urls: this.list[e.currentTarget.dataset.index].image_part,
                current: e.currentTarget.dataset.url
            });
        },
        LostInfo(d) {
            console.log(d);
            var id = d.currentTarget.dataset.id;
            uni.navigateTo({
                url: '/yl_welore/pages/packageF/lost_info/index?id=' + id
            });
        },
        add_local(d) {
            var key = d.currentTarget.dataset.key;
            uni.navigateTo({
                url: '/yl_welore/pages/packageF/lost_add/index?key=' + key
            });
        },
        tabSelect(d) {
            console.log(d);
            var index = d.currentTarget.dataset.index;
            var id = d.currentTarget.dataset.id;
            this.type_index = index;
            this.type_id = id;
            this.list = [];
            this.page = 1;
            this.get_list();
        },
        get_lost_type() {
            var b = app.globalData.api_root + 'Lost/getLostType';
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            http.POST(b, {
                params: params,
                success(res) {
                    that.type_list = res.data;
                },
                fail() {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success(res) { }
                    });
                }
            });
        },
        DelInfo(d) {
            var that = this;
            var id = d.currentTarget.dataset.id;
            uni.showModal({
                title: '提示',
                content: '确定删除吗？',
                success(res) {
                    if (res.confirm) {
                        that.DelInfoDo(id);
                    }
                }
            });
        },
        DelInfoDo(id) {
            var b = app.globalData.api_root + 'Lost/DelInfoDo';
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.id = id;
            http.POST(b, {
                params: params,
                success(res) {
                    uni.showToast({
                        title: res.data.msg,
                        icon: 'none',
                        duration: 2000
                    });
                    that.page = 1;
                    that.list = [];
                    that.get_list();
                },
                fail() {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success(res) { }
                    });
                }
            });
        },
        get_list() {
            var b = app.globalData.api_root + 'Lost/getLostList';
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.page = this.page;
            params.type_id = this.type_id;
            params.current = this.current;
            params.search = this.content;
            http.POST(b, {
                params: params,
                success(res) {
                    if (res.data.length == 0 || res.data.length < 7) {
                        that.di_msg = true;
                    }
                    var list = that.list;
                    list.push(...res.data);
                    that.list = list;
                },
                fail() {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success(res) { }
                    });
                }
            });
        },
        click() {
            this.isShow = !this.isShow;
            if (this.isShow) {
                this.maskAnimation = 'maskClose';
                this.btnAnimation = 'menuClose';
            } else {
                this.maskAnimation = 'maskOpen';
                this.btnAnimation = 'menuOpen';
            }
        },
        BackPage() {
            var pages = getCurrentPages();
            var Page = pages[pages.length - 1]; //当前页
            var prevPage = pages[pages.length - 2]; //上一个页面
            if (pages.length == 1) {
                this.toHome();
                return;
            }
            uni.navigateBack();
        },
        toHome() {
            uni.reLaunch({
                url: '/yl_welore/pages/index/index'
            });
        }
    },
    onLoad(options) {
        var lost = app.globalData.__PlugUnitScreen('27bc52a3b4dcfd099671fb09706f02d8');
        if (!lost) {
            uni.showToast({
                title: '未开通插件',
                icon: 'none',
                duration: 2000
            });
            return;
        }
        this.get_list();
        this.get_lost_type();
    },
    onShow() {
        var lost = app.globalData.__PlugUnitScreen('27bc52a3b4dcfd099671fb09706f02d8');
        if (!lost) {
            uni.showToast({
                title: '未开通插件',
                icon: 'none',
                duration: 2000
            });
            setTimeout(() => {
                this.BackPage();
            }, 1000);
            return;
        }
        if (this.Refresh) {
            this.type_list = [];
            this.list = [];
            this.page = 1;
            this.type_index = -1;
            this.type_id = 0;
            this.Refresh = false;
            this.click();
            this.get_list();
            this.get_lost_type();
        }
    },
    onReachBottom() {
        this.page = this.page + 1;
        this.get_list();
    },
    /**
     * 下拉刷新
     */
    onPullDownRefresh() {
        uni.showNavigationBarLoading(); //在标题栏中显示加载
        //模拟加载
        setTimeout(() => {
            uni.hideNavigationBarLoading(); //完成停止加载
            uni.stopPullDownRefresh(); //停止下拉刷新
        }, 1500);
        this.list = [];
        this.page = 1;
        this.get_list();
    }
};
</script>
<style>
page {
    background: #f8fafb;
    min-height: 100vh;
}

._this {
    font-weight: 600;
    font-size: 20px;
}

/* 搜索容器样式 */
.search-container {
    background: #ffffff;
    margin: 16rpx 20rpx 24rpx;
    border-radius: 16rpx;
    padding: 24rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
    display: flex;
    align-items: center;
    gap: 16rpx;
}

.search-form-modern {
    flex: 1;
    background: #f5f7fa;
    border-radius: 12rpx;
    padding: 0 20rpx;
    display: flex;
    align-items: center;
    height: 72rpx;
}

.search-icon {
    color: #00D4AA;
    font-size: 28rpx;
    margin-right: 12rpx;
}

.search-input {
    flex: 1;
    font-size: 28rpx;
    color: #2c3e50;
    background: transparent;
    border: none;
}

.search-action {
    flex-shrink: 0;
}

.search-btn {
    background: #00D4AA;
    color: #ffffff;
    border: none;
    border-radius: 12rpx;
    padding: 0 24rpx;
    font-size: 28rpx;
    font-weight: 500;
    height: 72rpx;
    line-height: 72rpx;
}

/* 导航容器样式 */
.nav-container {
    width: 95%;
    background: #ffffff;
    margin: 0 20rpx 24rpx;
    border-radius: 16rpx;
    padding: 16rpx 20rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
    white-space: nowrap;
}

.nav-item {
    display: inline-block;
    padding: 12rpx 24rpx;
    margin-right: 12rpx;
    border-radius: 12rpx;
    font-size: 28rpx;
    color: #7f8c8d;
    background: #f8f9fa;
    transition: all 0.2s ease;
}

.nav-item-active {
    background: #00D4AA;
    color: #ffffff;
}

/* 失物卡片样式 */
.lost-item-card {
    background: #ffffff;
    margin: 0 20rpx 20rpx;
    border-radius: 16rpx;
    overflow: hidden;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
    position: relative;
    border: 1rpx solid #f1f3f4;
}

.card-content {
    padding: 24rpx;
    position: relative;
}

.item-content {
    position: relative;
}

.item-header {
    display: flex;
    align-items: center;
    margin-bottom: 16rpx;
    flex-wrap: wrap;
    gap: 12rpx;
}

.top-tag {
    background: #ff6b35;
    color: #ffffff;
    padding: 6rpx 12rpx;
    border-radius: 8rpx;
    font-size: 20rpx;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 4rpx;
}

.item-title {
    font-size: 30rpx;
    font-weight: 600;
    color: #2c3e50;
    flex: 1;
    line-height: 1.4;
}

.item-description {
    color: #7f8c8d;
    font-size: 26rpx;
    line-height: 1.5;
    margin-bottom: 20rpx;
    padding-right: 80rpx;
}

.item-images {
    display: flex;
    flex-wrap: wrap;
    gap: 12rpx;
    margin-bottom: 20rpx;
}

.item-image {
    width: 120rpx;
    height: 120rpx;
    border-radius: 12rpx;
    object-fit: cover;
}

.user-info {
    display: flex;
    align-items: center;
    margin-bottom: 12rpx;
}

.user-avatar {
    width: 36rpx;
    height: 36rpx;
    border-radius: 50%;
    margin-right: 10rpx;
}

.user-name {
    font-size: 24rpx;
    color: #95a5a6;
}

.publish-time {
    font-size: 24rpx;
    color: #bdc3c7;
    margin-bottom: 16rpx;
}

.audit-status {
    margin-bottom: 16rpx;
}

.status-pending {
    color: #f39c12;
    font-size: 24rpx;
    font-weight: 500;
}

.status-approved {
    color: #27ae60;
    font-size: 24rpx;
    font-weight: 500;
}

.status-rejected {
    color: #e74c3c;
    font-size: 24rpx;
    font-weight: 500;
}

.reject-reason {
    color: #95a5a6;
    font-size: 22rpx;
    margin-top: 6rpx;
}

.delete-action {
    position: absolute;
    right: 0rpx;
    bottom: 24rpx;
}

.delete-btn {
    background: #95a5a6;
    color: #ffffff;
    padding: 8rpx 16rpx;
    border-radius: 12rpx;
    font-size: 22rpx;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 6rpx;
    transition: all 0.2s ease;
}

.delete-btn:active {
    transform: scale(0.95);
    background: #e74c3c;
}

.delete-icon {
    font-size: 24rpx;
    line-height: 1;
}

.delete-text {
    font-size: 22rpx;
    font-weight: 600;
    line-height: 1;
}

.status-tags {
    position: absolute;
    right: 24rpx;
    top: 24rpx;
    display: flex;
    flex-direction: column;
    gap: 6rpx;
}

.status-tag {
    padding: 8rpx 16rpx;
    border-radius: 12rpx;
    font-size: 22rpx;
    font-weight: 500;
    text-align: center;
    min-width: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
}

.status-text {
    font-size: 22rpx;
    font-weight: 500;
    line-height: 1;
}

.status-lost {
    background: #e74c3c;
    color: #ffffff;
}

.status-found {
    background: #74B9FF;
    color: #ffffff;
}

.status-completed {
    background: #27ae60;
    color: #ffffff;
}

/* 简化的状态标签样式，移除过度动画 */

/* 底部导航栏样式 */
.bottom-tabbar {
    position: fixed;
    bottom: 0;
    width: 100%;
    z-index: 2000;
    background: #ffffff;
    border-top: 1rpx solid #f1f3f4;
    box-shadow: 0 -2rpx 12rpx rgba(0, 0, 0, 0.04);
    display: flex;
    align-items: center;
    padding: 16rpx 0 calc(16rpx + env(safe-area-inset-bottom));
}

.tab-action {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.tab-active {
    color: #00D4AA;
}

.tab-inactive {
    color: #bdc3c7;
}

.tab-icon {
    font-size: 44rpx;
    margin-bottom: 6rpx;
    line-height: 1;
}

.tab-text {
    font-size: 22rpx;
    font-weight: 500;
    line-height: 1;
}

.add-action-container {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

.add-btn {
    width: 88rpx;
    height: 88rpx;
    border-radius: 44rpx;
    background: #00D4AA;
    color: white;
    border: none;
    font-size: 36rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4rpx 16rpx rgba(0, 212, 170, 0.3);
    position: relative;
    top: -20rpx;
    transition: all 0.2s ease;
}

.add-btn:active {
    transform: translateY(2rpx) scale(0.95);
    box-shadow: 0 2rpx 12rpx rgba(0, 212, 170, 0.4);
}

/* 移除不必要的遮罩动画样式，简化设计 */

/* 移除旧的按钮样式，使用新的简洁设计 */

/* 移除复杂的菜单动画样式，保持简洁设计 */

/* 新增：优化加载状态样式 */
.cu-load {
    text-align: center;
    padding: 40rpx 0;
    color: #bdc3c7;
    font-size: 24rpx;
}

.cu-load.loading::before {
    content: '加载中...';
}

.cu-load.over::before {
    content: '没有更多了';
}
</style>
