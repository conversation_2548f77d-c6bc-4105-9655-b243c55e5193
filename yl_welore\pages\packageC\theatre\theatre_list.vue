<template>
    <view>
        <view class="cu-bar search">
            <view class="search-form round">
                <text class="cuIcon-search"></text>
                <input @input="get_ser_name" :value="content" type="text" placeholder="输入名称搜搜看 ..." />
            </view>
            <view class="action">
                <button @tap="sou" class="cu-btn bg-yellow shadow-blur round text-white">搜索</button>
            </view>
        </view>
        <scroll-view scroll-x class="nav" scroll-with-animation :scroll-left="scrollLeft">
            <view
                :class="'cu-item ' + (index == TabCur ? 'nv-text' : 'def-text')"
                :data-key="index"
                @tap="tabSelect"
                :data-id="item.id"
                v-for="(item, index) in type_list"
                :key="index"
            >
                <text>{{ item.name }}</text>

                <view v-if="index == TabCur" class="cut"></view>
            </view>
        </scroll-view>
        <view style="padding: 15rpx">
            <view class="grid col-3">
                <view @tap="open_url" data-type="2" :data-id="item.id" style="padding: 0rpx 10rpx; margin-bottom: 40rpx" v-for="(item, index) in index_list" :key="index">
                    <image :lazy-load="true" :src="item.poster_url" style="width: 100%; max-height: 350rpx; border-radius: 10rpx"></image>

                    <view class="text_num_1" style="margin-top: 10rpx">{{ item.title }}</view>

                    <view style="margin-top: 10rpx; font-size: 24rpx; color: #999999">共{{ item.total_episodes }}集</view>
                </view>
            </view>
        </view>
        <view :class="'cu-load ' + (!di_msg ? 'loading' : 'over')" style="padding-bottom: 220rpx"></view>
    </view>
</template>

<script>
export default {
    props: ['data', 'compName'],
    computed: {
        content() {
            return this.$parent.$data.content;
        },
        TabCur() {
            return this.$parent.$data.TabCur;
        },
        index_list(){
            return this.$parent.$data.index_list;
        },
        type_list() {
            return this.$parent.$data.type_list;
        },
        di_msg() {
            return this.$parent.$data.di_msg;
        },
    },
    methods:{
        get_ser_name(e){
            this.$emit('get_ser_name', e);
        },
        sou(e){
            this.$emit('sou', e);
        },
        tabSelect(e){
            this.$emit('tabSelect', e);
        },
        open_url(e){
            this.$emit('open_url', e);
        }
    }
};
</script>
<style></style>
