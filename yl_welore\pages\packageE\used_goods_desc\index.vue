<template>
    <view>
        <cu-custom bgColor="bg-white" :isSearch="false" :isBack="true">
            <view slot="backText"></view>
            <view slot="content" style="color: #000000; font-weight: 600; font-size: 34rpx">{{ config.custom_title }}使用规则</view>
        </cu-custom>
        <view style="padding: 15px">
            <rich-text :user-select="true" :nodes="config.help_document"></rich-text>
        </view>
    </view>
</template>

<script>
const app = getApp();
var http = require('../../../util/http.js');

export default {
    /**
     * 页面的初始数据
     */
    data() {
        return {
            info: {},
            config: {}
        }
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        this.getLostConfig();
    },
    onShow() {
        var lost = app.globalData.__PlugUnitScreen('a11eb9c1955977a6d890dca4991209f6');
        if (!lost) {
            uni.showToast({
                title: '未开通插件',
                icon: 'none',
                duration: 2000
            });
            setTimeout(() => {
                this.BackPage();
            }, 1000);
            return;
        }
    },
    methods: {
        getLostConfig() {
            var b = app.globalData.api_root + 'Used/getLostConfig';
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    that.config = res.data;
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => {}
                    });
                }
            });
        }
    }
}
</script>
<style>
page {
    background-color: #ffffff;
}
rich-text {
    line-height: 50rpx;
}
</style>
