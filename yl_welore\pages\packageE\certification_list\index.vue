<template>
    <view class="page-container">
        <cu-custom bgColor="none" :isSearch="false" :isBack="true" style="color: #ffffff;">
            <view slot="backText"></view>
            <view slot="content" class="header-title">认证</view>
        </cu-custom>

        <view class="certification-container">
            <view class="certification-card" v-for="(item, l_index) in list" :key="l_index">
                <!-- 认证图标区域 -->
                <view class="cert-icon-section">
                    <view class="icon-background">
                        <view class="icon-pattern"></view>
                        <image class="cert-icon" :src="item.at_icon" mode="widthFix"></image>
                    </view>

                    <!-- 状态徽章 -->
                    <view class="status-badge-container">
                        <view class="status-badge uncertified" v-if="item.refuse == ''">
                            <text class="status-icon">📋</text>
                            <text class="status-text">未认证</text>
                        </view>
                        <block v-else>
                            <view v-if="item.refuse.adopt_status == 0" class="status-badge reviewing">
                                <text class="status-icon">⏳</text>
                                <text class="status-text">审核中</text>
                            </view>
                            <view v-if="item.refuse.adopt_status == 1" class="status-badge certified">
                                <text class="status-icon">✅</text>
                                <text class="status-text">已认证</text>
                            </view>
                            <view v-if="item.refuse.adopt_status == 2" class="status-badge rejected">
                                <text class="status-icon">❌</text>
                                <text class="status-text">未通过</text>
                            </view>
                        </block>
                    </view>
                </view>

                <!-- 认证信息区域 -->
                <view class="cert-info-section">
                    <view class="cert-name">{{ item.at_name }}</view>
                    <view class="cert-count">已认证人数：{{ item.count }}</view>
                </view>

                <!-- 操作按钮区域 -->
                <view >
                    <view class="cert-actions" v-if="item.refuse != ''">
                        <button
                            class="detail-btn"
                            @tap.stop.prevent="open_mak"
                            :data-id="l_index"
                            v-if="item.refuse.adopt_status == 2"
                        >
                            <text class="btn-icon">👁️</text>
                            <text class="btn-text">查看详情</text>
                        </button>
                        <button
                            @tap.stop.prevent="open_ramk"
                            :data-id="item.id"
                            v-if="item.refuse.adopt_status == 2"
                            class="submit-btn resubmit"
                        >
                            <text class="btn-icon">🔄</text>
                            <text class="btn-text">重新提交</text>
                        </button>
                    </view>
                    <view class="cert-actions" v-else>
                        <button
                            @tap.stop.prevent="open_ramk"
                            :data-id="item.id"
                            class="submit-btn primary"
                        >
                            <text class="btn-icon">📝</text>
                            <text class="btn-text">提交认证</text>
                        </button>
                    </view>
                </view>
            </view>
        </view>

        <!-- 优化后的弹窗 -->
        <view :class="'modern-modal ' + (modalName == 'Modal' ? 'show' : '')">
            <view class="modal-mask" @tap="hideModal"></view>
            <view class="modal-content">
                <view class="modal-header">
                    <view class="modal-title">审核详情</view>
                    <view class="modal-close" @tap="hideModal">
                        <text class="close-icon">✕</text>
                    </view>
                </view>
                <view class="modal-body">
                    {{ info }}
                </view>
            </view>
        </view>
    </view>
</template>

<script>
const app = getApp();
var http = require('../../../util/http.js');

export default {
    /**
     * 页面的初始数据
     */
    data() {
        return {
            check: null,
            http_root: app.globalData.http_root,
            list: [],
            modalName: null,
            info: ''
        }
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        uni.hideShareMenu();
        this.get_rz_list();
    },
    methods: {
        open_mak(item) {
            var index = item.target.dataset.id;
            this.info = this.list[index]['refuse']['ut_inject'];
            this.modalName = 'Modal';
        },
        hideModal(e) {
            this.modalName = null;
        },
        get_rz_list() {
            var b = app.globalData.api_root + 'Ranking/get_rz_list';
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            http.POST(b, {
                params: params,
                success(res) {
                    console.log(res);
                    that.list = res.data.list;
                },
                fail() {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success(res) {}
                    });
                }
            });
        },
        open_ramk(item) {
            var e = app.globalData.getCache('userinfo');
            console.log(e);
            if (e.tourist == 1) {
                uni.showToast({
                    title: '请登录后认证！',
                    icon: 'none',
                    duration: 2000
                });
                return;
            }
            var id = item.currentTarget.dataset.id;
            uni.redirectTo({
                url: '/yl_welore/pages/packageE/certification_ramk/index?id=' + id
            });
        }
    }
};
</script>
<style>
page {
    background: linear-gradient(180deg, #4facfe 0%, #00f2fe 25%, #667eea 50%, #764ba2 75%, #f093fb 100%);
    background-attachment: fixed;
    min-height: 100vh;
}

.page-container {
    background: transparent;
    min-height: 100vh;
    position: relative;
}

.page-container::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(180deg,
        rgba(79, 172, 254, 0.1) 0%,
        rgba(0, 242, 254, 0.1) 25%,
        rgba(102, 126, 234, 0.1) 50%,
        rgba(118, 75, 162, 0.1) 75%,
        rgba(240, 147, 251, 0.1) 100%);
    pointer-events: none;
    z-index: 0;
}

/* 头部标题样式 */
.header-title {
    color: #ffffff;
    font-weight: 600;
    font-size: 36rpx;
    text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 认证容器 */
.certification-container {
    padding: 20rpx;
    padding-bottom: 40rpx;
    position: relative;
    z-index: 1;
}

/* 认证卡片 */
.certification-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20rpx);
    border-radius: 32rpx;
    margin-bottom: 30rpx;
    overflow: hidden;
    box-shadow: 0 20rpx 60rpx rgba(79, 172, 254, 0.2),
                0 8rpx 32rpx rgba(102, 126, 234, 0.15);
    transition: all 0.3s ease;
    position: relative;
    border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.certification-card:active {
    transform: translateY(2rpx) scale(0.98);
    box-shadow: 0 10rpx 30rpx rgba(102, 126, 234, 0.2);
}

/* 认证图标区域 */
.cert-icon-section {
    position: relative;
    padding: 40rpx 20rpx 20rpx;
    text-align: center;
    background: linear-gradient(135deg,
        rgba(79, 172, 254, 0.08) 0%,
        rgba(0, 242, 254, 0.08) 25%,
        rgba(102, 126, 234, 0.08) 50%,
        rgba(240, 147, 251, 0.08) 100%);
}

.icon-background {
    position: relative;
    display: inline-block;
    padding: 20rpx;
    border-radius: 24rpx;
    background: #ffffff;
    box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.1);
}

.icon-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: radial-gradient(circle at 20% 50%, rgba(79, 172, 254, 0.08) 2rpx, transparent 2rpx),
                      radial-gradient(circle at 80% 50%, rgba(0, 242, 254, 0.08) 2rpx, transparent 2rpx),
                      radial-gradient(circle at 50% 20%, rgba(240, 147, 251, 0.06) 2rpx, transparent 2rpx);
    background-size: 40rpx 40rpx, 60rpx 60rpx, 80rpx 80rpx;
    border-radius: 24rpx;
}

.cert-icon {
    width: 160rpx;
    position: relative;
    z-index: 2;
}

/* 状态徽章容器 */
.status-badge-container {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
}

.status-badge {
    display: flex;
    align-items: center;
    padding: 8rpx 16rpx;
    border-radius: 20rpx;
    font-size: 22rpx;
    font-weight: 600;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.status-badge.uncertified {
    background: linear-gradient(135deg, #36cfc9 0%, #52c41a 100%);
    color: #ffffff;
}

.status-badge.reviewing {
    background: linear-gradient(135deg, #faad14 0%, #ffc53d 100%);
    color: #ffffff;
}

.status-badge.certified {
    background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
    color: #ffffff;
}

.status-badge.rejected {
    background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
    color: #ffffff;
}

.status-icon {
    margin-right: 6rpx;
    font-size: 20rpx;
}

.status-text {
    font-size: 20rpx;
}

/* 认证信息区域 */
.cert-info-section {
    padding: 30rpx 40rpx;
    text-align: center;
}

.cert-name {
    font-size: 32rpx;
    font-weight: 700;
    color: #333333;
    margin-bottom: 12rpx;
    line-height: 1.4;
}

.cert-count {
    font-size: 24rpx;
    color: #999999;
    font-weight: 400;
}

/* 操作按钮区域 */
.cert-actions {
    display: flex;
    flex-direction: column;
    padding: 0rpx 30rpx;
}

/* 详情按钮 */
.detail-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #ffffff;
    border: none;
    border-radius: 20rpx;
    padding: 10rpx 32rpx;
    font-size: 26rpx;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
    margin: 20rpx 0rpx;
}

.detail-btn:active {
    transform: translateY(2rpx);
    box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.4);
}

/* 提交按钮 */
.submit-btn {
    width: 100%;
    border: none;
    border-radius: 20rpx;
    padding: 10rpx 32rpx;
    font-size: 28rpx;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    margin-bottom: 30rpx;
}

.submit-btn.primary {
    background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
    color: #ffffff;
    box-shadow: 0 8rpx 24rpx rgba(82, 196, 26, 0.3);
}

.submit-btn.primary:active {
    transform: translateY(2rpx);
    box-shadow: 0 4rpx 12rpx rgba(82, 196, 26, 0.4);
}

.submit-btn.resubmit {
    background: linear-gradient(135deg, #faad14 0%, #ffc53d 100%);
    color: #ffffff;
    box-shadow: 0 8rpx 24rpx rgba(250, 173, 20, 0.3);
}

.submit-btn.resubmit:active {
    transform: translateY(2rpx);
    box-shadow: 0 4rpx 12rpx rgba(250, 173, 20, 0.4);
}

.btn-icon {
    margin-right: 8rpx;
    font-size: 24rpx;
}

.btn-text {
    font-size: 26rpx;
}

/* 现代化弹窗样式 */
.modern-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modern-modal.show {
    opacity: 1;
    visibility: visible;
}

.modal-mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(10rpx);
}

.modal-content {
    position: relative;
    background: #ffffff;
    border-radius: 32rpx;
    margin: 40rpx;
    max-width: 600rpx;
    width: 100%;
    overflow: hidden;
    box-shadow: 0 40rpx 80rpx rgba(0, 0, 0, 0.2);
    transform: translateY(50rpx) scale(0.9);
    transition: all 0.3s ease;
}

.modern-modal.show .modal-content {
    transform: translateY(0) scale(1);
}

.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 30rpx 40rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-title {
    color: #ffffff;
    font-size: 32rpx;
    font-weight: 600;
}

.modal-close {
    width: 60rpx;
    height: 60rpx;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.modal-close:active {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(0.9);
}

.close-icon {
    color: #ffffff;
    font-size: 28rpx;
    font-weight: 600;
}

.modal-body {
    padding: 40rpx;
    font-size: 28rpx;
    line-height: 1.6;
    color: #333333;
    max-height: 400rpx;
    overflow-y: auto;
}

/* 滚动条样式 */
.modal-body::-webkit-scrollbar {
    width: 6rpx;
}

.modal-body::-webkit-scrollbar-track {
    background: #f0f2f5;
    border-radius: 3rpx;
}

.modal-body::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 3rpx;
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30rpx);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.certification-card {
    animation: fadeInUp 0.6s ease forwards;
}

.certification-card:nth-child(1) { animation-delay: 0.1s; }
.certification-card:nth-child(2) { animation-delay: 0.2s; }
.certification-card:nth-child(3) { animation-delay: 0.3s; }
.certification-card:nth-child(4) { animation-delay: 0.4s; }
.certification-card:nth-child(5) { animation-delay: 0.5s; }
</style>
