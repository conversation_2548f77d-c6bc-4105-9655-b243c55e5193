import { weBtoa, weAtob } from './atob.js';
class Util {
    static formatTime(date) {
        let year = date.getFullYear();
        let month = date.getMonth() + 1;
        let day = date.getDate();
        let hour = date.getHours();
        let minute = date.getMinutes();
        let second = date.getSeconds();
        return [year, month, day].map(this.formatNumber).join('-') + ' ' + [hour, minute, second].map(this.formatNumber).join(':');
    }
    static formatNumber(n) {
        n = n.toString();
        return n[1] ? n : '0' + n;
    }
    static _base64ToArrayBuffer(base64) {
        var atoba = require('./atob.js');
        var binary_string = atoba.weAtob(base64);
        var len = binary_string.length;
        var bytes = new Uint8Array(len);
        for (var i = 0; i < len; i++) {
            bytes[i] = binary_string.charCodeAt(i);
        }
        return bytes.buffer;
    }
}
module.exports = Util;
