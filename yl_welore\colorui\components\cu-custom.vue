<template>
    <view class="cu-custom" :style="'height:' + CustomBar + 'px'">
        <view :class="'cu-bar fixed ' + (bgImage != '' ? 'none-bg text-white bg-img' : '') + ' ' + bgColor"
            :style="'height:' + CustomBar + 'px;padding-top:' + StatusBar + 'px;' + (bgImage ? 'background-image:url( + bgImage+)' : '')">
            <view class="action" @tap="BackPage" v-if="isBack">
                <text class="cuIcon-back"></text>
                <slot name="backText"></slot>
            </view>
            <navigator v-if="isSearch" url="/yl_welore/pages/packageB/search/index" hover-class="none">
                <view class="action">
                    <image :src="http_root + 'addons/yl_welore/web/static/mineIcon/material/search.png'"
                        style="height: 60rpx; width: 60rpx" class=""></image>
                </view>
            </navigator>
            <slot class="action" name="left_z"></slot>
            <view class="action border-custom" v-if="isCustom"
                :style="'width:' + Custom.width + 'px;height:' + Custom.height + 'px;margin-left:calc(750rpx - ' + Custom.right + 'px)'">
                <text class="cuIcon-back" @tap="BackPage"></text>
                <text class="cuIcon-homefill" @tap="toHome"></text>
            </view>
            <view class="content" :style="'top:' + StatusBar + 'px'">
                <slot name="content"></slot>
                <!-- <text class="font-yl-1" v-if="ShowUid"
        style="position: fixed;top: {{StatusBar+25}}px;font-size: 30rpx;color:{{TextColor}};opacity: 1;">123456</text> -->
            </view>
            <slot name="right"></slot>
        </view>
    </view>
</template>

<script>
const app = getApp();
export default {
    /**
     * 组件的一些选项
     */
    name: 'cu-custom',
    data() {
        return {
            http_root: app.globalData.http_root,
            StatusBar: app.globalData.StatusBar,
            CustomBar: app.globalData.CustomBar,
            Custom: app.globalData.Custom,
            uid: ''
        };
    },
    pageLifetimes: {
        // 组件所在页面的生命周期函数
        show() {
            clearTimeout();
            console.log('----------------');
            setTimeout(() => {
                var e = app.globalData.getCache('userinfo');
                this.uid = e.id;
            }, 2000);
        },
        hide() { },
        resize() { }
    },
    mounted() {
        uni.getSystemInfo({
            success: (e) => {
                console.log(e);
                this.StatusBar = e.statusBarHeight;

                let capsule = uni.getMenuButtonBoundingClientRect();
                if (capsule) {
                    this.Custom = capsule;
                    this.CustomBar = capsule.bottom + capsule.top - e.statusBarHeight;
                } else {
                    this.CustomBar = e.statusBarHeight + 50;
                }
                var op = uni.getLaunchOptionsSync();
                app.globalData.store.setState({
                    isIphoneX: true
                });
                // if (e.model.search('iPhone X') != -1 || e.model.search('iPhone 11') != -1 || e.model.search('iPhone 12') != -1 || e.model.search('iPhone 13') != -1) {
                //     app.globalData.store.setState({
                //         isIphoneX: true
                //     });
                // }
            }
        });
        setTimeout(() => {
            var e = app.globalData.getCache('userinfo');
            this.uid = e.id;
        }, 1000);
    },
    /**
     * 组件的对外属性
     */
    props: {
        bgColor: {
            type: String,
            default: ''
        },
        isCustom: {
            type: Boolean,
            default: false
        },
        isBack: {
            type: Boolean,
            default: false
        },
        isSearch: {
            type: Boolean,
            default: false
        },
        bgImage: {
            type: String,
            default: ''
        },
        isBackAlert: {
            type: Boolean,
            default: false
        },
        TextColor: {
            type: String,
            default: 'rgb(0,0,0)'
            //value:"rgb(255,255,240)"
        },

        ShowUid: {
            type: Boolean,
            default: true
        }
    },
    /**
     * 组件的方法列表
     */
    methods: {
        BackPage() {
            console.log();
            var pages = getCurrentPages();
            var Page = pages[pages.length - 1]; //当前页
            var prevPage = pages[pages.length - 2]; //上一个页面
            if (pages.length == 1) {
                this.toHome();
                return;
            }
            console.log(this.isBackAlert);
            if (this.isBackAlert) {
                uni.enableAlertBeforeUnload({
                    message: '确认离开吗？',
                    success(res) {
                        console.log(res);
                    }
                });
            }
            uni.navigateBack();
        },
        toHome() {
            uni.reLaunch({
                url: '/yl_welore/pages/index/index'
            });
        }
    }
};
</script>
<style>
/* colorui/components/cu-custom.wxss */
</style>
