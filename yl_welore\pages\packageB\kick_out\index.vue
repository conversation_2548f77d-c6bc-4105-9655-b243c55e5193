<template>
    <view>
        <cu-custom bgColor="bg-white" :isBack="true" :isSearch="false">
            <view slot="backText">返回</view>
            <view slot="content" style="color: #2c2b2b; font-weight: 600; font-size: 36rpx">已关注</view>
        </cu-custom>
        <view class="cu-bar search bg-white">
            <view class="search-form round">
                <text class="cuIcon-search"></text>
                <input @input="search_name" :value="search_name_text" type="text" placeholder="搜索用户昵称" confirm-type="search" />
            </view>
            <view class="action">
                <button @tap="search_list" class="cu-btn bg-green shadow-blur round">搜索</button>
            </view>
        </view>
        <view class="cu-list menu">
            <view class="cu-item solid-bottom" v-for="(item, index) in list" :key="index">
                <view class="content padding-tb-sm">
                    <view>
                        <view class="cu-avatar round" :style="'background-image:url(' + item.user_head_sculpture + ');'"></view>
                        <text style="margin-left: 10px">{{ item.user_nick_name }}</text>
                    </view>
                    <view class="text-gray text-sm">关注时间：{{ item.ling_time }}</view>
                </view>

                <view class="action">
                    <button @tap="del_gz" :data-id="item.id" class="bg-red cu-btn round sm">移除</button>
                </view>
            </view>
        </view>
        <view :class="'cu-load ' + (!di_msg ? 'loading' : 'over')"></view>
    </view>
</template>

<script>
var app = getApp();
import http from '../../../util/http.js';
export default {
    /**
     * 页面的初始数据
     */
    data() {
        return {
            id: 0,
            page: 1,
            list: [],
            di_msg: false,
            search_name_text: ''
        };
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        this.id = options.id;
        this.get_q_gz();
    },
    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh() {
        setTimeout(function () {
            uni.hideNavigationBarLoading(); //完成停止加载
            uni.stopPullDownRefresh(); //停止下拉刷新
        }, 1500);
        this.page = 1;
        this.list = [];
        this.get_q_gz();
    },
    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom() {
        this.page = this.page + 1;
        this.get_q_gz();
    },
    /**
     * 用户点击右上角分享
     */
    onShareAppMessage() {},

    methods: {
        search_name(item) {
            this.search_name_text = item.detail.value;
        },

        del_gz(item) {
            var that = this;
            var id = item.currentTarget.dataset.id;
            uni.showModal({
                title: '提示',
                content: '确定要移除吗？',
                success(res) {
                    if (res.confirm) {
                        that.del_gz_do(id);
                    } else if (res.cancel) {
                        console.log('用户点击取消');
                    }
                }
            });
        },
        del_gz_do(id) {
            var b = app.globalData.api_root + 'Message/del_gz_do';
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.tory_id = this.id;
            params.id = id;
            http.POST(b, {
                params: params,
                success: function (res) {
                    uni.showToast({
                        title: res.data.msg,
                        icon: 'none',
                        duration: 2000
                    });
                    that.list = [];
                    that.page = 1;
                    setTimeout(() => {
                        that.get_q_gz();
                    }, 1000);
                },
                fail: function () {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        },

        search_list() {
            this.page = 1;
            this.list = [];
            this.get_q_gz();
        },
        get_q_gz() {
            var b = app.globalData.api_root + 'Message/get_q_gz';
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.id = this.id;
            params.page = this.page;
            params.search_name_text = this.search_name_text;
            var allMsg = that.list;
            http.POST(b, {
                params: params,
                success: function (res) {
                    console.log(res);
                    if (res.data.length == 0) {
                        that.di_msg = true;
                        return;
                    }
                    for (var i = 0; i < res.data.length; i++) {
                        allMsg.push(res.data[i]);
                    }
                    that.list = allMsg;
                },
                fail: function () {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        }
    }
};
</script>
<style>
page {
    background-color: #ffffff;
}
</style>
