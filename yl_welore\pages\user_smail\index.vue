<template>
    <view class="page-container">
        <!-- 现代化头部导航 -->
        <cu-custom bgColor="none" :isSearch="false" :isBack="design.elect_sheathe == 0">
            <view slot="backText">返回</view>
            <view slot="content" style="color: #000000; font-weight: 600; font-size: 36rpx">站内信</view>
        </cu-custom>
        <!-- 现代化Tab切换 -->
        <view class="modern-tab-container">
            <view class="tab-wrapper">
                <view v-for="(item, index) in tab_list" :key="index"
                    :class="['tab-item', current == index ? 'active' : '']" @tap="handleTabChange" :data-index="index">
                    <view class="tab-content">
                        <view class="tab-icon">
                            {{ index == 0 ? '🔔' : '💬' }}
                        </view>
                        <text class="tab-text">{{ item.name }}</text>
                        <view v-if="item.tag" class="tab-badge">{{ item.tag }}</view>
                    </view>
                </view>
                <view class="tab-indicator" :style="{ transform: `translateX(${current * 100}%)` }"></view>
            </view>

        </view>
        <!-- 管理按钮 -->
        <view v-if="current == '0'" class="manage-toggle-btn" @tap="toggleManageBar">
            <view class="manage-icon">{{ showManageBar ? '✕' : '⚙️' }}</view>
            <text class="manage-text">{{ showManageBar ? '关闭' : '管理' }}</text>
        </view>
        <!-- 主要内容区域 -->
        <view class="main-content">
            <!-- 系统通知页面 -->
            <view v-if="current == 0" class="notification-page">
                <!-- 操作栏 -->
                <view v-if="showManageBar" class="modern-operation-bar">
                    <view class="operation-left">
                        <view class="operation-icon">⚙️</view>
                        <text class="operation-text">管理通知</text>
                    </view>
                    <view class="operation-right">
                        <view v-if="!del" @tap="show_add" class="action-btn primary">
                            <view class="btn-icon">✓</view>
                            <text>选择</text>
                        </view>
                        <view v-if="del" @tap="show_add" class="action-btn secondary">
                            <view class="btn-icon">✕</view>
                            <text>取消</text>
                        </view>
                    </view>
                </view>

                <!-- 批量操作栏 -->
                <form @submit="del_do">
                    <view v-if="del" class="batch-operation-bar">
                        <view class="batch-select">
                            <checkbox @tap="quanxuan" name="cck" class="modern-checkbox"></checkbox>
                            <text class="select-text">全选</text>
                        </view>
                        <button formType="submit" class="batch-delete-btn">
                            <view class="delete-icon">🗑</view>
                            <text>删除选中</text>
                        </button>
                    </view>

                    <!-- 通知列表 -->
                    <view class="notification-list">
                        <checkbox-group name="is_check">
                            <view class="notification-card" v-for="(item, index) in list" :key="index">
                                <view class="card-main" @tap="mod_show" :data-key="index">
                                    <!-- 卡片头部 -->
                                    <view class="card-header">
                                        <view class="notification-type">
                                            <view class="type-icon">
                                                {{ item.skip_type == 3 ? '💬' : '🔔' }}
                                            </view>
                                            <text class="type-text">
                                                {{ item.skip_type == 3 ? '评论通知' : '系统通知' }}
                                            </text>
                                        </view>
                                        <view v-if="!check_box" class="status-indicator">
                                            <view :class="['status-dot', item.status == 0 ? 'unread' : 'read']"></view>
                                            <text :class="['status-text', item.status == 0 ? 'unread' : 'read']">
                                                {{ item.status == 0 ? '未读' : '已读' }}
                                            </text>
                                        </view>
                                    </view>

                                    <!-- 消息内容 -->
                                    <view class="card-content">
                                        <text class="message-text">{{ item.maring }}</text>
                                    </view>

                                    <!-- 卡片底部 -->
                                    <view class="card-footer">
                                        <view class="time-info">
                                            <view class="time-icon">🕐</view>
                                            <text class="time-text">{{ item.clue_time }}</text>
                                        </view>
                                        <view v-if="item.skip_type != 0" @tap.stop.prevent="url_nav" :data-id="item.id"
                                            :data-skip_type="item.skip_type" :data-paper_id="item.paper_id"
                                            class="detail-action">
                                            <text class="detail-text">查看详情</text>
                                            <view class="detail-arrow">›</view>
                                        </view>
                                    </view>
                                </view>

                                <!-- 选择框 -->
                                <view v-if="check_box" class="card-checkbox">
                                    <checkbox class="modern-item-checkbox" :value="item.id"
                                        :checked="item.is == 0 ? false : true"></checkbox>
                                </view>
                            </view>
                        </checkbox-group>
                    </view>
                </form>

                <!-- 系统通知空状态 -->
                <view v-if="list.length === 0 && my_list_di" class="empty-state">
                    <view class="empty-container">
                        <view class="empty-icon">🔔</view>
                        <view class="empty-title">暂无系统通知</view>
                        <view class="empty-description">当有新的系统消息时，会在这里显示</view>
                    </view>
                </view>
                <!-- 现代化模态框 -->
                <view :class="'modern-modal ' + (modalShow ? 'show' : '')" @touchmove.stop.prevent="true"
                    @tap="hideModal">
                    <view class="modal-container">
                        <view class="modal-header">
                            <view class="modal-title">
                                <view class="title-icon">�</view>
                                <text>通知详情</text>
                            </view>
                            <view class="modal-close" @tap="hideModal">
                                <text class="close-icon">✕</text>
                            </view>
                        </view>
                        <view class="modal-body">
                            <scroll-view scroll-y class="modal-scroll">
                                <text class="modal-text">{{ info.maring }}</text>
                            </scroll-view>
                        </view>
                    </view>
                </view>

                <!-- 用户留言页面 -->


                <!-- 加载状态指示器 -->
                <!-- <view v-if="list.length > 0" class="modern-loading">
                    <view v-if="!my_list_di" class="loading-content">
                        <view class="loading-spinner"></view>
                        <text class="loading-text">加载中...</text>
                    </view>
                    <view v-else class="loading-end">
                        <view class="end-line"></view>
                        <text class="end-text">没有更多了</text>
                        <view class="end-line"></view>
                    </view>
                </view> -->
            </view>
            <view v-if="current == 1" class="chat-page">
                <view class="modern-chat-list">
                    <view
                        :class="'modern-chat-card ' + (modalName_er == 'move-box-er-' + ll_index ? 'swiped' : '') + (item.deleting ? ' deleting' : '')"
                        @touchstart="ListTouchStart_er" @touchmove="ListTouchMove_er" @touchend="ListTouchEnd_er"
                        :data-target="'move-box-er-' + ll_index" v-for="(item, ll_index) in l_list" :key="ll_index">
                        <!-- 聊天卡片主体 -->
                        <view class="chat-card-main" @tap="url_url" :data-id="item.recent_user_id">
                            <!-- 头像区域 -->
                            <view class="avatar-section">
                                <view class="user-avatar" :style="'background-image:url(' + item.re_user_head + ');'">
                                    <!-- 未读消息数量 -->
                                    <view v-if="item.msg_count > 0" class="unread-badge">
                                        {{ item.msg_count > 99 ? '99+' : item.msg_count }}
                                    </view>
                                </view>
                                <!-- 在线状态指示器 -->
                                <view class="online-status"></view>
                            </view>

                            <!-- 聊天内容区域 -->
                            <view class="chat-info">
                                <view class="chat-top">
                                    <view class="username">{{ item.user_nick_name }}</view>
                                    <view class="chat-time">{{ item.blatter_time }}</view>
                                </view>
                                <view class="last-message">
                                    <text class="message-text">{{ item.last_msg }}</text>
                                </view>
                            </view>
                        </view>

                        <!-- 滑动操作按钮 -->
                        <view class="swipe-actions">
                            <view class="action-btn close-action">关闭</view>
                            <view @tap="del_my_contacts" :data-id="item.id" :data-index="ll_index"
                                class="action-btn delete-action">删除</view>
                        </view>
                    </view>
                </view>

                <!-- 用户留言空状态 -->
                <view v-if="l_list.length === 0 && my_list_di" class="empty-state">
                    <view class="empty-container">
                        <view class="empty-icon">💬</view>
                        <view class="empty-title">暂无用户留言</view>
                        <view class="empty-description">当有用户给您发送私信时，会在这里显示</view>
                    </view>
                </view>

                <!-- 用户留言加载状态指示器 -->
                <view v-if="l_list.length > 0" class="modern-loading">
                    <view v-if="!my_list_di" class="loading-content">
                        <view class="loading-spinner"></view>
                        <text class="loading-text">加载中...</text>
                    </view>
                    <view v-else class="loading-end">
                        <view class="end-line"></view>
                        <text class="end-text">没有更多了</text>
                        <view class="end-line"></view>
                    </view>
                </view>
            </view>
        </view>
        <tabbar id="tabbar" v-if="design.elect_sheathe != 0 || design.shop_arbor == 0" :tabbar="tabbar" :activeIndex="3"></tabbar>
    </view>
</template>

<script>
import tabbar from "@/yl_welore/util/tabbarComponent/modern-tabbar";
import uiTab from '@/yl_welore/colorui/ui-tab/ui-tab';
const app = getApp();
var http = require('../../util/http.js');
export default {
    components: {
        tabbar,
        uiTab
    },
    /**
     * 页面的初始数据
     */
    data() {
        return {
            tab_list: [
                {
                    name: '系统通知',
                },
                {
                    name: '用户留言',
                }
            ],
            http_root: app.globalData.http_root,
            modalShow: false,
            current: 0,
            user_phone: '',
            list: [],
            info: '',
            l_list: [],
            del_mod: false,
            bj_mod: false,
            page: 1,
            l_page: 1,
            my_list_di: false,
            user_male: 0,
            actions: [
                {
                    name: '删除',
                    color: '#fff',
                    fontsize: '20',
                    width: 100,
                    background: '#ed3f14'
                },
                {
                    name: '取消',
                    width: 100,
                    color: '#80848f',
                    fontsize: '20'
                }
            ],
            sum: 0,
            del: false,
            check_box: false,
            quan: false,
            tabbar: {},
            design: {},
            modalName_er: null,
            modalName: null,
            ListTouchStart: 0,
            ListTouchDirection: null,
            del_id: '',
            showManageBar: false
        };
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        this.page = 1;
        this.list = [];
        var e = app.globalData.getCache('userinfo');
        if (!e) {
            console.log(1);
            uni.login({
                success: (res) => {
                    var params = new Object();
                    params.code = res.code;
                    http.POST(app.globalData.api_root + 'Login/index', {
                        params: params,
                        success: (open) => {
                            // console.log(open);
                            var data = new Object();
                            data.openid = open.data.info.openid;
                            data.session_key = open.data.info.session_key;
                            http.POST(app.globalData.api_root + 'Login/add_tourist', {
                                params: data,
                                success: (d) => {
                                    //console.log('1111111');
                                    //console.log(d);
                                    app.globalData.setCache('userinfo', d.data.info);
                                    this.get_my_rec();
                                }
                            });
                        }
                    });
                }
            });
        } else {
            this.user_phone = e.user_phone;
            this.get_my_rec();
        }
    },
    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {
        //wx.hideTabBar();
        var dd = uni.getStorageSync('is_diy');
        console.log(dd);
        if (dd) {
            this.design = dd;
            app.globalData.editTabbar();
        } else {
            this.get_diy();
        }
        if (this.current == 1) {
            this.l_page = 1;
            this.l_list = [];
            this.get_my_private();
        }
        // this.selectComponent('#tabbar').get_user();
    },
    /**
     * 下拉刷新
     */
    onPullDownRefresh() {
        //模拟加载
        setTimeout(() => {
            uni.hideNavigationBarLoading(); //完成停止加载
            uni.stopPullDownRefresh(); //停止下拉刷新
        }, 1500);
        this.page = 1;
        this.l_page = 1;
        this.list = [];
        this.l_list = [];
        if (this.current == '0') {
            this.get_my_rec();
        }
        if (this.current == '1') {
            this.get_my_private();
        }
    },
    /**
     * 加载下一页
     */
    onReachBottom() {
        if (this.current == '0') {
            this.page = this.page + 1;
            this.get_my_rec();
        } else {
            this.l_page = this.l_page + 1;
            this.get_my_private();
        }
    },
    methods: {
        // 返回按钮
        goBack() {
            uni.navigateBack();
        },
        // 切换管理栏显示
        toggleManageBar() {
            this.showManageBar = !this.showManageBar;
            // 如果隐藏管理栏，同时重置相关状态
            if (!this.showManageBar) {
                this.del = false;
                this.check_box = false;
            }
        },
        // 新的Tab切换方法
        handleTabChange(e) {
            const index = e.currentTarget.dataset.index;
            this.current = index;
            this.modalName_er = null;
            this.modalName = null;
            if (index == 0) {
                this.page = 1;
                this.list = [];
                this.my_list_di = false;
                this.get_my_rec();
            } else {
                this.l_page = 1;
                this.l_list = [];
                this.my_list_di = false;
                this.get_my_private();
            }
        },
        mod_show(d) {
            var index = d.currentTarget.dataset.key;
            this.modalShow = true;
            this.info = this.list[index];
            var b = app.globalData.api_root + 'Conversation/overall_set';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.id = this.list[index].id;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    this.page = 1;
                    this.list = [];
                    this.get_my_rec();
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => { }
                    });
                }
            });
        },
        show_add() {
            this.del = !this.del;
            this.check_box = !this.check_box;
        },
        quanxuan(k) {
            this.quan = !this.quan;
            var list = this.list;
            var arr = [];
            for (let i = 0; i < list.length; i++) {
                if (this.quan) {
                    this.$set(this.list[i], 'is', 1);
                } else {
                    this.$set(this.list[i], 'is', 0);
                }
            }
        },
        get_select(c) {
            console.log(c);
            var key = c.detail.index;
            var id = c.currentTarget.dataset.id;
            var index = c.currentTarget.dataset.index;
            if (key == 0) {
                this.del_my_contacts(id, index);
            }
        },
        /**
         * 删除最近联系人
         */
        del_my_contacts(dd) {
            var id = dd.currentTarget.dataset.id;
            var index = dd.currentTarget.dataset.index;

            // 添加触觉反馈
            uni.vibrateShort({
                type: 'medium'
            });

            // 为要删除的项添加动画类标记
            this.$set(this.l_list[index], 'deleting', true);

            // 等待动画完成后再删除数据
            setTimeout(() => {
                var b = app.globalData.api_root + 'User/del_my_contacts';
                var e = app.globalData.getCache('userinfo');
                var params = new Object();
                params.token = e.token;
                params.openid = e.openid;
                params.id = id;
                http.POST(b, {
                    params: params,
                    success: (res) => {
                        console.log(res);
                        var lists = this.l_list;
                        lists.splice(index, 1);
                        this.l_list = lists;
                        this.modalName_er = null;
                    },
                    fail: () => {
                        // 如果删除失败，移除动画类
                        if (this.l_list[index]) {
                            this.$set(this.l_list[index], 'deleting', false);
                        }
                        uni.showModal({
                            title: '提示',
                            content: '网络繁忙，请稍候重试！',
                            showCancel: false,
                            success: () => { }
                        });
                    }
                });
            }, 400); // 动画持续时间
        },
        /**
         * 跳转
         */
        url_url(c) {
            var e = app.globalData.getCache('userinfo');
            if (!this.user_phone) {
                uni.showModal({
                    title: '提示',
                    content: '您尚未绑定手机号！',
                    confirmText: '去绑定',
                    success: (res) => {
                        if (res.confirm) {
                            uni.navigateTo({
                                url: '/yl_welore/pages/packageC/service_centre/index'
                            });
                        }
                    }
                });
            } else {
                var id = c.currentTarget.dataset.id;
                uni.navigateTo({
                    url: '/yl_welore/pages/packageB/private_letter/index?id=' + id
                });
            }
        },
        handleChange(detail) {
            var key = detail.detail.index;
            this.current = key;
            this.modalName_er = null;
            this.modalName = null;
            if (key == '0') {
                this.page = 1;
                this.list = [];
                this.my_list_di = false;
                this.get_my_rec();
            } else {
                this.l_page = 1;
                this.l_list = [];
                this.my_list_di = false;
                this.get_my_private();
            }
        },
        get_diy() {
            var b = app.globalData.api_root + 'User/get_diy';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.uid = e.uid;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    this.design = res.data;
                    app.globalData.editTabbar();
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => { }
                    });
                }
            });
        },
        longPress(e) {
            console.log(e);
            this.del_id = e.currentTarget.dataset.id;
            this.del_mod = true;
        },
        /**
         *
         */
        url_nav(d) {
            var paper_id = d.currentTarget.dataset.paper_id;
            var skip_type = d.currentTarget.dataset.skip_type;
            var id = d.currentTarget.dataset.id;
            this.up_user_smail(id);
            if (skip_type == 1) {
                uni.navigateTo({
                    url: '/yl_welore/pages/packageC/confession/index'
                });
                return;
            }
            if (skip_type == 2) {
                uni.navigateTo({
                    url: '/yl_welore/pages/packageA/user_order/index'
                });
                return;
            }
            if (skip_type == 3 && paper_id != 0) {
                uni.navigateTo({
                    url: '/yl_welore/pages/packageA/article/index?id=' + paper_id
                });
                return;
            }
            if (skip_type == 4) {
                uni.navigateTo({
                    url: '/yl_welore/pages/packageC/delete_posts/index'
                });
                return;
            }
            if (skip_type == 5) {
                uni.navigateTo({
                    url: '/yl_welore/pages/packageC/user_report/index'
                });
                return;
            }
        },
        del_user_smail(e) {
            this.del_id = e.currentTarget.dataset.id;
            this.del_mod = true;
        },
        /**
         *
         */
        get_all_mod() {
            this.bj_mod = true;
        },
        hideModal() {
            this.del_mod = false;
            this.bj_mod = false;
            this.modalShow = false;
        },
        /**
         * 全部标为已读
         */
        get_all() {
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.uid = e.uid;
            var b = app.globalData.api_root + 'User/up_user_smail_all';
            http.POST(b, {
                params: params,
                success: (res) => { },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => { }
                    });
                }
            });
        },
        /**
         * 未读
         */
        up_user_smail(dd) {
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.id = dd;
            var b = app.globalData.api_root + 'User/up_user_smail';
            http.POST(b, {
                params: params,
                success: (res) => {
                    //console.log(res);
                    //that.get_my_rec();
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => { }
                    });
                }
            });
        },
        /**
         * 删除站内信
         */
        del_do(dd) {
            console.log(dd);
            var id = dd.detail.value.is_check;
            if (id.length == 0) {
                uni.showToast({
                    title: '请选择一个通知',
                    icon: 'none',
                    duration: 2000
                });
                return;
            }
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.id = id.toString();
            var b = app.globalData.api_root + 'User/del_user_smail';
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.status == 'success') {
                        this.del_mod = false;
                        this.page = 1;
                        this.list = [];
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                        this.get_my_rec();
                    } else {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => { }
                    });
                }
            });
        },
        /**
         * 消息
         */
        get_my_private() {
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.uid = e.uid;
            params.page = this.l_page;
            var b = app.globalData.api_root + 'User/get_my_private';
            var allMsg = this.l_list;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.status == 'success') {
                        for (var i = 0; i < res.data.info.length; i++) {
                            allMsg.push(res.data.info[i]);
                        }
                        this.l_list = allMsg;
                        this.sum = res.data.sum;
                        if (res.data.info.length == 0 || allMsg.length < 10) {
                            this.my_list_di = true;
                        }
                    } else {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => { }
                    });
                }
            });
        },
        get_my_rec() {
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.uid = e.uid;
            params.page = this.page;
            var b = app.globalData.api_root + 'User/get_user_smail';
            var allMsg = this.list;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.status == 'success') {
                        for (var i = 0; i < res.data.info.length; i++) {
                            allMsg.push(res.data.info[i]);
                        }
                        // if (res.data.user_male > 0) {
                        //     this.$set(this.tab_list[0], 'tag', res.data.user_male);
                        // }
                        // if (res.data.user_to_count > 0) {
                        //     this.$set(this.tab_list[1], 'tag', res.data.user_to_count);
                        // }
                        this.list = allMsg;
                        this.user_male = res.data.user_male;
                        if (res.data.info.length == 0 || allMsg.length < 10) {
                            this.my_list_di = true;
                        }
                    } else {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => { }
                    });
                }
            });
        },
        // ListTouch触摸开始
        ListTouchStart_er(e) {
            this.ListTouchStart = e.touches[0].pageX;
        },
        // ListTouch计算方向
        ListTouchMove_er(e) {
            this.ListTouchDirection = e.touches[0].pageX - this.ListTouchStart > 0 ? 'right' : 'left';
        },
        // ListTouch计算滚动
        ListTouchEnd_er(e) {
            if (this.ListTouchDirection == 'left') {
                this.modalName_er = e.currentTarget.dataset.target;
                // 添加触觉反馈
                uni.vibrateShort({
                    type: 'light'
                });
            } else {
                this.modalName_er = null;
            }
            this.ListTouchDirection = null;
        }
    }
};
</script>
<style>
/* 全局页面样式 */
/* 页面容器 */
.page-container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background: #f0f8ff;
}

/* 现代化头部导航 */
.modern-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
    background: #f0f8ff;
    padding: 20rpx 0;
    z-index: 1000;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20rpx 40rpx;
    margin-top: 80rpx;
}

.back-btn {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10rpx);
}

.back-icon {
    color: #ffffff;
    font-size: 48rpx;
    font-weight: 300;
}

.header-title {
    display: flex;
    align-items: center;
    gap: 16rpx;
}

.title-icon {
    font-size: 48rpx;
}

.title-text {
    color: #ffffff;
    font-size: 36rpx;
    font-weight: 600;
    letter-spacing: 1rpx;
}

.header-actions {
    width: 80rpx;
}

/* 现代化Tab切换 */
.modern-tab-container {
    position: fixed;
    top: calc(var(--status-bar-height, 44px) + 120rpx);
    left: 0;
    right: 0;
    width: 100%;
    padding: 0 40rpx 20rpx;
    z-index: 999;
}

.tab-wrapper {
    position: relative;
    background: rgba(255, 255, 255, 0.15);
    border-radius: 25rpx;
    padding: 8rpx;
    backdrop-filter: blur(20rpx);
    display: flex;
}

.tab-item {
    flex: 1;
    position: relative;
    z-index: 2;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.tab-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20rpx 0;
    gap: 8rpx;
}

.tab-icon {
    font-size: 32rpx;
    transition: all 0.3s ease;
}

.tab-text {
    font-size: 28rpx;
    font-weight: 500;
    color: #999999;
    transition: all 0.3s ease;
}

.tab-item.active .tab-text {
    color: #4A90E2;
    font-weight: 600;
}

.tab-badge {
    position: absolute;
    top: 8rpx;
    right: 20rpx;
    background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
    color: #ffffff;
    border-radius: 20rpx;
    font-size: 20rpx;
    font-weight: 600;
    min-width: 32rpx;
    height: 32rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.4);
}

.tab-indicator {
    position: absolute;
    top: 8rpx;
    left: 8rpx;
    width: calc(50% - 8rpx);
    height: calc(100% - 16rpx);
    background: #ffffff;
    border-radius: 20rpx;
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

/* 管理切换按钮 - 右侧悬浮 */
.manage-toggle-btn {
    position: fixed;
    right: 30rpx;
    bottom: 23%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 4rpx;
    width: 90rpx;
    height: 90rpx;
    background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
    border-radius: 50%;
    transition: all 0.3s ease;
    z-index: 1001;
}

.manage-icon {
    font-size: 28rpx;
    color: #ffffff;
    line-height: 1;
}

.manage-text {
    font-size: 18rpx;
    color: #ffffff;
    font-weight: 500;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
}

/* 主要内容区域 */
.main-content {
    flex: 1;
    background: #f8f9fc;
    border-radius: 40rpx 40rpx 0 0;
    margin-top: 140rpx;
    min-height: calc(100vh - 300rpx);
}

/* 通知页面样式 */
.notification-page {
    padding: 40rpx 0;
}

.modern-operation-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0 40rpx 20rpx;
    padding: 20rpx 24rpx;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fc 100%);
    border-radius: 16rpx;
    box-shadow: 0 4rpx 20rpx rgba(74, 144, 226, 0.08);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    animation: slideDown 0.3s ease-out;
}

.operation-left {
    display: flex;
    align-items: center;
    gap: 16rpx;
}

.operation-icon {
    font-size: 32rpx;
}

.operation-text {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
}

.operation-right {
    display: flex;
    gap: 20rpx;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 8rpx;
    padding: 16rpx 24rpx;
    font-size: 28rpx;
    font-weight: 500;
    transition: all 0.3s ease;
}

.action-btn.primary {
    background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
    color: #ffffff;
    box-shadow: 0 4rpx 16rpx rgba(74, 144, 226, 0.3);
}

.action-btn.secondary {
    background: linear-gradient(135deg, #8799a3 0%, #a0adb8 100%);
    color: #ffffff;
    box-shadow: 0 4rpx 16rpx rgba(135, 153, 163, 0.3);
}

.btn-icon {
    font-size: 24rpx;
}

/* 批量操作栏 */
.batch-operation-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0 40rpx 30rpx;
    padding: 24rpx 30rpx;
    background: linear-gradient(135deg, #fff5f5 0%, #ffe6e6 100%);
    border-radius: 20rpx;
    border: 2rpx solid #ffb3b3;
}

.batch-select {
    display: flex;
    align-items: center;
    gap: 16rpx;
}

.modern-checkbox {
    transform: scale(0.8);
    accent-color: #4A90E2;
}

.select-text {
    font-size: 30rpx;
    font-weight: 500;
    color: #333;
}

.batch-delete-btn {
    display: flex;
    align-items: center;
    gap: 8rpx;
    background: linear-gradient(135deg, #ff6b6b 0%, #ff8e8e 100%);
    color: #ffffff;
    padding: 16rpx 24rpx;
    border-radius: 25rpx;
    font-size: 28rpx;
    font-weight: 500;
    border: none;
    box-shadow: 0 4rpx 16rpx rgba(255, 107, 107, 0.3);
    transition: all 0.3s ease;
}

.delete-icon {
    font-size: 24rpx;
}

/* 通知列表 */
.notification-list {
    padding: 0 40rpx;
}

.notification-card {
    position: relative;
    margin-bottom: 24rpx;
    border-radius: 24rpx;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fc 100%);
    box-shadow: 0 8rpx 32rpx rgba(74, 144, 226, 0.08);
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 2rpx solid transparent;
}

.notification-card:hover {
    transform: translateY(-4rpx);
    box-shadow: 0 16rpx 48rpx rgba(74, 144, 226, 0.15);
    border-color: rgba(74, 144, 226, 0.2);
}

.card-main {
    padding: 32rpx;
    width: 100%;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24rpx;
}

.notification-type {
    display: flex;
    align-items: center;
    gap: 12rpx;
}

.type-icon {
    font-size: 32rpx;
}

.type-text {
    font-size: 30rpx;
    font-weight: 600;
    color: #333;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 8rpx;
}

.status-dot {
    width: 16rpx;
    height: 16rpx;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.status-dot.unread {
    background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
    box-shadow: 0 0 16rpx rgba(255, 107, 107, 0.5);
}

.status-dot.read {
    background: linear-gradient(45deg, #52c41a, #73d13d);
}

.status-text {
    font-size: 24rpx;
    font-weight: 500;
}

.status-text.unread {
    color: #ff6b6b;
}

.status-text.read {
    color: #52c41a;
}

.card-content {
    margin: 24rpx 0;
}

.message-text {
    font-size: 30rpx;
    color: #666;
    line-height: 1.6;
    word-break: break-all;
}

.card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 24rpx;
    padding-top: 24rpx;
    border-top: 2rpx solid #f0f0f0;
}

.time-info {
    display: flex;
    align-items: center;
    gap: 8rpx;
}

.time-icon {
    font-size: 24rpx;
    color: #999;
}

.time-text {
    font-size: 26rpx;
    color: #999;
}

.detail-action {
    display: flex;
    align-items: center;
    gap: 8rpx;
    padding: 12rpx 20rpx;
    background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
    color: #ffffff;
    border-radius: 20rpx;
    font-size: 26rpx;
    font-weight: 500;
    transition: all 0.3s ease;
}

.detail-text {
    font-size: 26rpx;
}

.detail-arrow {
    font-size: 28rpx;
    font-weight: 300;
}

.card-checkbox {
    position: absolute;
    top: 24rpx;
    right: 24rpx;
    border-radius: 50%;
    backdrop-filter: blur(10rpx);
}

.modern-item-checkbox {
    transform: scale(0.8);
    accent-color: #4A90E2;
}

/* 聊天页面样式 */
.chat-page {
    padding: 40rpx 0;
}

.chat-header {
    margin: 0 40rpx 30rpx;
}

.chat-title {
    display: flex;
    align-items: center;
    gap: 16rpx;
    padding: 30rpx;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fc 100%);
    border-radius: 20rpx;
    box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.1);
}

.title-icon {
    font-size: 32rpx;
}

.title-text {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
}

/* 现代化聊天列表 */
.modern-chat-list {
    padding: 0 40rpx;
}

.modern-chat-card {
    position: relative;
    margin-bottom: 24rpx;
    border-radius: 24rpx;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fc 100%);
    box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.08);
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateX(0);
}

.modern-chat-card.swiped {
    transform: translateX(-0rpx);
}

.modern-chat-card.deleting {
    animation: slideOutAndFade 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.chat-card-main {
    display: flex;
    align-items: center;
    padding: 28rpx;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fc 100%);
    transition: all 0.3s ease;
}

.avatar-section {
    position: relative;
    margin-right: 24rpx;
}

.user-avatar {
    width: 100rpx;
    height: 100rpx;
    border-radius: 50%;
    background-size: cover;
    background-position: center;
    border: 4rpx solid #ffffff;
    box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.15);
    position: relative;
}

.unread-badge {
    position: absolute;
    top: -8rpx;
    right: -8rpx;
    background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
    color: #ffffff;
    border-radius: 50%;
    font-size: 22rpx;
    font-weight: 600;
    min-width: 40rpx;
    height: 40rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 3rpx solid #ffffff;
    box-shadow: 0 4rpx 16rpx rgba(255, 107, 107, 0.4);
}

.online-status {
    position: absolute;
    bottom: 8rpx;
    right: 8rpx;
    width: 24rpx;
    height: 24rpx;
    background: linear-gradient(45deg, #52c41a, #73d13d);
    border-radius: 50%;
    border: 4rpx solid #ffffff;
    box-shadow: 0 2rpx 8rpx rgba(82, 196, 26, 0.3);
}

.chat-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 12rpx;
}

.chat-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.username {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
}

.chat-time {
    font-size: 24rpx;
    color: #999;
    background: #f0f0f0;
    padding: 6rpx 12rpx;
    border-radius: 12rpx;
}

.last-message {
    margin-top: 8rpx;
}

.message-text {
    font-size: 28rpx;
    color: #666;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

.swipe-actions {
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
    width: 260rpx;
    display: flex;
    transform: translateX(100%);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modern-chat-card.swiped .swipe-actions {
    transform: translateX(0);
}

.action-btn {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    font-size: 28rpx;
    font-weight: 500;
    transition: all 0.3s ease;
}

.close-action {
    background: linear-gradient(135deg, #8799a3 0%, #a0adb8 100%);
}

.delete-action {
    background: linear-gradient(135deg, #ff6b6b 0%, #ff8e8e 100%);
}

/* 现代化模态框 */
.modern-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1110;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(10rpx);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    pointer-events: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modern-modal.show {
    opacity: 1;
    pointer-events: auto;
}

.modal-container {
    width: 85%;
    max-width: 600rpx;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fc 100%);
    border-radius: 24rpx;
    overflow: hidden;
    box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
    transform: scale(0.9);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modern-modal.show .modal-container {
    transform: scale(1);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 32rpx;
    background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
    color: #ffffff;
}

.modal-title {
    display: flex;
    align-items: center;
    gap: 12rpx;
    font-size: 32rpx;
    font-weight: 600;
}

.modal-close {
    width: 60rpx;
    height: 60rpx;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.close-icon {
    font-size: 32rpx;
    font-weight: 300;
}

.modal-body {
    padding: 40rpx;
}

.modal-scroll {
    max-height: 800rpx;
    text-align: left;
}

.modal-text {
    font-size: 30rpx;
    line-height: 1.6;
    color: #333;
}

/* 现代化加载状态 */
.modern-loading {
    padding: 60rpx 40rpx;
    display: flex;
    justify-content: center;
    align-items: center;
}

.loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20rpx;
}

.loading-spinner {
    width: 60rpx;
    height: 60rpx;
    border: 6rpx solid #f0f0f0;
    border-top: 6rpx solid #4A90E2;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.loading-text {
    font-size: 28rpx;
    color: #999;
}

.loading-end {
    display: flex;
    align-items: center;
    gap: 20rpx;
    width: 100%;
}

.end-line {
    flex: 1;
    height: 2rpx;
    background: linear-gradient(90deg, transparent 0%, #e0e0e0 50%, transparent 100%);
}

.end-text {
    font-size: 26rpx;
    color: #999;
    white-space: nowrap;
}

/* 空状态样式 */
.empty-state {
    padding: 80rpx 40rpx;
    display: flex;
    justify-content: center;
    align-items: center;
}

.empty-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 24rpx;
    padding: 60rpx 40rpx;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fc 100%);
    border-radius: 24rpx;
    box-shadow: 0 8rpx 32rpx rgba(74, 144, 226, 0.08);
    border: 2rpx solid rgba(74, 144, 226, 0.1);
    width: 100%;
    text-align: center;
}

.empty-icon {
    font-size: 80rpx;
    opacity: 0.6;
    margin-bottom: 8rpx;
}

.empty-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 8rpx;
}

.empty-description {
    font-size: 28rpx;
    color: #666;
    line-height: 1.5;
    text-align: center;
}

/* 动画效果 */
@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

@keyframes slideOutAndFade {
    0% {
        transform: translateX(-260rpx);
        opacity: 1;
        height: auto;
        margin-bottom: 24rpx;
    }

    50% {
        transform: translateX(-400rpx);
        opacity: 0.3;
    }

    100% {
        transform: translateX(-600rpx);
        opacity: 0;
        height: 0;
        margin-bottom: 0;
        padding-top: 0;
        padding-bottom: 0;
    }
}

@keyframes pulse {

    0%,
    100% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.1);
    }
}

@keyframes slideDown {
    0% {
        opacity: 0;
        transform: translateY(-20rpx);
        max-height: 0;
    }

    100% {
        opacity: 1;
        transform: translateY(0);
        max-height: 200rpx;
    }
}

/* 按钮样式重置 */
button::after {
    display: none;
}

button {
    background: none;
    border: none;
    padding: 0;
    margin: 0;
    line-height: normal;
}
</style>
