<template>
    <view>
        <cu-custom bgColor="bg-white" :isBack="true" :isSearch="false">
            <view slot="backText">返回</view>
            <view slot="content" style="color: #2c2b2b; font-weight: 600; font-size: 36rpx">提现须知</view>
        </cu-custom>
        <view style="width: 91%; padding: 20px">
            <mp-html @linktap="linktap" :selectable="true" :copy-link="false" :lazy-load="true" :content="article" />
        </view>
    </view>
</template>

<script>
import http from '../../../util/http.js';
const app = getApp();
export default {
    components: {},
    data() {
        return {
            info: [],
            article: ''
        };
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {},
    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {
        const b = app.globalData.api_root + 'User/get_raws_setting';
        const e = app.globalData.getCache('userinfo');
        let params = new Object();
        params.token = e.token;
        params.openid = e.openid;
        http.POST(b, {
            params: params,
            success: (res) => {
                console.log(res);
                this.article = res.data.notice;
            },
            fail: () => {
                uni.showModal({
                    title: '提示',
                    content: '网络繁忙，请稍候重试！',
                    showCancel: false,
                    success: (res) => {}
                });
            }
        });
    },
    methods: {
        linktap() {
            console.log('占位：函数 linktap 未声明');
        }
    }
};
</script>
<style>
.classify {
    height: 100%;
    margin: 25rpx;
    text-align: center;
    background-color: #fff;
    border-radius: 25rpx;
    box-sizing: border-box;
    display: inline-block;
    position: relative;
}
.class_img {
    width: 130rpx;
    height: 130rpx;
    border-radius: 100%;
}

/* 标题要居中 */
.nav-title {
    position: absolute;
    text-align: center;
    max-width: 377rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
    font-size: 36rpx;
    color: #2c2b2b;
    font-weight: 600;
}

.nav-capsule {
    display: flex;
    align-items: center;
    margin-left: 20rpx;
    width: 50rpx;
    justify-content: space-around;
    border-radius: 50%;
    margin-top: 54rpx;
    z-index: 999999999;
}

.navbar-v-line {
    width: 1px;
    height: 32rpx;
    background-color: #f3f3f3;
}

.back-pre {
    width: 32rpx;
    height: 32rpx;
    margin-top: 11rpx;
    margin-left: 0rpx;
}

/**
     * 弹窗
     */
.show-btn {
    margin-top: 100rpx;
    color: #22cc22;
}

.modal-mask {
    width: 100%;
    height: 100%;
    position: fixed;
    top: 0;
    left: 0;
    background: #000;
    opacity: 0.5;
    overflow: hidden;
    z-index: 9000;
    color: #fff;
}

.modal-dialog {
    width: 540rpx;
    overflow: hidden;
    position: fixed;
    top: 45%;
    left: 0;
    z-index: 9999;
    background: #f9f9f9;
    margin: -180rpx 105rpx;
    border-radius: 30rpx;
}

.modal-title {
    padding-top: 50rpx;
    font-size: 36rpx;
    color: #030303;
    text-align: center;
}

.modal-content {
    padding: 20rpx 32rpx;
}

.modal-input {
    display: flex;
    background: #fff;
    border: 2rpx solid #ddd;
    border-radius: 4rpx;
    font-size: 28rpx;
}

.input {
    width: 100%;
    height: 82rpx;
    font-size: 28rpx;
    line-height: 28rpx;
    padding: 0 20rpx;
    box-sizing: border-box;
    color: #333;
}

input-holder {
    color: #666;
    font-size: 28rpx;
}

.modal-footer {
    display: flex;
    flex-direction: row;
    height: 86rpx;
    border-top: 1px solid #dedede;
    font-size: 34rpx;
    line-height: 86rpx;
}

.btn-cancel {
    width: 50%;
    color: #666;
    text-align: center;
    border-right: 1px solid #dedede;
}

.btn-confirm {
    width: 50%;
    color: #cc3333;
    text-align: center;
}
</style>
