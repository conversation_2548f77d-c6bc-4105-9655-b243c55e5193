<template>
    <view :class="'_root ' + (selectable ? '_select' : '')" :style="containerStyle">
        <slot v-if="!nodes[0]" />
        <node id="_root" :childs="nodes" :opts="[lazyLoad, loadingImg, errorImg, showImgMenu]" @add.stop.prevent="addFun($event, { tagId: '_root' })" />
    </view>
</template>

<script>
'use strict';
import node from './node/node';
function e(e, t, n) {
    if (t in e) {
        Object.defineProperty(e, t, {
            value: n,
            enumerable: true,
            configurable: true,
            writable: true
        });
    } else {
        e[t] = n;
    }
    return e;
}
/*!
 * mp-html v2.2.0
 * https://github.com/jin-yufeng/mp-html
 *
 * Released under the MIT license
 * Author: Jin <PERSON>
 */
var t = require('./parser');
var n = [];
export default {
    components: {
        node
    },
    data() {
        return {
            nodes: []
        };
    },
    props: {
        containerStyle: String,
        content: {
            type: String,
            default: ''
        },
        copyLink: {
            type: Boolean,
            default: true
        },
        domain: String,
        errorImg: String,
        lazyLoad: Boolean,
        loadingImg: String,
        pauseVideo: {
            type: Boolean,
            default: true
        },
        previewImg: {
            type: Boolean,
            default: true
        },
        scrollTable: Boolean,
        selectable: null,
        setTitle: {
            type: Boolean,
            default: true
        },
        showImgMenu: {
            type: Boolean,
            default: true
        },
        tagStyle: Object,
        useAnchor: null
    },
    created: function () {
        this.plugins = [];
        for (var e = n.length; e--; ) {
            this.plugins.push(new n[e](this));
        }
    },
    destroyed: function () {
        clearInterval(this._timer);
        this.hookFun('onDetached');
    },
    methods: {
        inFun: function (e, t, n) {
            if (e && t && n) {
                this._in = {
                    page: e,
                    selector: t,
                    scrollTop: n
                };
            }
        },
        navigateTo: function (t, n) {
            var that = this;
            return new Promise(function (r, i) {
                if (!that.useAnchor) {
                    return void i(Error('Anchor is disabled'));
                }
                var a = uni
                    .createSelectorQuery()
                    .in(that._in ? that._in.page : that)
                    .select((that._in ? that._in.selector : '._root') + (t ? ''.concat('>>>', '#').concat(t) : ''))
                    .boundingClientRect();
                that._in ? a.select(that._in.selector).scrollOffset().select(that._in.selector).boundingClientRect() : a.selectViewport().scrollOffset();
                a.exec(function (t) {
                    if (!t[0]) {
                        return void i(Error('Label not found'));
                    }
                    var a = t[1].scrollTop + t[0].top - (t[2] ? t[2].top : 0) + (n || parseInt(that.useAnchor) || 0);
                    that._in
                        ? that._in.page.setData(e({}, that._in.scrollTop, a))
                        : uni.pageScrollTo({
                              scrollTop: a,
                              duration: 300
                          });
                    r();
                });
            });
        },
        getText: function (e) {
            var t = '';
            (function e(n) {
                for (var o = 0; o < n.length; o++) {
                    var r = n[o];
                    if ('text' === r.type) t += r.text.replace(/&amp;/g, '&');
                    else if ('br' === r.name) {
                        t += '\n';
                    } else {
                        var i = 'p' === r.name || 'div' === r.name || 'tr' === r.name || 'li' === r.name || ('h' === r.name[0] && r.name[1] > '0' && r.name[1] < '7');
                        if (i && t && '\n' !== t[t.length - 1]) {
                            t += '\n';
                        }
                        if (r.children) {
                            e(r.children);
                        }
                        i && '\n' !== t[t.length - 1] ? (t += '\n') : ('td' !== r.name && 'th' !== r.name) || (t += '\t');
                    }
                }
            })(e || this.nodes);
            return t;
        },
        getRect: function () {
            var that = this;
            return new Promise(function (t, n) {
                uni.createSelectorQuery()
                    .in(that)
                    .select('._root')
                    .boundingClientRect()
                    .exec(function (e) {
                        return e[0] ? t(e[0]) : n(Error('Root label not found'));
                    });
            });
        },
        setContent: function (e, n) {
            var that = this;
            (this.imgList && n) || (this.imgList = []);
            this._videos = [];
            var r = {};
            var i = new t(this).parse(e);
            if (n) {
                for (var a = this.nodes.length, l = i.length; l--; ) {
                    r['nodes['.concat(a + l, ']')] = i[l];
                }
            } else {
                r.nodes = i;
            }
            this.setData(r, function () {
                that.hookFun('onLoad');
                that.$emit('load');
            });
            var s;
            clearInterval(this._timer);
            this._timer = setInterval(function () {
                that.getRect()
                    .then(function (e) {
                        if (e.height === s) {
                            that.$emit('ready', {
                                detail: e
                            });
                            clearInterval(that._timer);
                        }
                        s = e.height;
                    })
                    .catch(function () {});
            }, 350);
        },
        hookFun: function (e) {
            for (var t = n.length; t--; ) {
                if (this.plugins[t][e]) {
                    this.plugins[t][e]();
                }
            }
        },
        addFun: function (e, _dataset) {
            /* ---处理dataset begin--- */
            this.handleDataset(e, _dataset);
            /* ---处理dataset end--- */
            e.detail.root = this;
        }
    },
    watch: {
        content: {
            handler: function (e) {
                this.setContent(e);
            },

            immediate: true
        }
    }
};
</script>
<style>
._root {
    padding: 1px 0;
    overflow-x: auto;
    overflow-y: hidden;
    -webkit-overflow-scrolling: touch;
}
._select {
    -webkit-user-select: text;
    user-select: text;
}
</style>
