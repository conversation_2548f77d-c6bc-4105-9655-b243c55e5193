<template>
    <view>
        <cu-custom bgColor="none" :isSearch="false" :isBack="true">
            <view slot="backText">返回</view>
            <view slot="content" class="title-content">
                <text class="title-emoji">📝</text>
                <text class="title-text">发布商品</text>
            </view>
        </cu-custom>
        <view class="page-container">
            <view class="form-card">
                <view class="form-item">
                    <view class="form-label">
                        <text class="label-emoji">🏷️</text>
                        <text class="label-text">发布类型</text>
                    </view>
                    <view class="form-control">
                        <picker @change="bindReleaseType" :value="index" :range="release_type">
                            <view class="picker-wrapper">
                                <text class="picker-text">{{ release_index == -1 ? '请选择' : release_type[release_index]
                                    }}</text>
                                <text class="cicon-forward picker-arrow"></text>
                            </view>
                        </picker>
                    </view>
                </view>
                <view class="form-item">
                    <view class="form-label">
                        <text class="label-emoji">📦</text>
                        <text class="label-text">物品类别</text>
                    </view>
                    <view class="form-control">
                        <picker @change="bindPickerType" :value="index" range-key="realm_name" :range="type_list">
                            <view class="picker-wrapper">
                                <text class="picker-text">{{ type_index == -1 ? '请选择' : type_list[type_index].realm_name
                                    }}</text>
                                <text class="cicon-forward picker-arrow"></text>
                            </view>
                        </picker>
                    </view>
                </view>
                <view class="form-item">
                    <view class="form-label">
                        <text class="label-emoji">🏷️</text>
                        <text class="label-text">物品名称</text>
                    </view>
                    <view class="form-control">
                        <input @input="set_lost_name" :value="lost_name" class="modern-input" placeholder="请输入物品名称" />
                    </view>
                </view>
                <view class="form-item">
                    <view class="form-label">
                        <text class="label-emoji">📍</text>
                        <text class="label-text">交易地点</text>
                    </view>
                    <view class="form-control">
                        <input @input="set_lost_address" :value="lost_address" class="modern-input"
                            placeholder="请输入交易地点" />
                    </view>
                </view>
                <view class="form-item">
                    <view class="form-label">
                        <text class="label-emoji">📞</text>
                        <text class="label-text">联系方式</text>
                    </view>
                    <view class="form-control">
                        <input @input="set_lost_phone" :value="lost_phone" class="modern-input"
                            placeholder="手机号，微信，QQ" />
                    </view>
                </view>
                <view class="form-item">
                    <view class="form-label">
                        <text class="label-emoji">💰</text>
                        <text class="label-text">物品价格</text>
                    </view>
                    <view class="form-control">
                        <input @input="set_lost_moeny" :value="lost_money" class="modern-input" placeholder="请输入物品价格" />
                        <text class="price-tip">💡 特殊需求请填写：面议</text>
                    </view>
                </view>
                <view class="form-item form-item-vertical">
                    <view class="form-label">
                        <text class="label-emoji">📝</text>
                        <text class="label-text">物品描述</text>
                    </view>
                    <view class="form-control">
                        <textarea @input="set_lost_desc" :value="lost_desc" class="modern-textarea" maxlength="200"
                            placeholder="请详细描述物品的特征、使用情况等信息"></textarea>
                    </view>
                </view>
                <view class="form-item form-item-vertical">
                    <view class="form-label">
                        <text class="label-emoji">📷</text>
                        <text class="label-text">物品图片</text>
                    </view>
                    <view class="form-control">
                        <view class="image-grid">
                            <view class="image-item" @tap="ViewImage" :data-url="ImgArr[index]"
                                v-for="(item, index) in ImgArr" :key="index">
                                <image :src="ImgArr[index]" mode="aspectFill" class="uploaded-image"></image>
                                <view class="delete-btn" @tap.stop.prevent="DelImg" :data-index="index">
                                    <text class="cuIcon-close"></text>
                                </view>
                            </view>
                            <view v-if="ImgArr.length < 3" @tap="previewImage" class="upload-btn">
                                <view class="upload-icon">📷</view>
                                <view class="upload-text">上传图片</view>
                            </view>
                        </view>
                    </view>
                </view>
                <view v-if="config.top_twig == 1" class="form-item">
                    <view class="form-label">
                        <text class="label-emoji">📌</text>
                        <text class="label-text">置顶推广</text>
                        <text class="price-info">
                            ({{ config.top_price }}
                            <text v-if="config.price_type == 0 || config.price_type == 1">
                                {{ config.price_type == 0 ? config.design.currency : config.design.confer }}
                            </text>/天)
                        </text>
                    </view>
                    <view class="form-control">
                        <picker :value="index" :range="ToTop" @change="bindTopChange">
                            <view class="picker-wrapper">
                                <text class="picker-text">{{ ToTop[TopIndex] }}</text>
                                <text class="cicon-forward picker-arrow"></text>
                            </view>
                        </picker>
                    </view>
                </view>
                <view v-if="config.top_twig == 1 && TopIndex != 0" class="payment-info">
                    <view class="payment-card">
                        <text class="payment-label">💳 需支付：</text>
                        <text class="payment-amount" :class="config.price_type == 2 ? 'text-price' : ''">{{ price
                            }}</text>
                        <text class="payment-unit" v-if="config.price_type == 0 || config.price_type == 1">{{
                            config.price_type == 0 ? config.design.currency : config.design.confer }}</text>
                        <block v-if="config.price_type != 2">
                            <text class="payment-divider">|</text>
                            <text class="balance-label">💰 我拥有：</text>
                            <text class="balance-amount" v-if="config.price_type == 0">{{ config.conch }}{{
                                config.design.currency }}</text>
                            <text class="balance-amount" v-if="config.price_type == 1">{{ config.fraction }}{{
                                config.design.confer }}</text>
                        </block>
                    </view>
                </view>
                <view @tap="openUrl" class="rules-link">
                    <text class="rules-emoji">📋</text>
                    <text class="rules-text">《{{ config.custom_title }}使用规则》</text>
                </view>
                <view @tap="submit" class="submit-btn">
                    <text class="submit-emoji">🚀</text>
                    <text class="submit-text">立即发布</text>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
const app = getApp();
var http = require('../../../util/http.js');
var md5 = require('../../../util/md5.js');

export default {
    /**
     * 页面的初始数据
     */
    data() {
        return {
            config: {},
            release_type: ['出售', '求购', '租聘', '置换', '定制'],
            release_index: -1,
            type_list: [],
            type_index: -1,
            lost_name: '',
            lost_address: '',
            lost_desc: '',
            lost_phone: '',
            lost_money: '',
            ImgArr: [],
            TopIndex: 0,
            ToTop: ['不设置', '1天', '2天', '3天', '4天', '5天', '6天', '7天', '8天', '9天', '10天'],
            price: 0,
            is_submit: false,
            lost_date: '',
            key: ''
        }
    },
    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {
        var lost = app.globalData.__PlugUnitScreen('a11eb9c1955977a6d890dca4991209f6');
        if (!lost) {
            uni.showToast({
                title: '未开通插件',
                icon: 'none',
                duration: 2000
            });
            setTimeout(() => {
                this.BackPage();
            }, 1000);
            return;
        }
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        var lost = app.globalData.__PlugUnitScreen('a11eb9c1955977a6d890dca4991209f6');
        if (!lost) {
            uni.showToast({
                title: '未开通插件',
                icon: 'none',
                duration: 2000
            });
            setTimeout(() => {
                var pages = getCurrentPages();
                if (pages.length == 1) {
                    uni.reLaunch({
                        url: '/yl_welore/pages/index/index'
                    });
                    return;
                }
                uni.navigateBack();
            }, 1000);
            return;
        }
        this.getLostType();
        this.getLostConfig();
    },
    methods: {
        openUrl() {
            uni.navigateTo({
                url: '/yl_welore/pages/packageE/used_goods_desc/index'
            });
        },
        bindReleaseType(d) {
            console.log(d);
            this.release_index = d.detail.value;
        },
        onPickerChange(d) {
            this.lost_date = d.detail.dateString;
        },
        bindTopChange(d) {
            console.log(d);
            this.TopIndex = d.detail.value;
            this.price = (d.detail.value * this.config.top_price).toFixed(2);
        },
        getLostConfig() {
            var b = app.globalData.api_root + 'Used/getLostConfig';
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    that.config = res.data;
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => { }
                    });
                }
            });
        },
        submit() {
            if (this.release_index == -1) {
                uni.showToast({
                    title: '请选择发布类型',
                    icon: 'none',
                    duration: 2000
                });
                return;
            }
            if (this.type_index == -1) {
                uni.showToast({
                    title: '请选择物品类别',
                    icon: 'none',
                    duration: 2000
                });
                return;
            }
            if (this.lost_money == '') {
                uni.showToast({
                    title: '请填写物品价格',
                    icon: 'none',
                    duration: 2000
                });
                return;
            }
            if (this.lost_name == '') {
                uni.showToast({
                    title: '请填写物品名称',
                    icon: 'none',
                    duration: 2000
                });
                return;
            }
            if (this.lost_address == '') {
                uni.showToast({
                    title: '请填写交易地点',
                    icon: 'none',
                    duration: 2000
                });
                return;
            }
            if (this.lost_phone == '') {
                uni.showToast({
                    title: '请填写联系方式',
                    icon: 'none',
                    duration: 2000
                });
                return;
            }
            if (this.lost_desc == '') {
                uni.showToast({
                    title: '请填写物品描述',
                    icon: 'none',
                    duration: 2000
                });
                return;
            }
            if (this.ImgArr.length == 0) {
                uni.showToast({
                    title: '请上传物品图片',
                    icon: 'none',
                    duration: 2000
                });
                return;
            }
            uni.showLoading({
                title: '发布中...',
                mask: true
            });
            var b = app.globalData.api_root + 'Used/InsLost';
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.release_index = this.release_index;
            params.type = this.type_list[this.type_index].id;
            params.lost_name = this.lost_name;
            params.lost_address = this.lost_address;
            params.lost_desc = this.lost_desc;
            params.lost_phone = this.lost_phone;
            params.lost_money = this.lost_money;
            params.top_day = this.TopIndex;
            params.ImgArr = JSON.stringify(this.ImgArr);
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.code == 1) {
                        //that.Back();
                        uni.showModal({
                            title: '提示',
                            content: res.data.msg,
                            showCancel: false,
                            success: (res) => { }
                        });
                        uni.hideLoading();
                    }
                    if (res.data.code == 0) {
                        uni.showModal({
                            title: '提示',
                            content: res.data.msg,
                            showCancel: false,
                            success: (res) => {
                                that.Back();
                            }
                        });
                        uni.hideLoading();
                    }
                    if (res.data.code == 2) {
                        that.pay_submit(res.data.item);
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => { }
                    });
                }
            });
        },
        /**
         * 充值
         */
        pay_submit(item) {
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.lost_id = item.id;
            params.top_day = item.top_day;
            params.uid = e.id;
            var b = app.globalData.api_root + 'Pay/do_used_pay';
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.return_msg == 'OK') {
                        var timeStamp = (Date.parse(new Date()) / 1000).toString();
                        var pkg = 'prepay_id=' + res.data.prepay_id;
                        var nonceStr = res.data.nonce_str;
                        var paySign = md5
                            .hexMD5(
                                'appId=' +
                                res.data.appid +
                                '&nonceStr=' +
                                nonceStr +
                                '&package=' +
                                pkg +
                                '&signType=MD5&timeStamp=' +
                                timeStamp +
                                '&key=' +
                                res.data.app_info['app_key']
                            )
                            .toUpperCase(); //此处用到hexMD5插件
                        //发起支付
                        uni.requestPayment({
                            timeStamp: timeStamp,
                            nonceStr: nonceStr,
                            package: pkg,
                            signType: 'MD5',
                            paySign: paySign,
                            success: (res) => {
                                uni.showModal({
                                    title: '提示',
                                    content: '支付成功，请等待审核！',
                                    showCancel: false,
                                    success: (res) => {
                                        that.Back();
                                    }
                                });
                            },
                            complete: () => {
                                uni.hideLoading();
                                that.Back();
                            }
                        });
                    } else {
                        uni.showModal({
                            title: '提示',
                            content: '支付参数错误！',
                            showCancel: false,
                            success: (res) => { }
                        });
                        uni.hideLoading();
                        that.Back();
                    }
                },
                fail: () => {
                    that.is_submit = false;
                    uni.hideLoading();
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => { }
                    });
                }
            });
        },
        DelImg(d) {
            var that = this;
            uni.showModal({
                title: '提示',
                content: '确定要删除这张图片吗？',
                cancelText: '取消',
                confirmText: '删除',
                success: (res) => {
                    if (res.confirm) {
                        that.ImgArr.splice(d.currentTarget.dataset.index, 1);
                        that.ImgArr = [...that.ImgArr];
                    }
                }
            });
        },
        ViewImage(e) {
            uni.previewImage({
                urls: this.ImgArr,
                current: e.currentTarget.dataset.url
            });
        },
        /**
         * 上传主图
         */
        previewImage() {
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var b = app.globalData.api_root + 'User/img_upload';
            uni.chooseMedia({
                count: 3,
                mediaType: ['image'],
                // 可以指定是原图还是压缩图，默认二者都有
                sourceType: ['album', 'camera'],
                // 可以指定来源是相册还是相机，默认二者都有
                sizeType: ['original', 'compressed'],
                success: (res) => {
                    uni.showLoading({
                        title: '上传中...',
                        mask: true
                    });
                    console.log(res);
                    var tempFilePaths = res.tempFiles;
                    for (var i = 0, h = tempFilePaths.length; i < h; i++) {
                        uni.uploadFile({
                            url: b,
                            filePath: tempFilePaths[i].tempFilePath,
                            name: 'sngpic',
                            header: {
                                'content-type': 'multipart/form-data'
                            },
                            formData: {
                                'content-type': 'multipart/form-data',
                                token: e.token,
                                openid: e.openid,
                                much_id: app.globalData.siteInfo.uniacid
                            },
                            success: (res) => {
                                console.log(res);
                                var data = JSON.parse(res.data);
                                console.log(data);
                                if (data.status == 'error') {
                                    uni.showToast({
                                        title: data.msg,
                                        icon: 'none',
                                        duration: 2000
                                    });
                                } else {
                                    that.ImgArr = that.ImgArr.concat(data.url);
                                    uni.hideLoading();
                                }
                            },
                            fail: (res) => {
                                uni.showToast({
                                    title: '上传错误！',
                                    icon: 'none',
                                    duration: 2000
                                });
                            }
                        });
                    }
                }
            });
        },
        set_lost_moeny(d) {
            this.lost_money = d.detail.value;
        },
        set_lost_phone(d) {
            this.lost_phone = d.detail.value;
        },
        set_lost_desc(d) {
            this.lost_desc = d.detail.value;
        },
        set_lost_address(d) {
            this.lost_address = d.detail.value;
        },
        set_lost_name(d) {
            this.lost_name = d.detail.value;
        },
        bindDateChange(d) {
            console.log(d);
            this.lost_date = d.detail.value;
        },
        bindPickerType(d) {
            console.log(d);
            this.type_index = d.detail.value;
        },
        getLostType() {
            var b = app.globalData.api_root + 'Used/getLostType';
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            http.POST(b, {
                params: params,
                success: (res) => {
                    that.type_list = res.data;
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => { }
                    });
                }
            });
        },
        radioType(d) {
            this.key = d.detail.value;
        },
        /**
         * 返回上一页
         */
        Back(e) {
            //获取页面栈
            let pages = getCurrentPages();
            //获取所需页面
            let prevPage = pages[pages.length - 2]; //上一页
            // uniapp中使用$vm访问页面实例并直接赋值
            if (prevPage && prevPage.$vm) {
                prevPage.$vm.Refresh = true; //需要传过去的数据
            }

            uni.navigateBack({
                delta: 1
            });
        }
    }
};
</script>
<style>
/* 页面整体样式 */
page {
    background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 50%, #f0f8ff 100%);
    min-height: 100vh;
}

/* 标题区域样式 */
.title-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12rpx;
}

.title-emoji {
    font-size: 32rpx;
    line-height: 1;
}

.title-text {
    color: #000000;
    font-weight: 600;
    font-size: 36rpx;
}

/* 页面容器 */
.page-container {
    padding: 20rpx;
}

/* 表单卡片 */
.form-card {
    background: #ffffff;
    border-radius: 24rpx;
    padding: 40rpx;
    box-shadow: 0 12rpx 40rpx rgba(28, 187, 180, 0.12);
    position: relative;
    overflow: hidden;
}

.form-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6rpx;
    background: linear-gradient(135deg, #1cbbb4 0%, #36cfc9 100%);
}

/* 表单项样式 */
.form-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 32rpx 0;
    border-bottom: 2rpx solid rgba(28, 187, 180, 0.08);
    position: relative;
}

.form-item:last-child {
    border-bottom: none;
}

.form-item-vertical {
    flex-direction: column;
    align-items: flex-start;
    gap: 24rpx;
}

/* 表单标签 */
.form-label {
    display: flex;
    align-items: center;
    gap: 12rpx;
    min-width: 200rpx;
}

.label-emoji {
    font-size: 28rpx;
    line-height: 1;
}

.label-text {
    font-size: 28rpx;
    font-weight: 600;
    color: #333333;
}

/* 表单控件 */
.form-control {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 16rpx;
}

/* 现代化输入框 */
.modern-input {
    background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 100%);
    border: 2rpx solid transparent;
    border-radius: 16rpx;
    padding: 24rpx 32rpx;
    font-size: 28rpx;
    color: #333333;
    transition: all 0.3s ease;
    width: 100%;
    box-sizing: border-box;
    height: 86rpx;
}

.modern-input:focus {
    border-color: #1cbbb4;
    background: #ffffff;
    box-shadow: 0 0 0 6rpx rgba(28, 187, 180, 0.1);
}

/* 现代化文本域 */
.modern-textarea {
    background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 100%);
    border: 2rpx solid transparent;
    border-radius: 16rpx;
    padding: 24rpx 32rpx;
    font-size: 28rpx;
    color: #333333;
    min-height: 200rpx;
    width: 100%;
    box-sizing: border-box;
    transition: all 0.3s ease;
}

.modern-textarea:focus {
    border-color: #1cbbb4;
    background: #ffffff;
    box-shadow: 0 0 0 6rpx rgba(28, 187, 180, 0.1);
}

/* 选择器样式 */
.picker-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 100%);
    border-radius: 16rpx;
    padding: 24rpx 32rpx;
    transition: all 0.3s ease;
}

.picker-wrapper:active {
    background: #ffffff;
    box-shadow: 0 0 0 6rpx rgba(28, 187, 180, 0.1);
}

.picker-text {
    font-size: 28rpx;
    color: #333333;
    font-weight: 500;
}

.picker-arrow {
    color: #1cbbb4;
    font-size: 24rpx;
}

/* 价格提示 */
.price-tip {
    font-size: 24rpx;
    color: #999999;
    margin-top: 8rpx;
    display: flex;
    align-items: center;
    gap: 8rpx;
}

/* 价格信息 */
.price-info {
    font-size: 24rpx;
    color: #666666;
    margin-left: 8rpx;
}

/* 图片网格 */
.image-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 20rpx;
}

.image-item {
    position: relative;
    width: 160rpx;
    height: 160rpx;
    border-radius: 16rpx;
    overflow: hidden;
    box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
}

.uploaded-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.delete-btn {
    position: absolute;
    top: -8rpx;
    right: -8rpx;
    width: 48rpx;
    height: 48rpx;
    background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    font-size: 20rpx;
    box-shadow: 0 4rpx 12rpx rgba(255, 77, 79, 0.3);
    z-index: 10;
}

.upload-btn {
    width: 160rpx;
    height: 160rpx;
    background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 100%);
    border: 2rpx dashed #1cbbb4;
    border-radius: 16rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 12rpx;
    transition: all 0.3s ease;
}

.upload-btn:active {
    background: #ffffff;
    box-shadow: 0 0 0 6rpx rgba(28, 187, 180, 0.1);
}

.upload-icon {
    font-size: 48rpx;
    line-height: 1;
}

.upload-text {
    font-size: 24rpx;
    color: #1cbbb4;
    font-weight: 600;
}

/* 支付信息卡片 */
.payment-info {
    margin-top: 24rpx;
}

.payment-card {
    background: linear-gradient(135deg, #fff7e6 0%, #fff2e8 100%);
    border: 2rpx solid rgba(255, 193, 7, 0.2);
    border-radius: 16rpx;
    padding: 24rpx 32rpx;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 16rpx;
    font-size: 26rpx;
}

.payment-label {
    color: #fa8c16;
    font-weight: 600;
}

.payment-amount {
    color: #fa8c16;
    font-weight: 700;
    font-size: 32rpx;
}

.payment-unit {
    color: #fa8c16;
    font-weight: 600;
}

.payment-divider {
    color: #d9d9d9;
    margin: 0 8rpx;
}

.balance-label {
    color: #52c41a;
    font-weight: 600;
}

.balance-amount {
    color: #52c41a;
    font-weight: 700;
    font-size: 28rpx;
}

/* 规则链接 */
.rules-link {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12rpx;
    margin-top: 40rpx;
    padding: 20rpx;
    transition: all 0.3s ease;
}

.rules-link:active {
    transform: scale(0.98);
}

.rules-emoji {
    font-size: 24rpx;
    line-height: 1;
}

.rules-text {
    color: #1cbbb4;
    font-size: 26rpx;
    font-weight: 600;
    text-decoration: underline;
}

/* 提交按钮 */
.submit-btn {
    background: linear-gradient(135deg, #1cbbb4 0%, #36cfc9 100%);
    border-radius: 24rpx;
    padding: 32rpx;
    margin: 40rpx 0 20rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 16rpx;
    box-shadow: 0 12rpx 40rpx rgba(28, 187, 180, 0.3);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.submit-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.submit-btn:active {
    transform: scale(0.98);
    box-shadow: 0 8rpx 24rpx rgba(28, 187, 180, 0.4);
}

.submit-btn:active::before {
    left: 100%;
}

.submit-emoji {
    font-size: 32rpx;
    line-height: 1;
}

.submit-text {
    color: #ffffff;
    font-size: 32rpx;
    font-weight: 700;
}

/* 兼容性样式 */
.ui-form-group {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    padding: 0.5em 30rpx;
}

.ui-form-group .ui-form-title {
    text-align: justify;
    font-size: 1.1em;
    position: relative;
    padding-left: 0;
    display: flex;
    align-items: center;
    margin-right: 30rpx;
}

.ui-form-group .ui-form-content {
    flex: 1;
    border-radius: 10rpx;
    display: flex;
    align-items: center;
    min-height: 3em;
}

.border-bottom {
    border-bottom: 1px solid rgba(119, 119, 119, 0.075);
}

.left_text {
    margin: 10px 0px 20px 0px;
}

/* 响应式设计 */
.form-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 20rpx;
}

.form-label {
    min-width: auto;
}

.form-control {
    width: 100%;
}
</style>
