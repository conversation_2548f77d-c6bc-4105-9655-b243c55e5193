<template>
    <view>
        <cu-custom bgColor="bg-white" :isSearch="false" :isBack="true">
            <view slot="backText">返回</view>
            <view slot="content" style="color: #2c2b2b; font-weight: 600; font-size: 36rpx">等级说明</view>
        </cu-custom>

        <view style="margin: 10px; background-color: #fff; border-radius: 5px; padding-bottom: 20px">
            <view style="padding: 10px">
                <view class="title">1、什么是个人等级？</view>
                <view class="content">
                    个人等级是用户的成长记录，也是用户身份的象征。通过累积经验值，用户将不断提升自己的等级，等级高的用户将在社区内拥有更高的威望，也会解锁相应特权，平台未来还会提供丰富的福利回馈用户、鼓励用户积极为社区创造价值。
                </view>
                <view class="title">2、什么是经验值？</view>
                <view class="content">经验值是用于计算用户个人等级的经验成长数值，累计到达指定经验值即可提升等级。经验值可以通过完成 "任务中心" 发布的任务来获取。</view>
                <view class="title">3、什么是等级特权？</view>
                <view class="content">等级特权是社区活跃用户贡献的回馈和激励，涉及身份特权、功能特权、装扮特权、福利特权及其他特权。让你拥有与众不同的荣誉感~</view>
                <view class="title">4、如何快速提升我的等级？</view>
                <view class="content">我们为所有用户提供丰富的经验值任务，通过完成任务，提升自己的等级吧！</view>
                <view class="title">5、需要多少经验才能升级？</view>
            </view>
            <view style="margin: 0px 10px 10px 10px">
                <view class="falseee">
                    <view class="col-1">等级</view>
                    <view class="col-2">称号</view>
                    <view class="col-3">所需经验</view>
                    <view class="col-4">赠送荣誉点</view>
                </view>
                <block v-for="(item, dataListindex) in list" :key="dataListindex">
                    <view class="falseee">
                        <view class="col-1">
                            <image class="now_level" :src="item.level_icon" mode="widthFix" style="width: 50rpx; vertical-align: middle"></image>
                        </view>
                        <view class="col-2">
                            {{ item.level_name }}
                        </view>
                        <view class="col-3">
                            {{ item.need_experience }}
                        </view>
                        <view class="col-4">
                            {{ item.honor_point }}
                        </view>
                    </view>
                </block>
            </view>
        </view>
    </view>
</template>

<script>
const app = getApp();
import http from '../../../util/http.js';
export default {
    data() {
        return {
            list: []
        };
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        this.get_user_level();
    },
    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {},
    methods: {
        get_user_level() {
            const b = app.globalData.api_root + 'Task/user_level';
            const e = app.globalData.getCache('userinfo');
            const params = {
                token: e.token,
                openid: e.openid
            };
            http.POST(b, {
                params: params,
                success: (res) => {
                    this.list = res.data;
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => {}
                    });
                }
            });
        }
    }
};
</script>
<style>
page {
    background-color: #e6edf1;
}

.title {
    font-weight: 600;
    margin: 5px 0px;
}

.content {
    font-size: 14px;
}

/* 标题要居中 */

.nav-title {
    position: absolute;
    text-align: center;
    max-width: 377rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    top: 0;
    left: 0;
    right: 0;
    margin: auto;
    font-size: 36rpx;
    color: #2c2b2b;
    font-weight: 600;
}

.nav-capsule {
    display: flex;
    align-items: center;
    margin-left: 20rpx;
    width: 50rpx;
    justify-content: space-around;
    border-radius: 50%;
    margin-top: 54rpx;
    z-index: 999999999;
}

.navbar-v-line {
    width: 1px;
    height: 32rpx;
    background-color: #f3f3f3;
}

.back-pre {
    width: 40rpx;
    height: 40rpx;
    margin-top: 11rpx;
    margin-left: 0rpx;
}

.falseee {
    overflow: hidden;
    display: flex; /*弹性盒子*/
    flex-flow: row wrap; /*子元素溢出父容器时换行*/
}

.falseee:after {
    content: '';
    height: 30px;
}

.col-1 {
    width: calc(100% / 4 - 2px);
    float: left;
    text-align: center;
    font-size: 12px;
    height: 35px;
    line-height: 35px;
    border-left: 1px solid;
    border-top: 1px solid;
    border-bottom: 1px solid;
}

.col-2 {
    width: calc(100% / 4 - 2px);
    float: left;
    text-align: center;
    font-size: 12px;
    height: 35px;
    line-height: 35px;
    border-left: 1px solid;
    border-top: 1px solid;
    border-bottom: 1px solid;
}

.col-3 {
    width: calc(100% / 4 - 2px);
    float: left;
    text-align: center;
    font-size: 12px;
    height: 35px;
    line-height: 35px;
    border-left: 1px solid;
    border-top: 1px solid;
    border-bottom: 1px solid;
}

.col-4 {
    width: calc(100% / 4 - 2px);
    float: left;
    text-align: center;
    font-size: 12px;
    height: 35px;
    line-height: 35px;
    border: 1px solid;
}
</style>
