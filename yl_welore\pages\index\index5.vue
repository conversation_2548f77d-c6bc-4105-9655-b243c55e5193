<template>
    <view style="background-color: #ffffff; min-height: 1200rpx; padding: 10px; padding-bottom: 80px">
        <block v-for="(item, dataListindex) in new_list" :key="dataListindex">
            <view style="background-color: #fff; overflow: hidden; position: relative">
                <image
                    v-if="item.top_time"
                    src="/static/yl_welore/style/icon/index3/top1.png"
                    style="opacity: 0.8; width: 70px; height: 70px; position: absolute; right: 0; top: 2%; z-index: 200"
                ></image>
                <view style="">
                    <!-- 头像 -->
                    <view class="cu-list menu-avatar">
                        <view class="cu-item">
                            <view
                                @tap="home_url"
                                :data-index="dataListindex"
                                data-k="1"
                                :data-user_id="item.user_id"
                                class="cu-avatar round index5"
                                :style="'background-color: #ffffff;background-image:url(' + item.user_head_sculpture + ');'"
                            >
                                <!-- <view style="z-index:10;"
                class="cu-tag badge {{item.gender==2?'cuIcon-female bg-pink':'cuIcon-male bg-blue'}}"></view> -->
                                <!-- <image class="now_level" style="height: 48px;width: 48px;position: absolute;max-width:initial"
                src="{{item.avatar_frame}}"></image> -->
                                <!-- <block wx:if="{{item.attr!=''}}">
                  <image class="now_level"
                    style="height: 25rpx;width: 25rpx;position: absolute;right:-1px;bottom:-3px;z-index:100;max-width:initial"
                    src="{{item.attr.attest.at_icon}}"></image>
                </block> -->
                            </view>
                            <view class="content flex-sub" style="left: 130rpx">
                                <view class="align-center">
                                    <view style="font-size: 15px">{{ item.user_nick_name }}</view>
                                    <image v-if="item.attr != ''" class="now_level" style="height: 30rpx; width: 30rpx; margin-left: 5px" :src="item.attr.attest.at_icon"></image>
                                    <image mode="heightFix" class="now_level" v-if="item.wear_merit" :src="item.wear_merit" style="height: 12px; margin-left: 3px"></image>
                                </view>
                                <view class="text-gray text-sm flex">
                                    <text
                                        v-if="item.check_qq == 'da'"
                                        style="background-color: #9966ff; color: #fff; padding: 0px 4px; border-radius: 2px; font-size: 10px; margin-right: 5px"
                                    >
                                        {{ $state.diy.qq_name }}主
                                    </text>
                                    <text
                                        v-if="item.check_qq == 'xiao'"
                                        style="background-color: #4facfe; color: #fff; padding: 0px 4px; border-radius: 2px; font-size: 10px; margin-right: 5px"
                                    >
                                        管理
                                    </text>
                                    <text class="text_num_1" style="font-size: 14px; color: #aaabb4; width: 100px; font-weight: 400">
                                        {{ item.autograph == null ? '' : item.autograph }}
                                    </text>
                                </view>
                            </view>
                        </view>
                        <view style="position: absolute; right: 7px; top: 15px">
                            <view>
                                <image
                                    v-if="item.red == 1 && version == 0"
                                    src="/static/yl_welore/style/icon/index3/fl.png"
                                    style="width: 40px; height: 20px; margin: 0px 5px"
                                ></image>
                                <image v-if="item.study_type == 3" src="/static/yl_welore/style/icon/index3/hd.png" style="width: 40px; height: 20px; margin: 0px 5px"></image>
                                <image
                                    v-if="item.is_buy == 1 && version == 0"
                                    src="/static/yl_welore/style/icon/index3/ff.png"
                                    style="width: 40px; height: 20px; margin: 0px 5px"
                                ></image>
                            </view>
                        </view>
                    </view>
                    <!-- 头像 -->
                    <!-- 内容 -->
                    <view>
                        <view class="weui-cell" style="padding: 0rpx 10px 10px 20px">
                            <view
                                @tap="home_url"
                                :data-index="dataListindex"
                                data-k="3"
                                :data-type="item.study_type"
                                :data-id="item.id"
                                :style="'word-break:break-all;position: relative;color:' + item.study_title_color + ';'"
                            >
                                <block>
                                    <text style="font-size: 16px; font-weight: 600; letter-spacing: 1px" v-if="item.study_title!=''">
                                        {{ item.study_title }}
                                    </text>
                                    <view style="font-size: 14px; color: #6a6e7e; margin-top: 10px">
                                        <rich-text class="text_num_3" :nodes="item.study_content"></rich-text>
                                    </view>
                                </block>
                            </view>
                            <view
                                v-if="item.gambit_id"
                                @tap="gambit_list"
                                :data-id="item.gambit_id"
                                style="
                                    display: inline-block;
                                    background-color: #f7f7f8;
                                    border-radius: 20px;
                                    color: #87b9e5;
                                    padding: 3px 10px 3px 2px;
                                    font-size: 13px;
                                    margin-top: 10px;
                                "
                            >
                                <image style="width: 20px; height: 20px; vertical-align: middle" :src="http_root + 'addons/yl_welore/web/static/mineIcon/index5/topic.png'"></image>
                                <text style="vertical-align: middle; margin-left: 3px">{{ item.gambit_name }}</text>
                            </view>
                        </view>
                        <view
                            @tap="home_url"
                            :data-index="dataListindex"
                            data-k="3"
                            :data-type="item.study_type"
                            :data-id="item.id"
                            v-if="item.study_type == 0 || item.study_type == 3 || item.study_type == 4 || item.study_type == 5 || item.study_type == 6"
                            style="overflow: hidden"
                        >
                            <!-- 1 -->
                            <view style="padding: 0px 15px" v-if="item.image_part.length == 1 && img != ''" v-for="(img, img_index) in item.image_part" :key="img_index">
                                <image :lazy-load="true" :src="img" style="width: 100%; height: 600rpx" mode="aspectFill"></image>
                            </view>
                            <!-- 1 -->
                            <!-- 2 -->
                            <view
                                :style="'width:31%;float:left;text-align:center;padding-left:' + (img_index == 0 ? 18 : 8) + 'px;'"
                                v-if="item.image_part.length == 2"
                                v-for="(img, img_index) in item.image_part"
                                :key="img_index"
                            >
                                <image :lazy-load="true" v-if="img_index == 0" :src="img" style="height: 100px; width: 100%" mode="aspectFill"></image>

                                <image :lazy-load="true" v-if="img_index == 1" :src="img" style="height: 100px; width: 100%" mode="aspectFill"></image>
                            </view>
                            <!-- 2 -->
                            <!-- 3 -->
                            <view class="grid col-3 text-center" style="padding: 0px 10px">
                                <block v-if="item.image_part.length == 3" v-for="(img, img_index) in item.image_part" :key="img_index">
                                    <view style="text-align: center; padding-left: 8px" v-if="img_index == 0">
                                        <image :lazy-load="true" :src="img" style="width: 100%; height: 100px" mode="aspectFill"></image>
                                    </view>

                                    <view style="text-align: center; padding-left: 5px" v-if="img_index == 1">
                                        <image :lazy-load="true" :src="img" style="width: 100%; height: 100px" mode="aspectFill"></image>
                                    </view>

                                    <view style="text-align: center; padding-left: 5px; padding-right: 8px; position: relative" v-if="img_index == 2">
                                        <image :lazy-load="true" :src="img" style="width: 100%; height: 100px" mode="aspectFill"></image>
                                    </view>
                                </block>
                            </view>
                            <!-- 4 -->
                            <view class="grid col-2 text-center" style="padding: 0px 10px">
                                <block v-if="item.image_part.length > 3" v-for="(img, img_index) in item.image_part" :key="img_index">
                                    <view style="text-align: center; padding-left: 5px" v-if="img_index == 0">
                                        <image :lazy-load="true" :src="img" style="width: 100%; height: 300rpx; border-radius: 2px" mode="aspectFill"></image>
                                    </view>

                                    <view style="text-align: center; padding-left: 5px" v-if="img_index == 1">
                                        <image :lazy-load="true" :src="img" style="width: 100%; height: 300rpx; border-radius: 2px" mode="aspectFill"></image>
                                    </view>

                                    <view style="text-align: center; padding-left: 5px; position: relative; margin-top: 1px" v-if="img_index == 2">
                                        <image :lazy-load="true" :src="img" style="width: 100%; height: 300rpx; border-radius: 2px" mode="aspectFill"></image>
                                    </view>

                                    <view style="text-align: center; padding-left: 5px; position: relative; margin-top: 1px" v-if="img_index == 3">
                                        <image :lazy-load="true" :src="img" style="width: 100%; height: 300rpx; border-radius: 2px" mode="aspectFill"></image>
                                    </view>
                                </block>
                            </view>
                            <!-- 投票 -->
                            <view v-if="item.study_type == 4 || item.study_type == 5" class="shadow-warp" style="margin: 15px; background-color: #f8f8f8">
                                <view style="padding: 15px; text-align: center">
                                    <view
                                        @tap.stop.prevent="home_url"
                                        :data-index="dataListindex"
                                        data-k="3"
                                        :data-type="item.study_type"
                                        :data-id="item.id"
                                        style="font-size: 15px; font-weight: 600"
                                    >
                                        <text v-if="item.study_type == 4">（单选）</text>
                                        <text v-if="item.study_type == 5">（多选）</text>
                                        <rich-text class="text_num" v-if="item.study_title != ''" :nodes="item.study_title"></rich-text>
                                    </view>
                                    <view style="height: 10px"></view>
                                    <view style="position: relative" v-if="vo_index < 3" v-for="(vo_item, vo_index) in item.vo" :key="vo_index">
                                        <view
                                            style="width: 95%; height: 40px; border-radius: 5px; line-height: 40px; margin: 5px auto"
                                            class="text_num bg-white"
                                            @tap.stop.prevent="dian_option"
                                            :data-id="vo_item.id"
                                            :data-key="dataListindex"
                                            :data-index="vo_index"
                                        >
                                            <view class="text-cut" style="z-index: 3; position: relative; width: 70%; margin: 0 auto">
                                                {{ vo_item.ballot_name }}
                                            </view>
                                            <text
                                                v-if="voi_item == vo_item.id"
                                                :style="'position: absolute;right: ' + (item.is_vo_check > 0 ? 90 : 7) + '%;z-index:3;top: 0;'"
                                                class="cuIcon-check lg text-green"
                                                v-for="(voi_item, index) in item.vo_id"
                                                :key="index"
                                            ></text>
                                            <text v-if="item.is_vo_check > 0" style="z-index: 3; position: absolute; right: 40rpx; top: 0">
                                                {{ vo_item.voters }}
                                            </text>
                                        </view>

                                        <view
                                            v-if="item.is_vo_check > 0"
                                            class="cu-progress radius sm"
                                            style="position: absolute; z-index: 1; left: 0; right: 0; top: 0; width: 95%; height: 40px; margin: 0 auto; background-color: #ffffff"
                                        >
                                            <view :style="'width:' + vo_item.ratio + '%;background-image: linear-gradient(120deg, #a1c4fd 0%, #c2e9fb 100%);'"></view>
                                        </view>
                                    </view>
                                    <view
                                        @tap.stop.prevent="home_url"
                                        :data-index="dataListindex"
                                        data-k="3"
                                        :data-type="item.study_type"
                                        :data-id="item.id"
                                        v-if="item.vo.length > 3"
                                        style="width: 95%; height: 40px; border-radius: 5px; line-height: 40px; margin: 5px auto"
                                        class="text_num bg-white"
                                    >
                                        查看全部选项
                                        <text class="cuIcon-right lg text-gray"></text>
                                    </view>
                                </view>
                                <view class="flex align-end" style="padding-bottom: 10px">
                                    <view class="flex-sub">
                                        <view style="font-weight: 300; margin-left: 46rpx">参与人数：{{ item.vo_count }}</view>
                                    </view>
                                    <view class="flex-sub">
                                        <button
                                            @tap.stop.prevent="vote_do"
                                            :data-index="vo_index"
                                            :data-key="dataListindex"
                                            v-if="item.vo_id.length > 0 && item.is_vo_check == 0"
                                            style="font-weight: 300; float: right; margin-right: 46rpx"
                                            class="cu-btn bg-grey round sm"
                                        >
                                            投票
                                        </button>
                                    </view>
                                </view>
                            </view>
                            <!-- 投票 -->
                            <!-- 3 -->
                        </view>
                        <view
                            v-if="item.study_type == 1"
                            style="
                                margin: 0 auto;
                                overflow: hidden;
                                display: flex;
                                justify-content: space-between;
                                align-items: center;
                                height: 170rpx;
                                width: 90%;
                                background-color: #f6f7f7;
                                border: 1px solid #f0f0f0;
                                border-radius: 10rpx;
                            "
                        >
                            <view
                                :style="
                                    'background-image: url(' +
                                    item.user_head_sculpture +
                                    ');background-size: cover;background-position: center;width: 170rpx;background-color: #000;height: 170rpx;'
                                "
                            >
                                <view class="audioOpen" @tap="play" v-if="!item.is_voice" :data-key="dataListindex" :data-vo="item.study_voice">
                                    <text style="color: #ffffff; font-size: 15px" class="cicon-play-arrow"></text>
                                </view>
                                <view class="audioOpen" @tap="stop" v-if="item.is_voice" :data-key="dataListindex" :data-vo="item.study_voice">
                                    <text style="color: #ffffff; font-size: 15px" class="cicon-pause"></text>
                                </view>
                            </view>
                            <view style="width: 75%; padding: 20rpx">
                                <view style="display: flex; justify-content: space-between; align-items: center">
                                    <view style="font-size: 28rpx; color: #555555; font-weight: 600">{{ item.user_nick_name }}上传的音乐</view>
                                    <view class="times">{{ item.starttime }}</view>
                                </view>
                                <view style="display: flex; justify-content: space-between; align-items: center; margin-top: 20rpx">
                                    <view style="font-size: 24rpx; color: #999">{{ item.user_nick_name }}</view>
                                    <view>
                                        <slider
                                            style="width: 170rpx"
                                            @change="sliderChange"
                                            block-size="12px"
                                            step="1"
                                            :value="item.offset"
                                            :max="item.max"
                                            selected-color="#4c9dee"
                                        />
                                    </view>
                                </view>
                            </view>
                        </view>
                        <view
                            @tap="home_url"
                            :data-index="dataListindex"
                            data-k="3"
                            :data-type="item.study_type"
                            :data-id="item.id"
                            v-if="item.study_type == 1"
                            style="overflow: hidden"
                        >
                            <!-- 1 -->
                            <view style="padding: 0px 20px" v-if="item.image_part.length == 1 && img != ''" v-for="(img, img_index) in item.image_part" :key="img_index">
                                <image :lazy-load="true" :src="img" style="width: 100%; height: 190px" mode="aspectFill"></image>
                            </view>
                            <!-- 1 -->
                            <!-- 2 -->
                            <view
                                :style="'width:31%;float:left;text-align:center;padding-left:' + (img_index == 0 ? 18 : 8) + 'px;'"
                                v-if="item.image_part.length == 2"
                                v-for="(img, img_index) in item.image_part"
                                :key="img_index"
                            >
                                <image :lazy-load="true" v-if="img_index == 0" :src="img" style="height: 100px; width: 100%" mode="aspectFill"></image>

                                <image :lazy-load="true" v-if="img_index == 1" :src="img" style="height: 100px; width: 100%" mode="aspectFill"></image>
                            </view>
                            <!-- 2 -->
                            <!-- 3 -->
                            <view class="grid col-3 text-center" style="padding: 0px 10px">
                                <block v-if="item.image_part.length > 2" v-for="(img, img_index) in item.image_part" :key="img_index">
                                    <view style="text-align: center; padding-left: 8px" v-if="img_index == 0">
                                        <image :lazy-load="true" :src="img" style="width: 100%; height: 100px" mode="aspectFill"></image>
                                    </view>

                                    <view style="text-align: center; padding-left: 5px" v-if="img_index == 1">
                                        <image :lazy-load="true" :src="img" style="width: 100%; height: 100px" mode="aspectFill"></image>
                                    </view>

                                    <view style="text-align: center; padding-left: 5px; padding-right: 8px" v-if="img_index == 2">
                                        <image :lazy-load="true" :src="img" style="width: 100%; height: 100px" mode="aspectFill"></image>
                                    </view>
                                </block>
                            </view>
                            <!-- 3 -->
                        </view>
                        <view v-if="item.study_type == 2">
                            <view @tap="home_url" :data-index="dataListindex" data-k="3" :data-type="item.study_type" :data-id="item.id">
                                <view v-if="item.image_part.length > 0" class="grid flex-sub padding-lr col-1" style="position: relative">
                                    <image :src="item.image_part[0]" mode="aspectFill" style="height: 190px; margin: 0 auto"></image>
                                    <text
                                        class="cuIcon-videofill lg text-white"
                                        style="z-index: 1; font-size: 40px; position: absolute; text-align: center; left: 44%; bottom: 37%"
                                    ></text>
                                </view>
                                <view
                                    v-if="item.image_part.length == null || item.image_part.length == 0"
                                    class="bg-black padding radius text-center shadow-blur"
                                    style="position: relative; margin: 0 auto; width: 80%; height: 180px; z-index: 100; overflow: hidden; font-size: 16px"
                                >
                                    <text
                                        class="cuIcon-videofill lg text-white"
                                        style="z-index: 1; font-size: 40px; position: absolute; text-align: center; left: 44%; bottom: 37%"
                                    ></text>
                                </view>
                            </view>
                        </view>
                    </view>
                    <!-- 内容 -->
                    <!-- 位置 -->
                    <!-- 位置 -->
                    <view style="clear: both; height: 0"></view>
                    <view class="" style="padding-bottom: 10px; padding-top: 10px">
                        <view
                            @tap="home_url"
                            :data-index="dataListindex"
                            data-k="2"
                            :data-id="item.tory_id"
                            style="float: left; margin-left: 20px; font-size: 14px; padding-top: 6px; font-weight: 500; color: #3399ff"
                            class="weui-flex__item"
                        >
                            <text style="font-size: 13px; color: #afb0b9">{{ item.adapter_time }}</text>
                        </view>
                        <view style="float: right; margin-bottom: 15px; margin-right: 15px" class="weui-flex__item">
                            <button hover-class="none" @tap="parseEventDynamicCode($event, item.is_buy == 1 ? '' : 'add_zan')" :data-id="item.id" :data-key="dataListindex">
                                <image
                                    :animation="item.animationData_zan"
                                    v-if="item.is_info_zan == false"
                                    :src="http_root + 'addons/yl_welore/web/static/mineIcon/index5/zan.png'"
                                    style="width: 25px; height: 25px; vertical-align: middle"
                                ></image>
                                <image
                                    :animation="item.animationData_zan"
                                    v-if="item.is_info_zan == true"
                                    :src="http_root + 'addons/yl_welore/web/static/mineIcon/index5/zan_ok.png'"
                                    style="width: 25px; height: 25px; vertical-align: middle"
                                ></image>
                                <text class="index_nav_name" style="color: #9b9da9; font-size: 14px; vertical-align: middle">
                                    {{ item.info_zan_count_this > 10000 ? item.info_zan_count : item.info_zan_count_this }}
                                </text>
                            </button>
                        </view>
                        <view style="float: right; margin-right: 15px" class="weui-flex__item">
                            <button @tap="home_url" :data-index="dataListindex" data-k="3" :data-type="item.study_type" :data-id="item.id" hover-class="none">
                                <image
                                    :src="http_root + 'addons/yl_welore/web/static/mineIcon/index5/comment.png'"
                                    style="width: 25px; vertical-align: middle; height: 25px"
                                ></image>
                                <text class="index_nav_name" style="color: #9b9da9; font-size: 14px; vertical-align: middle">
                                    {{ item.study_repount }}
                                </text>
                            </button>
                        </view>
                        <view v-if="ad_info.paper_browse_num_hide == 1" style="float: right; margin-right: 15px" class="weui-flex__item">
                            <button hover-class="none">
                                <image :src="http_root + 'addons/yl_welore/web/static/mineIcon/index5/kan.png'" style="width: 25px; vertical-align: middle; height: 25px"></image>
                                <text class="index_nav_name" style="color: #9b9da9; font-size: 14px; vertical-align: middle">
                                    {{ item.study_heat }}
                                </text>
                            </button>
                        </view>
                    </view>
                </view>
            </view>

            <view style="width: 93%; height: 1px; background-color: #f2f2f2; margin: 0 auto"></view>

            <view v-if="dataListindex % ad_info.isolate == 0 && dataListindex != 0 && ad_info.adsper == 1">
                <ad :unit-id="ad_info.adunit_id"></ad>
            </view>
        </block>
        <view :class="'cu-load ' + (!di_msg ? 'loading' : 'over')"></view>
    </view>
</template>

<script>
export default {
    props: ['data', 'compName'],
    computed: {
        new_list() {
            return this.$parent.$data.new_list;
        },
        dataListindex() {
            return this.$parent.$data.dataListindex;
        },
        item() {
            return this.$parent.$data.item;
        },
        http_root() {
            return this.$parent.$data.http_root;
        },
        $state() {
            return this.$parent.$data.$state;
        },
        order_time() {
            return this.$parent.$data.order_time;
        },
        version() {
            return this.$parent.$data.version;
        },
        img() {
            return this.$parent.$data.img;
        },
        img_index() {
            return this.$parent.$data.img_index;
        },
        vo_index() {
            return this.$parent.$data.vo_index;
        },
        vo_item() {
            return this.$parent.$data.vo_item;
        },
        voi_item() {
            return this.$parent.$data.voi_item;
        },
        index() {
            return this.$parent.$data.index;
        },
        ad_info() {
            return this.$parent.$data.ad_info;
        },
        di_msg() {
            return this.$parent.$data.di_msg;
        }
    },
    methods: {
        home_url(e) {
            this.$emit('home-url', e);
        },
        gambit_list(e) {
            this.$emit('gambit-list', e);
        },
        dian_option(e) {
            this.$emit('dian-option', e);
        },
        vote_do(e) {
            this.$emit('vote-do', e);
        },
        play(e) {
            this.$emit('play', e);
        },
        stop(e) {
            this.$emit('stop', e);
        },
        sliderChange(e) {
            this.$emit('slider-change', e);
        },
        home_pl(e) {
            this.$emit('home-pl', e);
        },
        parseEventDynamicCode(e, type) {
            this.$emit('dynamic-code', e, type);
        }
    }
};
</script>
<style></style>
