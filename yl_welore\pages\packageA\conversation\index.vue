<template>
    <view>
        <cu-custom bgColor="bg-white" :isBack="true" :isSearch="false">
            <view slot="backText">返回</view>
            <view slot="content" style="color: #2c2b2b; font-weight: 600; font-size: 36rpx">话题</view>
        </cu-custom>
        <view class="cu-bar search bg-white">
            <view class="search-form round">
                <text class="cuIcon-search"></text>
                <input @input="get_input" :value="value_name" type="text" placeholder="搜话题" confirm-type="search" />
            </view>
            <view class="action">
                <button @tap="ser_gambit" class="cu-btn bg-yellow shadow-blur round text-white">搜索</button>
            </view>
        </view>
        <view class="VerticalBox">
            <scroll-view class="VerticalNav nav solid-right" scroll-y scroll-with-animation :scroll-top="VerticalNavTop" style="height: calc(100vh - 375rpx)">
                <view class="cu-item text-blue cur" @tap="tabSelect">全部</view>
            </scroll-view>
            <scroll-view class="VerticalMain" scroll-y scroll-with-animation style="height: calc(100vh - 340rpx)" @scrolltolower="bottom_get_list">
                <view class="padding-top-sm padding-lr-sm">
                    <view class="cu-bar solid-bottom bg-white">
                        <view class="action">
                            <text class="cuIcon-titles text-blue"></text>
                            <text class="text-xl text-bold">话题列表</text>
                            <view v-if="$state.copyright.allow_user_topic == 1" @tap="add_gambit" class="cu-tag radius bg-yellow light" style="margin-left: 160rpx">加个话题</view>
                        </view>
                    </view>
                    <view class="cu-list menu-avatar">
                        <view
                            class="cu-item animation-slide-bottom"
                            @tap="set_gambit"
                            :data-id="item.id"
                            :data-name="item.gambit_name"
                            :style="'animation-delay: ' + (index + 1) * 0.1 + 's;'"
                            v-for="(item, index) in list"
                            :key="index"
                        >
                            <view class="cu-avatar round lg bg-white">
                                <view class="padding-xs">
                                    <view class="cu-avatar radius top_bag">
                                        <text style="font-size: 25px">{{ item.top_name }}</text>
                                    </view>
                                </view>
                            </view>

                            <view class="content" style="left: 134rpx; width: 75%">
                                <view class="text-grey">
                                    <text class="text-cut" style="letter-spacing: 1px">#{{ item.gambit_name }}#</text>
                                </view>
                            </view>
                        </view>
                    </view>
                    <view :class="'cu-load ' + (!di_msg ? 'loading' : 'over')"></view>
                </view>
            </scroll-view>
        </view>
    </view>
</template>

<script>
const app = getApp();
import http from '../../../util/http.js';
export default {
    /**
     * 页面的初始数据
     */
    data() {
        return {
            http_root: app.globalData.http_root,
            StatusBar: app.globalData.StatusBar,
            CustomBar: app.globalData.CustomBar,
            Custom: app.globalData.Custom,
            TabCur: 0,
            MainCur: 0,
            VerticalNavTop: 0,
            list: [],
            page: 1,
            load: true,
            gambit: [],
            gambit_name: '',
            gambit_new: false,
            value_name: '',
            di_msg: false
        };
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        this.get_gambit();
    },
    methods: {
        bottom_get_list() {
            this.page = this.page + 1;
            this.get_gambit();
        },
        ser_gambit() {
            this.page = 1;
            this.list = [];
            this.get_gambit();
        },
        /**
         * 获取话题
         */
        get_gambit() {
            var b = app.globalData.api_root + 'User/get_gambit';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.uid = e.uid;
            params.page = this.page;
            params.search_name = this.value_name;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data && res.data.length > 0) {
                        this.list.push(...res.data);
                    }
                    if (!res.data || res.data.length === 0 || this.list.length < 9) {
                        this.di_msg = true;
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        },
        /**
         * 搜索
         */
        get_input(d) {
            var ser = d.detail.value;
            this.value_name = ser;
        },
        /**
         * 选择当前话题
         */
        set_gambit(d) {
            console.log(d);
            var id = d.currentTarget.dataset.id;
            var name = d.currentTarget.dataset.name;
            app.globalData.setCache('gambit_card', {
                gambit_id: id,
                gambit_name: name,
                button_key: 0
            });
            uni.navigateBack();
        },
        /**
         * 添加话题
         */
        add_gambit() {
            uni.showModal({
                title: '加个话题',
                editable: true,
                placeholderText: '不用带#号哦~',
                success: (res) => {
                    console.log(res);
                    if (res.confirm && res.content) {
                        var b = app.globalData.api_root + 'User/add_gambit';
                        var e = app.globalData.getCache('userinfo');
                        var params = new Object();
                        params.token = e.token;
                        params.openid = e.openid;
                        params.uid = e.uid;
                        params.gambit_name = res.content.replace(/[^A-Za-z0-9\u4e00-\u9fa5]/g, '');
                        http.POST(b, {
                            params: params,
                            success: (res) => {
                                console.log(res);
                                if (res.data.status == 'success') {
                                    this.page = 1;
                                    this.list = [];
                                    this.get_gambit();
                                } else {
                                    uni.showToast({
                                        title: res.data.msg,
                                        icon: 'none',
                                        duration: 2000
                                    });
                                }
                            },
                            fail: () => {
                                uni.showModal({
                                    title: '提示',
                                    content: '网络繁忙，请稍候重试！',
                                    showCancel: false,
                                    success: function (res) {}
                                });
                            }
                        });
                    }
                }
            });
        }
    }
};
</script>
<style>
page {
    background-color: #ffffff;
}
.VerticalNav.nav {
    width: 200rpx;
    white-space: initial;
}

.VerticalNav.nav .cu-item {
    width: 100%;
    text-align: center;
    background-color: #fff;
    margin: 0;
    border: none;
    height: 50px;
    position: relative;
}

.VerticalNav.nav .cu-item.cur {
    background-color: #ffffff;
}
.VerticalNav.nav .cu-item.cur::after {
    content: '';
    width: 8rpx;
    height: 30rpx;
    border-radius: 10rpx 0 0 10rpx;
    position: absolute;
    background-color: currentColor;
    top: 0;
    right: 0rpx;
    bottom: 0;
    margin: auto;
}
.VerticalBox {
    display: flex;
}
.VerticalMain {
    background-color: #ffffff;
}
.top_bag {
    background-image: linear-gradient(135deg, #a789f0 10%, #f35cf4 100%);
}
</style>
