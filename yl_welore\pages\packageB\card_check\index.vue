<template>
    <view>
        <cu-custom bgColor="bg-white" :isSearch="false" :isBack="true">
            <view slot="backText">返回</view>
            <view slot="content" style="color: #2c2b2b; font-weight: 600; font-size: 36rpx">活动验证</view>
        </cu-custom>

        <view style="margin: 15px; padding: 10px; background: #ffffff; border-radius: 10px" v-for="(item, index) in list" :key="index">
            <view style="border-radius: 5px">
                <view class="flex solid-bottom justify-between" style="padding: 10px">
                    <view>
                        <image :src="item.user.user_head_sculpture" style="width: 35px; height: 35px; vertical-align: middle; border-radius: 50%"></image>
                        <text style="vertical-align: middle; margin-left: 10px">{{ item.user.user_nick_name }}</text>
                    </view>
                    <view>
                        <image src="" style="width: 35px; height: 35px; vertical-align: middle"></image>
                        <view v-if="item.overdue == 2" class="bg-red cu-tag round">已过期</view>
                    </view>
                </view>

                <view
                    @tap="get_position"
                    :data-pos_name="item.brisk_address"
                    :data-latitude="item.brisk_address_latitude"
                    :data-longitude="item.brisk_address_longitude"
                    style="margin: 20px"
                    slot="content"
                >
                    <text>活动地址：{{ item.brisk_address }}</text>
                </view>
                <view style="margin: 20px" slot="content">
                    <text>活动时间：{{ item.start_time }} - {{ item.end_time }}</text>
                </view>
                <view style="margin: 20px" slot="content">
                    <text>活动人数：{{ item.number_of_people == 0 ? '不限人数' : item.number_of_people }}</text>
                </view>
                <view slot="content" style="text-align: center; margin: 20px">
                    <text style="font-size: 30px; font-weight: 700; letter-spacing: 10px">{{ item.rand_captcha }}</text>
                </view>
                <view slot="content" style="text-align: center; margin: 20px">
                    <!-- <navigator style="display: inline;" url="/yl_welore/pages/packageA/article/index?id={{item.paper.id}}&type={{item.paper.study_type}}" hover-class="none">
        <text style="padding: 7px 15px;border-radius: 2px;box-shadow: inset 0 0 0 1px rgba(0,0,0,.1);background: #2d8cf0;color:#fff;">查看详情</text>
      </navigator>
      <navigator wx:if="{{item.overdue==1}}" style="display: inline;" url="/yl_welore/pages/packageB/check_ok/index?id={{item.paper.id}}" hover-class="none">
        <text style="margin-left: 30px;padding: 7px 15px;border-radius: 2px;box-shadow: inset 0 0 0 1px rgba(0,0,0,.1);background: #19be6b;color:#fff;">活动验证</text>
      </navigator> -->
                    <button @tap="open_url" data-key="0" :data-id="item.paper.id" :data-type="item.paper.study_type" class="cu-btn round bg-blue shadow">查看详情</button>
                    <button @tap="open_url" data-key="1" :data-id="item.paper.id" v-if="item.overdue == 1" class="cu-btn round bg-green shadow margin-left">线下验证</button>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import http from '../../../util/http.js';
const app = getApp();
export default {
    data() {
        return {
            check: '',
            list: []
        };
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        this.get_my_brisk_team();
    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {},

    methods: {
        open_url(d) {
            const key = d.currentTarget.dataset.key;

            if (key == '0') {
                uni.navigateTo({
                    url: `/yl_welore/pages/packageA/article/index?id=${d.currentTarget.dataset.id}&type=${d.currentTarget.dataset.type}`
                });
            } else {
                uni.navigateTo({
                    url: `/yl_welore/pages/packageB/check_ok/index?id=${d.currentTarget.dataset.id}`
                });
            }
        },

        valueSix(d) {
            const t = d.detail;
            this.check = t;
        },

        /**
         * 打开地图
         */
        get_position(d) {
            const a = Number(d.currentTarget.dataset.latitude);
            const o = Number(d.currentTarget.dataset.longitude);
            const name = d.currentTarget.dataset.pos_name;

            if (a && o) {
                uni.openLocation({
                    latitude: a,
                    longitude: o,
                    name: name
                });
            }
        },

        /**
         * 获取我发布的活动
         */
        get_my_brisk_team() {
            const b = app.globalData.api_root + 'User/get_my_brisk_team';
            const that = this;
            const e = app.globalData.getCache('userinfo');
            const params = {};
            params.token = e.token;
            params.openid = e.openid;
            http.POST(b, {
                params: params,
                success: function (res) {
                    that.list = res.data;
                },
                fail: function () {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        }
    }
};
</script>
<style>
page {
    background-color: #f1f1f1;
}
</style>
