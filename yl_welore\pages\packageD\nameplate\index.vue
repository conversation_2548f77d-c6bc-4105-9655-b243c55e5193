<template>
    <view class="page-container">
        <cu-custom bgColor="none" :isSearch="false" :isBack="true" style="color: #ffffff;">
            <view slot="backText">返回</view>
            <view slot="content" class="header-title">身份铭牌</view>
        </cu-custom>

        <!-- 优化后的当前身份卡展示 -->
        <view class="current-identity-section">
            <view class="identity-card">
                <view class="card-background">
                    <view class="card-pattern"></view>
                    <view class="card-shine"></view>
                </view>

                <view class="card-content">
                    <view class="avatar-container">
                        <view class="avatar-wrapper">
                            <view class="cu-avatar round eight avatar-main" :style="'background-image:url(' + top_info.forgery_head + ');'"></view>
                            <view class="avatar-ring"></view>
                            <view class="avatar-glow"></view>
                        </view>
                    </view>

                    <view class="identity-info">
                        <view class="identity-name">{{ top_info.forgery_name }}</view>
                        <view class="identity-status">当前使用身份</view>
                    </view>
                </view>
            </view>
        </view>

        <!-- 优化后的积分展示 -->
        <view class="points-section">
            <view class="points-card">
                <view class="points-icon">💎</view>
                <view class="points-info">
                    <view class="points-label">{{ $state.diy.confer }}</view>
                    <view class="points-value">{{ user_info.fraction }}</view>
                </view>
                <view class="points-decoration">✨</view>
            </view>
        </view>

        <!-- 优化后的身份卡列表 -->
        <view class="identity-list-section">
            <view class="section-container">
                <!-- 标题区域 -->
                <view class="section-header">
                    <view class="section-title">
                        <text class="title-icon">🎭</text>
                        <text class="title-text">身份卡</text>
                    </view>
                </view>

                <!-- 身份卡网格 -->
                <view class="identity-grid">
                    <view
                        class="identity-item"
                        v-for="(item, a_index) in info"
                        :key="a_index"
                        :style="{ 'animation-delay': (a_index * 0.1) + 's' }"
                    >
                        <view class="item-card">
                            <!-- 状态标识 -->
                            <view class="status-badge" v-if="item.is_ok == 1">
                                <text class="status-text">已解锁</text>
                            </view>

                            <!-- 头像区域 -->
                            <view class="item-avatar-container">
                                <view class="item-avatar-wrapper">
                                    <view class="cu-avatar round eight item-avatar" :style="'background-image:url(' + item.forgery_head + ');'"></view>
                                    <view class="item-avatar-ring" :class="item.is_ok == 1 ? 'unlocked' : 'locked'"></view>
                                </view>
                            </view>

                            <!-- 身份信息 -->
                            <view class="item-info">
                                <view class="item-name">{{ item.forgery_name }}</view>
                                <view class="item-duration">可使用{{ item.cost_day }}天</view>
                            </view>

                            <!-- 操作按钮 -->
                            <view class="item-actions">
                                <button
                                    v-if="item.is_ok == 0"
                                    @tap="get_name_unlock"
                                    :data-key="a_index"
                                    :class="'action-btn unlock-btn ' + (item.unlock_fraction > 0 ? 'paid' : 'free')"
                                >
                                    <text class="btn-icon">🔓</text>
                                    <text class="btn-text">{{ item.unlock_fraction > 0 ? item.unlock_fraction : '免费' }}</text>
                                </button>
                                <button
                                    v-if="item.is_ok == 1"
                                    @tap="use_card"
                                    :data-key="a_index"
                                    class="action-btn use-btn"
                                >
                                    <text class="btn-icon">✨</text>
                                    <text class="btn-text">使用</text>
                                </button>
                            </view>
                        </view>
                    </view>
                </view>

                <!-- 说明区域 -->
                <view class="description-section">
                    <view class="description-card">
                        <view class="description-icon">💡</view>
                        <view class="description-text">
                            发帖和回复时可选择身份铭牌代替原有昵称，用于隐藏身份！
                        </view>
                    </view>
                </view>
            </view>
        </view>

        <!-- 优化后的解锁弹窗 -->
        <view :class="'unlock-modal ' + (mod_avatar ? 'show' : '')">
            <view class="modal-overlay" @tap="hideModal"></view>
            <view class="modal-dialog">
                <view class="modal-header">
                    <view class="modal-title">
                        <text class="title-icon">🔓</text>
                        <text class="title-text">解锁身份</text>
                    </view>
                    <view class="close-btn" @tap="hideModal">
                        <text class="close-icon">✕</text>
                    </view>
                </view>

                <view class="modal-content">
                    <view class="identity-preview">
                        <view class="preview-name">「{{ this_info.forgery_name }}」</view>
                    </view>

                    <view class="cost-info">
                        <view class="cost-label">解锁需要</view>
                        <view class="cost-amount">
                            <text class="cost-number">{{ this_info.unlock_fraction }}</text>
                            <text class="cost-unit">{{ $state.diy.confer }}</text>
                        </view>
                    </view>
                </view>

                <view class="modal-actions">
                    <button class="modal-btn cancel-btn" @tap="hideModal">
                        <text class="btn-text">取消</text>
                    </button>
                    <button class="modal-btn confirm-btn" @tap="do_unlock_name">
                        <text class="btn-icon">💎</text>
                        <text class="btn-text">确定解锁</text>
                    </button>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
const app = getApp();
var http = require('../../../util/http.js');
export default {
    data() {
        return {
            http_root: app.globalData.http_root,
            top_info:{},
            user_info: {
                fraction: ''
            },
            info: [],
            mod_avatar: false,

            this_info: {
                forgery_name: '',
                unlock_fraction: ''
            },
            this_key: 0,
            type: 0,
            a_index: 0
        };
    }
    /**
     * 生命周期函数--监听页面加载
     */,
    onLoad(options) {
        if (typeof options.type != 'undefined') {
            this.type = 1;
        }
        this.get_nameplate();
        this.get_user_medal();
    },
    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {
        var name_card = app.globalData.getCache('name_card');
        var e = app.globalData.getCache('userinfo');
        if (name_card) {
            console.log(name_card);
            this.top_info = name_card;
            return;
        }
        this.get_one_name_card(name_card);
    },
    methods: {
        get_one_name_card(name_card) {
            var b = app.globalData.api_root + 'Nameplate/get_one_name_card';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.name_id = name_card;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.status == 'success') {
                        this.top_info = res.data.info;
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        },

        do_unlock_name() {
            var b = app.globalData.api_root + 'Nameplate/ins_user_nameplate';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.name_id = this.this_info.id;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.status == 'success') {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                        this.get_user_medal();
                        this.get_nameplate();
                        this.hideModal();
                    } else {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        },

        use_card(dd) {
            var key = dd.currentTarget.dataset.key;
            var this_info = this.info[key];
            this.top_info = this_info;
            app.globalData.setCache('name_card', this_info);
            if (this.type == 1) {
                uni.navigateBack();
            }
        },

        get_name_unlock(dd) {
            var key = dd.currentTarget.dataset.key;
            this.mod_avatar = true;
            this.this_info = this.info[key];
            this.this_key = key;
        },

        hideModal() {
            this.mod_avatar = false;
        },

        get_nameplate() {
            var b = app.globalData.api_root + 'Nameplate/get_nameplate';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    this.info = res.data;
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        },

        //勋章
        get_user_medal() {
            var b = app.globalData.api_root + 'Nameplate/get_user_info';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    this.user_info = res.data.user_info;
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        }
    }
};
</script>
<style>
page {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    min-height: 100vh;
}

.page-container {
    background: transparent;
}

/* 头部标题样式 */
.header-title {
    color: #ffffff;
    font-weight: 600;
    font-size: 36rpx;
}

/* 当前身份卡展示 */
.current-identity-section {
    padding: 30rpx 20rpx;
}

.identity-card {
    position: relative;
    background: #ffffff;
    border-radius: 32rpx;
    overflow: hidden;
    box-shadow: 0 20rpx 60rpx rgba(102, 126, 234, 0.2);
}

.card-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    opacity: 0.05;
}

.card-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: radial-gradient(circle at 20% 50%, rgba(255, 255, 255, 0.1) 2rpx, transparent 2rpx),
                      radial-gradient(circle at 80% 50%, rgba(255, 255, 255, 0.1) 2rpx, transparent 2rpx);
    background-size: 60rpx 60rpx;
}

.card-shine {
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    animation: shine 3s infinite;
}

@keyframes shine {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.card-content {
    position: relative;
    padding: 50rpx 40rpx;
    text-align: center;
    z-index: 2;
}

.avatar-container {
    margin-bottom: 30rpx;
}

.avatar-wrapper {
    position: relative;
    display: inline-block;
}

.avatar-main {
    width: 120rpx !important;
    height: 120rpx !important;
    position: relative;
    z-index: 3;
}

.avatar-ring {
    position: absolute;
    top: -8rpx;
    left: -8rpx;
    width: 136rpx;
    height: 136rpx;
    border: 4rpx solid transparent;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea, #764ba2, #f093fb) border-box;
    mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
    z-index: 2;
}

.avatar-glow {
    position: absolute;
    top: -16rpx;
    left: -16rpx;
    width: 152rpx;
    height: 152rpx;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea, #764ba2, #f093fb);
    opacity: 0.3;
    animation: glow 2s ease-in-out infinite alternate;
    z-index: 1;
}

@keyframes glow {
    from { transform: scale(1); opacity: 0.3; }
    to { transform: scale(1.1); opacity: 0.1; }
}

.identity-info {
    text-align: center;
}

.identity-name {
    font-size: 32rpx;
    font-weight: 700;
    color: #333333;
    margin-bottom: 12rpx;
}

.identity-status {
    font-size: 24rpx;
    color: #667eea;
    font-weight: 600;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* 积分展示 */
.points-section {
    padding: 0 20rpx 30rpx;
}

.points-card {
    background: #ffffff;
    border-radius: 24rpx;
    padding: 30rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.08);
    position: relative;
    overflow: hidden;
}

.points-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6rpx;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
}

.points-icon {
    font-size: 40rpx;
    margin-right: 20rpx;
}

.points-info {
    flex: 1;
}

.points-label {
    font-size: 24rpx;
    color: #999999;
    margin-bottom: 8rpx;
}

.points-value {
    font-size: 36rpx;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.points-decoration {
    font-size: 32rpx;
    animation: sparkle 2s ease-in-out infinite;
}

@keyframes sparkle {
    0%, 100% { transform: scale(1) rotate(0deg); }
    50% { transform: scale(1.2) rotate(180deg); }
}

/* 身份卡列表 */
.identity-list-section {
    padding: 0 20rpx;
    margin-bottom: 40rpx;
}

.section-container {
    background: #ffffff;
    border-radius: 24rpx;
    overflow: hidden;
    box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.08);
}

.section-header {
    padding: 30rpx;
    background: linear-gradient(135deg, #f8f9ff 0%, #fff0f8 100%);
    border-bottom: 2rpx solid rgba(102, 126, 234, 0.1);
}

.section-title {
    display: flex;
    align-items: center;
    justify-content: center;
}

.title-icon {
    font-size: 32rpx;
    margin-right: 16rpx;
}

.title-text {
    font-size: 32rpx;
    font-weight: 700;
    color: #333333;
}

.identity-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20rpx;
    padding: 30rpx 20rpx;
}

.identity-item {
    opacity: 0;
    animation: slideInUp 0.6s ease forwards;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30rpx);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.item-card {
    background: #ffffff;
    border-radius: 20rpx;
    padding: 20rpx;
    text-align: center;
    position: relative;
    border: 2rpx solid #f0f2f5;
    transition: all 0.3s ease;
}

.item-card:active {
    transform: translateY(2rpx) scale(0.98);
    box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
}

.status-badge {
    position: absolute;
    top: -8rpx;
    right: -8rpx;
    background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
    border-radius: 20rpx;
    padding: 4rpx 12rpx;
    z-index: 2;
}

.status-text {
    font-size: 20rpx;
    color: #ffffff;
    font-weight: 600;
}

.item-avatar-container {
    margin-bottom: 16rpx;
}

.item-avatar-wrapper {
    position: relative;
    display: inline-block;
}

.item-avatar {
    width: 80rpx !important;
    height: 80rpx !important;
}

.item-avatar-ring {
    position: absolute;
    top: -4rpx;
    left: -4rpx;
    width: 88rpx;
    height: 88rpx;
    border: 2rpx solid transparent;
    border-radius: 50%;
    background: linear-gradient(135deg, #d9d9d9, #f0f0f0) border-box;
    mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
}

.item-avatar-ring.unlocked {
    background: linear-gradient(135deg, #52c41a, #73d13d) border-box;
    mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
}

.item-info {
    margin-bottom: 16rpx;
}

.item-name {
    font-size: 24rpx;
    font-weight: 600;
    color: #333333;
    margin-bottom: 8rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.item-duration {
    font-size: 20rpx;
    color: #999999;
}

.item-actions {
    margin-top: 16rpx;
}

.action-btn {
    width: 100%;
    padding: 0rpx 16rpx;
    border-radius: 16rpx;
    border: none;
    font-size: 22rpx;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.unlock-btn.free {
    background: linear-gradient(135deg, #36cfc9 0%, #52c41a 100%);
    color: #ffffff;
}

.unlock-btn.paid {
    background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
    color: #ffffff;
}

.use-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #ffffff;
}

.btn-icon {
    font-size: 20rpx;
    margin-right: 8rpx;
}

.btn-text {
    font-size: 22rpx;
}

/* 说明区域 */
.description-section {
    padding: 30rpx;
    background: linear-gradient(135deg, #f8f9ff 0%, #fff0f8 100%);
}

.description-card {
    display: flex;
    align-items: flex-start;
    padding: 20rpx;
    background: #ffffff;
    border-radius: 16rpx;
    border-left: 6rpx solid #667eea;
}

.description-icon {
    font-size: 28rpx;
    margin-right: 16rpx;
    margin-top: 4rpx;
}

.description-text {
    flex: 1;
    font-size: 24rpx;
    color: #666666;
    line-height: 1.6;
    letter-spacing: 1rpx;
}

/* 解锁弹窗 */
.unlock-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.unlock-modal.show {
    opacity: 1;
    visibility: visible;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(10rpx);
}

.modal-dialog {
    background: #ffffff;
    border-radius: 32rpx;
    margin: 40rpx;
    max-width: 600rpx;
    width: 100%;
    overflow: hidden;
    transform: scale(0.8);
    transition: all 0.3s ease;
    position: relative;
    z-index: 2;
}

.unlock-modal.show .modal-dialog {
    transform: scale(1);
}

.modal-header {
    padding: 40rpx 40rpx 20rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: linear-gradient(135deg, #f8f9ff 0%, #fff0f8 100%);
}

.modal-title {
    display: flex;
    align-items: center;
}

.title-icon {
    font-size: 32rpx;
    margin-right: 16rpx;
}

.title-text {
    font-size: 32rpx;
    font-weight: 700;
    color: #333333;
}

.close-btn {
    width: 60rpx;
    height: 60rpx;
    border-radius: 50%;
    background: rgba(255, 77, 79, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.close-btn:active {
    transform: scale(0.9);
    background: rgba(255, 77, 79, 0.2);
}

.close-icon {
    font-size: 24rpx;
    color: #ff4d4f;
    font-weight: 700;
}

.modal-content {
    padding: 40rpx;
}

.identity-preview {
    text-align: center;
    margin-bottom: 40rpx;
}

.preview-name {
    font-size: 36rpx;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.cost-info {
    text-align: center;
    padding: 30rpx;
    background: linear-gradient(135deg, #f8f9ff 0%, #fff0f8 100%);
    border-radius: 20rpx;
    border: 2rpx solid rgba(102, 126, 234, 0.1);
}

.cost-label {
    font-size: 24rpx;
    color: #999999;
    margin-bottom: 16rpx;
}

.cost-amount {
    display: flex;
    align-items: baseline;
    justify-content: center;
}

.cost-number {
    font-size: 48rpx;
    font-weight: 700;
    background: linear-gradient(135deg, #ff4d4f, #ff7875);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-right: 8rpx;
}

.cost-unit {
    font-size: 28rpx;
    color: #666666;
    font-weight: 600;
}

.modal-actions {
    padding: 20rpx 40rpx 40rpx;
    display: flex;
    gap: 20rpx;
}

.modal-btn {
    flex: 1;
    padding:15rpx 24rpx;
    border-radius: 20rpx;
    border: none;
    font-size: 28rpx;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.cancel-btn {
    background: #f5f5f5;
    color: #666666;
}

.cancel-btn:active {
    transform: scale(0.98);
    background: #e8e8e8;
}

.confirm-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #ffffff;
    box-shadow: 0 8rpx 25rpx rgba(102, 126, 234, 0.3);
}

.confirm-btn:active {
    transform: scale(0.98);
    box-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.4);
}

.btn-icon {
    font-size: 24rpx;
    margin-right: 8rpx;
}

.btn-text {
    font-size: 28rpx;
}

/* 占位符样式保留 */
.placeholder {
    margin: 10px 0px;
}
</style>
