<template>
    <view>
        <cu-custom bgColor="bg-white" :isSearch="false" :isBack="true">
            <view slot="backText">返回</view>
            <view slot="content" style="color: #2c2b2b; font-weight: 600; font-size: 36rpx">消息订阅</view>
        </cu-custom>

        <view class="cu-list menu-avatar">
            <view class="cu-item">
                <view class="content" style="left: 40rpx">
                    <view class="text-grey">凯尔</view>
                    <view class="text-gray text-sm flex">
                        <text class="text-cut">我已天理为凭，踏入这片荒芜，不再受凡人的枷锁遏制。我已天理为凭，踏入这片荒芜，不再受凡人的枷锁遏制。</text>
                    </view>
                </view>
                <view class="action">
                    <view class="cu-form-group" style="padding: 0px">
                        <switch></switch>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
const app = getApp();
var http = require('../../../util/http.js');
export default {
    data() {
        return {};
    }
    /**
     * 生命周期函数--监听页面加载
     */,
    onLoad: function (options) {},
    /**
     * 生命周期函数--监听页面初次渲染完成
     */
    onReady: function () {},
    /**
     * 生命周期函数--监听页面显示
     */
    onShow: function () {},
    /**
     * 生命周期函数--监听页面隐藏
     */
    onHide: function () {},
    /**
     * 生命周期函数--监听页面卸载
     */
    onUnload: function () {},
    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh: function () {},
    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom: function () {},
    /**
     * 用户点击右上角分享
     */
    onShareAppMessage: function () {},
    methods: {}
};
</script>
<style>
/* yl_welore/pages/packageD/user_msg/index.wxss */
</style>
