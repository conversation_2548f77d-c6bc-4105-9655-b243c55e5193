<template>
    <view>
        <view class="nav-wrap">
            <cu-custom :isSearch="false" :isBack="true">
                <view slot="backText">返回</view>
                <view slot="content" class="nav-title">提现</view>
            </cu-custom>
            <view class="wallet-info-container">
                <view class="padding">
                    <view class="wallet-card">
                        <view class="wallet-item">
                            <image src="/static/yl_welore/style/icon/qianbao.png" class="wallet-icon"></image>
                            <text class="wallet-text">我的钱包</text>
                        </view>
                        <view class="wallet-item">
                            <image src="/static/yl_welore/style/icon/zhuanzhang.png" class="transfer-icon"></image>
                        </view>
                        <view v-if="setting.open_offline_payment == 0" class="wallet-item">
                            <image src="/static/yl_welore/style/icon/lingqian.png" class="payment-icon"></image>
                            <text class="wallet-text">微信零钱</text>
                        </view>
                        <view v-if="setting.open_offline_payment == 1" class="wallet-item">
                            <image src="/static/yl_welore/style/icon/yinhangka.png" class="payment-icon"></image>
                            <text class="wallet-text">支付宝</text>
                        </view>
                    </view>
                </view>
            </view>
        </view>
        <view class="input-section">
            <view class="balance-info">
                <text class="balance-text">
                    可用余额：{{ setting.user_info.conch }}，最低提现额度：{{ setting.lowest_money }}，提现将会扣除提现金额的{{
                        setting.payment_tariff * 100 }}%作为手续费。
                </text>
            </view>
            <view class="amount-input-container">
                <text class="currency-symbol">￥</text>
                <input type="digit" :value="withdraw_money" @input="get_money" placeholder="请输入提现金额" maxlength="10"
                    class="amount-input" />
            </view>
            <view class="account-input-container" v-if="setting.open_offline_payment == 1">
                <input type="text" :value="withdraw_number" @input="get_withdraw_card" placeholder="请输入支付宝账号"
                    class="account-input" />
            </view>
            <navigator url="/yl_welore/pages/packageC/notice/index" hover-class="none">
                <view class="notice-link">提现说明</view>
            </navigator>
        </view>
        <!-- <navigator url="/yl_welore/pages/packageC/withdrawal_list/index" hover-class="none">
            <view class="detail-link">
                <i-icon type="prompt" />
                提现明细
            </view>
        </navigator> -->
        <button @tap="withdrawFun" class="confirm-button">确定提现</button>

        <!-- 提现记录列表 -->
        <view class="withdrawal-list-container">
            <view class="list-header">
                <text class="list-title">提现记录</text>
                <text class="list-subtitle">最近的提现申请记录</text>
            </view>

            <view class="list-content" v-if="withdrawalList.length > 0">
                <view class="list-item" v-for="(item, index) in withdrawalList" :key="index">
                    <view class="item-left">
                        <view class="item-type">
                            <image class="type-icon"
                                :src="item.withdraw_type == 0 ? '/static/yl_welore/style/icon/lingqian.png' : '/static/yl_welore/style/icon/yinhangka.png'">
                            </image>
                            <text class="type-text">{{ item.withdraw_type == 0 ? '微信' : '支付宝' }}提现</text>
                        </view>
                        <view class="item-info">
                            <text class="item-time">{{ item.seek_time }}</text>
                            <text class="item-status" :class="[getStatusClass(item.status)]">{{
                                getStatusText(item.status) }}</text>
                        </view>
                    </view>
                    <view class="item-right">
                        <view class="item-amount">￥{{ item.display_money }}</view>
                        <view @click="confirmWechatPayment(item)"
                            v-if="item.withdraw_type == 0 && item.status == 1 && item.is_paid == 0"
                            class="confirm-btn">确认收款</view>
                    </view>
                </view>
            </view>

            <view class="list-empty" v-else>
                <text class="empty-text">暂无提现记录</text>
            </view>

            <view class="list-loading" v-if="listLoading">
                <text class="loading-text">加载中...</text>
            </view>
        </view>

        <view :class="'cu-modal ' + (withdraw ? 'show' : '')">
            <view class="cu-dialog">
                <view class="cu-bar bg-white justify-end">
                    <view class="content">提现确认</view>
                    <view class="action" @tap="hideModal">
                        <text class="cuIcon-close text-red"></text>
                    </view>
                </view>
                <view class="padding-xl">确定要提现吗？</view>
                <view class="cu-bar bg-white justify-end">
                    <view class="action">
                        <button class="cu-btn line-green text-green" @tap="hideModal">取消</button>
                        <button class="cu-btn bg-green margin-left" @tap="withdraw_do">确定</button>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>
<!-- <script module="filters" lang="wxs" src="@/yl_welore/pages/packageC/withdrawal/tofix.wxs"></script> -->
<script>
var app = getApp();
var http = require('../../../util/http.js');
export default {
    data() {
        return {
            setting: {
                open_offline_payment: 0,
                user_info: {
                    conch: ''
                },
                lowest_money: '',
                payment_tariff: 0
            },
            withdraw: false,
            withdraw_money: '',
            withdraw_number: '',
            page: 0,
            // 提现列表相关数据
            withdrawalList: [],
            listLoading: false,
            listPage: 1,
            hasMore: true,
            PayInfo: {}
        };
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        this.page = 1;
        this.get_raws_setting();
        this.getWithdrawalList();
    },
    /**
     * 用户点击右上角分享
     */
    onShareAppMessage() {
        var forward = app.globalData.forward;
        console.log(forward);
        if (forward) {
            return {
                title: forward.title,
                path: '/yl_welore/pages/index/index',
                imageUrl: forward.reis_img
            };
        } else {
            return {
                title: '您的好友给您发了一条信息',
                path: '/yl_welore/pages/index/index'
            };
        }
    },

    methods: {
        /**
         * 获取提现配置
         */
        get_raws_setting() {
            var b = app.globalData.api_root + 'User/get_raws_setting';
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.uid = e.uid;
            http.POST(b, {
                params: params,
                success: function (res) {
                    that.setting = res.data;
                },
                fail: function () {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) { }
                    });
                }
            });
        },

        /**
         * 记录卡号
         */
        get_withdraw_card(e) {
            this.withdraw_number = e.detail.value;
        },

        /**
         * 记录提醒金额
         */
        get_money(e) {
            //var str2 = e.detail.value.replace('.', '');
            this.withdraw_money = e.detail.value;
        },

        /**
         * 提现确认
         */
        withdrawFun() {
            if (this.setting.open_offline_payment == 1) {
                if (this.withdraw_number == '') {
                    uni.showToast({
                        title: '支付宝账号不能为空',
                        icon: 'none',
                        duration: 2000
                    });
                    return false;
                }
            }
            if (this.withdraw_money == '' || this.withdraw_money <= 0) {
                uni.showToast({
                    title: '提现金额不正确',
                    icon: 'none',
                    duration: 2000
                });
                return false;
            }
            this.withdraw = true;
        },

        hideModal() {
            this.withdraw = false;
        },

        /**
         * 确认提现
         */
        withdraw_do() {
            this.hideModal();
            uni.showLoading({
                title: '提现中...'
            });
            if (this.setting.open_offline_payment == 1) {
                if (this.withdraw_number == '') {
                    uni.showToast({
                        title: '支付宝账号不能为空',
                        icon: 'none',
                        duration: 2000
                    });
                    return false;
                }
            }
            if (this.withdraw_money == '' || this.withdraw_money <= 0) {
                uni.showToast({
                    title: '提现金额不正确',
                    icon: 'none',
                    duration: 2000
                });
                return false;
            }
            var b = app.globalData.api_root + 'User/withdraw';
            var that = this;
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.uid = e.uid;
            params.withdraw_number = this.withdraw_number;
            params.withdraw_money = this.withdraw_money;
            http.POST(b, {
                params: params,
                success: function (res) {
                    console.log(res);
                    that.hideModal();
                    uni.hideLoading();
                    if (res.data.status == 'success') {
                        // uni.showToast({
                        //     title: res.data.msg,
                        //     icon: 'none',
                        //     duration: 2000
                        // });
                        // that.Transfer(res.data.info);
                        that.withdrawalList = [];
                        that.listPage = 1;
                        that.getWithdrawalList();
                    } else {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                    that.withdraw_money = '';
                    that.withdraw_number = '';
                    that.get_raws_setting();
                },
                fail: function () {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) { }
                    });
                }
            });
        },

        navbackFun() {
            uni.navigateBack();
        },
        Transfer(item) {
            var that = this;
            uni.requestMerchantTransfer({
                mchId: that.PayInfo.app_mchid,
                appId: that.PayInfo.app_id,
                package: item.package_info,
                openId: that.setting.user_info.openid,
                success(res) {
                    console.log(res);
                    that.insTk(item);
                },
                fail(res) {
                    console.log(res);
                    uni.showToast({
                        title: '收款确认失败',
                        icon: 'none',
                        duration: 2000
                    });
                }
            })
        },
        insTk(item) {
            var that = this;
            const b = app.globalData.api_root + 'Withdraw/withdraw_update';
            const e = app.globalData.getCache('userinfo');
            const params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.mch_order = item.mch_order;
            params.wxpay_transfer_num = item.wxpay_transfer_num;
            http.POST(b, {
                params: params,
                success: (res) => {
                    uni.showToast({
                        title: '收款确认成功',
                        icon: 'success',
                        duration: 2000
                    });
                    that.withdrawalList = [];
                    that.listPage = 1;
                    that.getWithdrawalList();
                },
                fail: () => {
                    this.listLoading = false;
                    uni.showModal({
                        title: '提示',
                        content: '提现失败！',
                        showCancel: false,
                        success: function (res) { }
                    });
                }
            });
        },
        /**
         * 获取提现列表
         */
        getWithdrawalList(loadMore = false) {
            const b = app.globalData.api_root + 'User/get_withdraw_list';
            const e = app.globalData.getCache('userinfo');
            const params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.uid = e.uid;
            params.page = this.listPage;

            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    this.listLoading = false;
                    this.withdrawalList.push(...res.data.list);
                    this.PayInfo = res.data.info;
                },
                fail: () => {
                    this.listLoading = false;
                    uni.showModal({
                        title: '提示',
                        content: '获取提现记录失败，请稍候重试！',
                        showCancel: false,
                        success: function (res) { }
                    });
                }
            });
        },

        /**
         * 加载更多列表
         */
        loadMoreList() {
            if (this.hasMore && !this.listLoading) {
                this.listPage++;
                this.getWithdrawalList(true);
            }
        },

        /**
         * 微信收款确认
         */
        confirmWechatPayment(item) {
            console.log(item);
            this.Transfer(item);
        },

        /**
         * 获取状态文本
         */
        getStatusText(status) {
            switch (status) {
                case 0: return '审核中';
                case 1: return '已提现';
                case 2: return '审核未通过';
                default: return '未知状态';
            }
        },

        /**
         * 获取状态样式类
         */
        getStatusClass(status) {
            switch (status) {
                case 0: return 'status-pending';
                case 1: return 'status-success';
                case 2: return 'status-failed';
                default: return '';
            }
        }
    }
};
</script>
<style>
page {
    background-color: #f8f9fa;
}

/* 顶部导航样式 */
.nav-wrap {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.08);
}

.nav-title {
    color: #495057;
    font-weight: 600;
    font-size: 36rpx;
}

/* 钱包信息容器 */
.wallet-info-container {
    width: 100%;
    min-height: 90px;
    margin: 0px auto;
    color: #495057;
    font-size: 14px;
}

.wallet-card {
    background: #ffffff;
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    text-align: center;
    border-radius: 16rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
    padding: 20rpx 0;
}

.wallet-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20rpx 10rpx;
}

.wallet-icon {
    width: 60rpx;
    height: 60rpx;
    margin-bottom: 10rpx;
}

.transfer-icon {
    width: 40rpx;
    height: 32rpx;
    margin-bottom: 10rpx;
}

.payment-icon {
    width: 50rpx;
    height: 50rpx;
    margin-bottom: 10rpx;
}

.wallet-text {
    font-size: 24rpx;
    color: #6c757d;
    font-weight: 500;
}

/* 输入区域样式 */
.input-section {
    background-color: #ffffff;
    margin: 20rpx;
    border-radius: 16rpx;
    padding: 40rpx 30rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.balance-info {
    margin-bottom: 40rpx;
}

.balance-text {
    font-size: 28rpx;
    color: #6c757d;
    line-height: 1.6;
}

.amount-input-container {
    display: flex;
    align-items: center;
    margin-bottom: 30rpx;
    padding: 20rpx 0;
    border-bottom: 2rpx solid #e9ecef;
}

.currency-symbol {
    font-size: 60rpx;
    color: #495057;
    font-weight: 600;
    margin-right: 20rpx;
}

.amount-input {
    flex: 1;
    font-size: 40rpx;
    color: #495057;
    border: none;
    outline: none;
    background: transparent;
}

.account-input-container {
    margin-bottom: 30rpx;
}

.account-input {
    width: 100%;
    padding: 25rpx 20rpx;
    font-size: 32rpx;
    color: #495057;
    border: 2rpx solid #e9ecef;
    border-radius: 12rpx;
    background: #f8f9fa;
    transition: all 0.3s ease;
    height: 100rpx;
}

.account-input:focus {
    border-color: #007bff;
    background: #ffffff;
    box-shadow: 0 0 0 6rpx rgba(0, 123, 255, 0.1);
}

.notice-link {
    text-align: center;
    padding: 20rpx;
    color: #007bff;
    font-size: 28rpx;
    font-weight: 500;
}

/* 明细链接样式 */
.detail-link {
    margin-top: 30rpx;
    color: #6c757d;
    font-size: 28rpx;
    text-align: center;
    padding: 20rpx;
}

/* 确认按钮样式 */
.confirm-button {
    width: 80%;
    height: 88rpx;
    line-height: 88rpx;
    border-radius: 44rpx;
    margin: 60rpx auto 40rpx;
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: #ffffff;
    font-size: 32rpx;
    font-weight: 600;
    border: none;
    box-shadow: 0 8rpx 24rpx rgba(0, 123, 255, 0.3);
    transition: all 0.3s ease;
}

.confirm-button:active {
    transform: translateY(2rpx);
    box-shadow: 0 4rpx 12rpx rgba(0, 123, 255, 0.4);
}

button::after {
    display: none;
}

button {
    line-height: normal;
    display: block;
    padding: 0;
    background-color: transparent;
    font-size: 30rpx;
    overflow: inherit;
}

/* 提现列表样式 */
.withdrawal-list-container {
    margin-top: 40rpx;
    background-color: #ffffff;
    border-radius: 20rpx;
    margin-left: 20rpx;
    margin-right: 20rpx;
    overflow: hidden;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.list-header {
    padding: 30rpx 30rpx 20rpx 30rpx;
    border-bottom: 1px solid #f5f5f5;
}

.list-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333333;
    display: block;
    margin-bottom: 10rpx;
}

.list-subtitle {
    font-size: 24rpx;
    color: #999999;
}

.list-content {
    padding: 0 30rpx;
}

.list-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx 0;
    border-bottom: 1px solid #f8f8f8;
}

.list-item:last-child {
    border-bottom: none;
}

.item-left {
    flex: 1;
}

.item-type {
    display: flex;
    align-items: center;
    margin-bottom: 10rpx;
}

.type-icon {
    width: 40rpx;
    height: 40rpx;
    margin-right: 15rpx;
}

.type-text {
    font-size: 28rpx;
    color: #333333;
    font-weight: 500;
}

.item-info {
    display: flex;
    align-items: center;
}

.item-time {
    font-size: 24rpx;
    color: #666666;
    margin-right: 20rpx;
}

.item-status {
    font-size: 24rpx;
    padding: 4rpx 12rpx;
    border-radius: 12rpx;
}

.status-pending {
    background-color: #fff3e0;
    color: #ff9933;
}

.status-success {
    background-color: #e8f5e8;
    color: #33cc99;
}

.status-failed {
    background-color: #ffeaea;
    color: #cc3333;
}

.item-right {
    text-align: right;
}

.item-amount {
    font-size: 32rpx;
    color: #54b835;
    font-weight: 600;
    margin-bottom: 10rpx;
}

.confirm-btn {
    background-color: #4481eb;
    color: #ffffff;
    border: none;
    border-radius: 20rpx;
    padding: 8rpx 20rpx;
    font-size: 24rpx;
    line-height: 1.2;
}

.list-empty {
    text-align: center;
    padding: 80rpx 0;
}

.empty-icon {
    width: 120rpx;
    height: 120rpx;
    margin-bottom: 20rpx;
}

.empty-text {
    font-size: 28rpx;
    color: #999999;
}

.list-loading {
    text-align: center;
    padding: 40rpx 0;
}

.loading-text {
    font-size: 28rpx;
    color: #999999;
}

.load-more {
    text-align: center;
    padding: 30rpx 0;
    border-top: 1px solid #f5f5f5;
}

.load-more-text {
    font-size: 28rpx;
    color: #4481eb;
}
</style>
