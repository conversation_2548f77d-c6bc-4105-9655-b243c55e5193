<template>
    <view>
        <template v-if="compName === 'el'">
            <block v-if="n.name === 'img'">
                <image
                    v-if="(opts[1] && !ctrl[i]) || ctrl[i] < 0"
                    :show-menu-by-longpress="true"
                    class="_img"
                    :style="n.attrs.style"
                    :src="ctrl[i] < 0 ? opts[2] : opts[1]"
                    mode="widthFix"
                />
                <image
                    :id="n.attrs.id"
                    :class="'_img ' + n.attrs.class"
                    :style="(ctrl[i] === -1 ? 'display:none;' : '') + 'width:' + (ctrl[i] || 1) + 'px;height:1px;' + n.attrs.style"
                    :src="n.attrs.src"
                    :mode="n.h ? '' : 'widthFix'"
                    :lazy-load="opts[0]"
                    :webp="n.webp"
                    :show-menu-by-longpress="true"
                    :data-i="i"
                    @load="imgLoad"
                    @error="mediaError"
                    @tap.stop.prevent="imgTap"
                    @longpress="noop"
                />
            </block>
            <text v-else-if="n.text" decode style="cursor: auto; user-select: text; -webkit-user-select: text">{{ n.text }}</text>
            <text v-else-if="n.name === 'br'">\n</text>

            <view
                v-else-if="n.name === 'a'"
                :id="n.attrs.id"
                :class="(n.attrs.href ? '_a ' : '') + n.attrs.class"
                hover-class="_hover"
                :style="'display:inline;' + n.attrs.style"
                :data-i="i"
                @tap.stop.prevent="linkTap"
            >
                <node :childs="n.children" :opts="opts" />
            </view>

            <block v-else-if="n.name === 'div' && n.attrs.class == 'stealth_module' && n.attrs.yl_id == 0">
                <view class="bg-stripes-grey" style="color: #ffffff; padding: 40rpx; background-color: #aaaaaa; text-align: center; margin: 20rpx">
                    <text style="background-color: #8799a3; color: #ffffff; padding: 10rpx">这里是隐藏内容，需要回复才能看到</text>
                </view>
            </block>

            <block v-else-if="n.name === 'div' && n.attrs.class == 'stealth_module' && !n.attrs.yl_id">
                <view class="bg-stripes-olive stealth_module_true">
                    <node :childs="n.children" :opts="opts" />
                </view>
            </block>

            <video
                v-else-if="n.name === 'video'"
                :id="n.attrs.id"
                :class="n.attrs.class"
                :style="n.attrs.style"
                :autoplay="n.attrs.autoplay"
                :controls="n.attrs.controls"
                :loop="n.attrs.loop"
                :muted="n.attrs.muted"
                :poster="n.attrs.poster"
                :src="n.src[ctrl[i] || 0]"
                :data-i="i"
                @play="play"
                @error="mediaError"
            />

            <audio
                v-else-if="n.name === 'audio'"
                :id="n.attrs.id"
                :class="n.attrs.class"
                :style="n.attrs.style"
                :author="n.attrs.author"
                :controls="n.attrs.controls"
                :loop="n.attrs.loop"
                :name="n.attrs.name"
                :poster="n.attrs.poster"
                :src="n.src[ctrl[i] || 0]"
                :data-i="i"
                @play="play"
                @error="mediaError"
            />

            <rich-text v-else :id="n.attrs.id" :style="n.f" :nodes="[n]"></rich-text>
        </template>
    </view>
</template>

<script>
import node from './node';
export default {
    components: {
        node
    },
    props: ['data', 'compName'],
    computed: {
        n() {
            return this.data.n;
        },
        opts() {
            return this.data.opts;
        },
        ctrl() {
            return this.data.ctrl;
        },
        i() {
            return this.data.i;
        },
        imgLoad() {
            return this.data.imgLoad;
        },
        mediaError() {
            return this.data.mediaError;
        },
        imgTap() {
            return this.data.imgTap;
        },
        noop() {
            return this.data.noop;
        },
        linkTap() {
            return this.data.linkTap;
        },
        play() {
            return this.data.play;
        }
    }
};
</script>
<style></style>
