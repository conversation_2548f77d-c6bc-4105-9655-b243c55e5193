<template>
    <view>
        <view class="page"></view>
        <cu-custom bgColor="transparent" :isSearch="false" :isBack="true">
            <view slot="content" style="color: #2c2b2b; font-weight: 600; font-size: 36rpx">我的剧集</view>
        </cu-custom>
        <view style="padding: 30rpx 30rpx 80rpx 30rpx">
            <view class="flex justify-start">
                <view>
                    <image :src="info.poster_url" style="width: 230rpx; border-radius: 10rpx" mode="widthFix"></image>
                </view>
                <view style="margin-left: 20rpx">
                    <view style="font-size: 38rpx; font-weight: 600">{{ info.title }}</view>
                    <view v-if="info.alias != ''" style="font-size: 28rpx; margin-top: 10rpx">别名：{{ info.alias }}</view>
                    <view style="margin-top: 20rpx; font-weight: 400">全{{ info.total_episodes }}集</view>
                    <view style="margin-top: 20rpx">
                        <view class="cu-tag bg-blue light sm" v-for="(item, index) in info.type_list" :key="index">{{ item.name }}</view>
                    </view>
                </view>
            </view>
            <view class="tt">
                <view v-if="info.director != ''">导演：{{ info.director }}</view>
                <view v-if="info.screenwriter != ''">编剧：{{ info.screenwriter }}</view>
                <view v-if="info.lead_actors != ''">主演：{{ info.lead_actors }}</view>
                <view v-if="info.production_country != ''">制片国家/地区：{{ info.production_country }}</view>
                <view v-if="info.language != ''">语言：{{ info.language }}</view>
                <view v-if="info.release_date != ''">上映日期：{{ info.release_date }}</view>
                <view v-if="info.duration_minutes != 0">短剧片长：{{ info.duration_minutes }}分钟</view>
            </view>
            <view style="height: 1rpx; width: 100%; margin: 0 auto; background-color: #eeeeee; margin-top: 30rpx"></view>
            <view style="margin-top: 40rpx; font-weight: 600; font-size: 32rpx">简介</view>
            <view style="margin-top: 20rpx; line-height: 45rpx">{{ info.plot_summary }}</view>
            <view style="height: 1rpx; width: 100%; margin: 0 auto; background-color: #eeeeee; margin-top: 30rpx"></view>
            <view style="margin-top: 40rpx; font-weight: 600; font-size: 32rpx">剧集</view>
            <view style="margin-top: 40rpx">
                <view class="flex justify-start" style="flex-wrap: wrap">
                    <view class="shadow jj" @tap="openJJ" :data-index="index" v-for="(item, index) in list" :key="index">
                        <text style="font-size: 24rpx">{{ item.msi_episode_number }}</text>

                        <image
                            v-if="item.is_allow_only_vip == 1"
                            src="/static/yl_welore/style/icon/bot_vip.png"
                            style="width: 40rpx; height: 40rpx; position: absolute; right: 1px; bottom: 1px"
                        ></image>

                        <image
                            v-if="item.is_allow_only_vip == 0 && item.paid_unlocking_type != 0"
                            src="/static/yl_welore/style/icon/bot_suo.png"
                            style="width: 40rpx; height: 40rpx; position: absolute; right: 1px; bottom: 1px"
                        ></image>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
var app = getApp();
var http = require('../../../util/http.js');
export default {
    /**
     * 页面的初始数据
     */
    data() {
        return {
            list: [],
            info: {},
            id: 0,
            count: 0
        };
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        this.id = options.id;
        this.getInfo();
    },
    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {},
    /**
     * 页面相关事件处理函数--监听用户下拉动作
     */
    onPullDownRefresh() {},
    /**
     * 页面上拉触底事件的处理函数
     */
    onReachBottom() {},
    methods: {
        openJJ(d) {
            console.log(d);
            var index = d.currentTarget.dataset.index;
            uni.navigateTo({
                url: '/yl_welore/pages/packageC/theatre_video/index?id=' + this.id + '&index=' + index + '&epis=' + this.list[index].id
            });
        },
        getInfo() {
            var that = this;
            var b = app.globalData.api_root + 'Microseries/getMyseriesInfo';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.openid = e.openid;
            params.id = this.id;
            http.POST(b, {
                params: params,
                success: function (res) {
                    console.log(res);
                    that.list = res.data.list;
                    that.info = res.data.info;
                },
                fail: function () {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        }
    }
};
</script>
<style>
.page {
    background-image: linear-gradient(to bottom, #ffedc7 0%, #fffdf9 50%, #fffdf8 100%);
    height: 100vh;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: -1;
}

.tt view {
    margin-top: 20rpx;
}

.jj {
    text-align: center;
    line-height: 90rpx;
    width: 90rpx;
    height: 90rpx;
    background-color: #dddddd;
    border-radius: 20rpx;
    color: #777777;
    font-weight: 600;
    margin: 10rpx;
    position: relative;
}
</style>
