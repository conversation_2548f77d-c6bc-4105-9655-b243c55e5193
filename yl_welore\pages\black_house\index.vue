<template>
    <view class="page">
        <view class="wx_user_login_box" style="padding-top: 8rem">
            <view class="wx_user_face">
                <icon type="warn" size="93"></icon>
            </view>
            <view class="wx_user_face" style="width: 100%">
                <text style="font-size: 48rpx; color: #f76260">您已被封禁</text>
            </view>
            <view class="wx_login_info" style="padding-bottom: 1em">
                <text :selectable="true" style="font-size: 28rpx; color: #999999">原因：{{ forbid_prompt }}</text>
            </view>
            <view class="wx_login_info">
                <button class="cu-btn round bg-red shadow lg" style="height: 100rpx; width: 70%" open-type="contact">点击联系管理员</button>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            forbid_prompt: '',
            open_id: ''
        };
    }
    /**
     * 生命周期函数--监听页面加载
     */,
    onLoad (options) {
        this.forbid_prompt = options.forbid_prompt;
        this.open_id = options.open_id;
        uni.hideShareMenu();
    },
    /**
     * 生命周期函数--监听页面显示
     */
    onShow () {
        uni.hideShareMenu();
    },
    methods: {}
};
</script>
<style>
page {
    background-color: #ffffff;
}
.wx_user_face {
    margin-top: 20rpx;
    text-align: center;
}
.wx_login_info {
    margin-top: 40rpx;
    text-align: center;
}
</style>
