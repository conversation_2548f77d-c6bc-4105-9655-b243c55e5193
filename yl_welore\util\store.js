'use strict';
const reduce = Function.bind.call(Function.call, Array.prototype.reduce);
const isEnumerable = Function.bind.call(Function.call, Object.prototype.propertyIsEnumerable);
const concat = Function.bind.call(Function.call, Array.prototype.concat);
const keys = Reflect.ownKeys;
if (!Object.values) {
    Object.values = function values(O) {
        return reduce(keys(O), (v, k) => concat(v, typeof k === 'string' && isEnumerable(O, k) ? [O[k]] : []), []);
    };
}
if (!Object.entries) {
    Object.entries = function entries(O) {
        return reduce(keys(O), (e, k) => concat(e, typeof k === 'string' && isEnumerable(O, k) ? [[k, O[k]]] : []), []);
    };
}
var TYPE_ARRAY = '[object Array]';
var TYPE_OBJECT = '[object Object]';
var _typeOf = function (e) {
    return Object.prototype.toString.call(e);
};
var _deepClone = function (e) {
    return JSON.parse(JSON.stringify(e));
};
var diff = function (e, t) {
    var n = 0 < arguments.length && void 0 !== e ? e : {};
    var o = 1 < arguments.length && void 0 !== t ? t : {};
    var r = {};
    updateDiff(n, o, '', r);
    nullDiff(n, o, '', r);
    return r;
};
var updateDiff = function r(e, t, n, o) {
    var i = 0 < arguments.length && void 0 !== e ? e : {};
    var a = 1 < arguments.length && void 0 !== t ? t : {};
    var s = 2 < arguments.length && void 0 !== n ? n : '';
    var l = 3 < arguments.length && void 0 !== o ? o : {};
    if (_typeOf(i) !== TYPE_ARRAY || ((_typeOf(a) !== TYPE_ARRAY || i.length === a.length) && _typeOf(a) === TYPE_ARRAY)) {
        Object.entries(i).forEach(function (e) {
            var t = e[0];
            var n = e[1];
            var o = '' === s ? t : s + '.' + t;
            if (_typeOf(i) === TYPE_ARRAY) {
                o = '' === s ? t : s + '[' + t + ']';
            }
            a.hasOwnProperty(t)
                ? (_typeOf(a[t]) === TYPE_OBJECT && _typeOf(i[t]) === TYPE_OBJECT) || (_typeOf(a[t]) === TYPE_ARRAY && _typeOf(i[t]) === TYPE_ARRAY)
                    ? r(i[t], a[t], o, l)
                    : a[t] !== i[t] && (l[o] = n)
                : (l[o] = n);
        });
        return l;
    }
    l[s] = i;
};
var nullDiff = function o(e, t, n, r) {
    var i = 0 < arguments.length && void 0 !== e ? e : {};
    var a = 1 < arguments.length && void 0 !== t ? t : {};
    var s = 2 < arguments.length && void 0 !== n ? n : '';
    var l = 3 < arguments.length && void 0 !== r ? r : {};
    if (_typeOf(i) !== TYPE_ARRAY || ((_typeOf(a) !== TYPE_ARRAY || i.length === a.length) && _typeOf(a) === TYPE_ARRAY)) {
        Object.entries(a).forEach(function (e) {
            var t = e[0];
            var n = '' === s ? t : s + '.' + t;
            if (_typeOf(i) === TYPE_ARRAY) {
                n = '' === s ? t : s + '[' + t + ']';
            }
            i.hasOwnProperty(t)
                ? ((_typeOf(a[t]) === TYPE_OBJECT && _typeOf(i[t]) === TYPE_OBJECT) || (_typeOf(a[t]) === TYPE_ARRAY && _typeOf(i[t]) === TYPE_ARRAY)) && o(i[t], a[t], n, l)
                : (l[n] = null);
        });
        return l;
    }
};
var name = 'wxministore';
var version = '1.2.9';
var description = '小程序全局状态管理工具';
var main = './lib/store.js';
var repository = {
    type: 'git',
    url: 'git+https://github.com/yx675258207/wxMiniStore.git'
};
var scripts = {
    start: 'rollup -c -w',
    build: 'rollup -c',
    test: 'mocha --require babel-core/register ./test/diff.test.js'
};
var files = ['lib'];
var keywords = ['store', 'wxstore', 'wxministore'];
var author = 'Leisure';
var license = 'MIT';
var bugs = {
    url: 'https://github.com/yx675258207/wxMiniStore/issues'
};
var homepage = 'https://github.com/yx675258207/wxMiniStore#readme';
var devDependencies = {
    '@rollup/plugin-json': '^4.0.0',
    'babel-core': '^6.26.3',
    'babel-plugin-external-helpers': '^6.22.0',
    'babel-preset-es2015': '^6.24.1',
    'babel-preset-stage-0': '^6.24.1',
    chai: '^4.2.0',
    mocha: '^6.2.2',
    rollup: '^1.27.5',
    'rollup-plugin-babel': '^3.0.7',
    'rollup-plugin-commonjs': '^10.1.0',
    'rollup-plugin-node-resolve': '^5.2.0',
    'rollup-plugin-uglify': '^6.0.3'
};
var pkg = {
    name: name,
    version: version,
    description: description,
    main: main,
    repository: repository,
    scripts: scripts,
    files: files,
    keywords: keywords,
    author: author,
    license: license,
    bugs: bugs,
    homepage: homepage,
    devDependencies: devDependencies
};
var classCallCheck = function (e, t) {
    if (!(e instanceof t)) {
        throw new TypeError('Cannot call a class as a function');
    }
};
var createClass = (function () {
    function o(e, t) {
        for (var n = 0; n < t.length; n++) {
            var o = t[n];
            o.enumerable = o.enumerable || false;
            o.configurable = true;
            if ('value' in o) {
                o.writable = true;
            }
            Object.defineProperty(e, o.key, o);
        }
    }
    return function (e, t, n) {
        if (t) {
            o(e.prototype, t);
        }
        if (n) {
            o(e, n);
        }
        return e;
    };
})();
var toConsumableArray = function (e) {
    if (Array.isArray(e)) {
        for (var t = 0, n = Array(e.length); t < e.length; t++) {
            n[t] = e[t];
        }
        return n;
    }
    return Array.from(e);
};
var Version = pkg.version;
console.log('当前wxministore版本：' + Version);
var Store = (function () {
    function b(e) {
        classCallCheck(this, b);
        this.version = Version;
        this.$state = {};
        this.$r = [];
        var t = e.openPart;
        var n = void 0 !== t && t;
        var u = e.behavior;
        var o = e.methods;
        var f = void 0 === o ? {} : o;
        var r = e.pageLisener;
        var a = void 0 === r ? {} : r;
        var i = e.nonWritable;
        var s = void 0 !== i && i;
        var l = e.debug;
        var c = void 0 === l || l;
        this.debug = c;
        this.$state = {};
        if (_typeOf(e.state) === TYPE_OBJECT) {
            this.$state = Object.assign({}, _deepClone(e.state));
        }
        this.$r = [];
        function p(e, t) {
            var n = 1 < arguments.length && void 0 !== t ? t : {};
            e.$store = {};
            var o = n.useProp;
            if (n.hasOwnProperty('useProp')) {
                (o && 'string' == typeof o) || _typeOf(o) === TYPE_ARRAY ? (e.$store.useProp = [].concat(o)) : (e.$store.useProp = []);
            }
            e.$store.useStore = g(n);
            if (g(n)) {
                that.$r.push(e);
                e.$store.useProp
                    ? e.setData({
                          $state: _filterKey(that.$state, e.$store.useProp, function (e, t) {
                              return e === t;
                          })
                      })
                    : e.setData({
                          $state: that.$state
                      });
            }
        }
        function h(t) {
            var e = that.$r.findIndex(function (e) {
                return e === t;
            });
            if (-1 < e) {
                that.$r.splice(e, 1);
            }
        }
        this.$openPart = n;
        var that = this;
        var y = ['data', 'onLoad', 'onShow', 'onReady', 'onHide', 'onUnload', 'onPullDownRefresh', 'onReachBottom', 'onShareAppMessage', 'onPageScroll', 'onTabItemTap'];
        var g = function (e) {
            return (true === n && true === (0 < arguments.length && void 0 !== e ? e : {}).useStore) || !n;
        };
        var v = Page;
        var m = Component;
        App.Page = function () {
            for (var e = arguments.length, t = Array(1 < e ? e - 1 : 0), n = 1; n < e; n++) {
                t[n - 1] = arguments[n];
            }
            var o = 0 < arguments.length && void 0 !== arguments[0] ? arguments[0] : {};
            if (g(o)) {
                o.data = Object.assign(o.data || {}, {
                    $state: that.$state
                });
            }
            Object.keys(f).forEach(function (t) {
                'function' != typeof f[t] ||
                    y.some(function (e) {
                        return e === t;
                    }) ||
                    (o[t] = f[t]);
            });
            var r = o.onLoad;
            o.onLoad = function () {
                p(this, o);
                if (r) {
                    r.apply(this, arguments);
                }
            };
            var i = o.onUnload;
            o.onUnload = function () {
                h(this);
                if (i) {
                    i.apply(this, arguments);
                }
            };
            Object.keys(a).forEach(function (t) {
                if (
                    'function' == typeof a[t] &&
                    y.some(function (e) {
                        return e === t;
                    })
                ) {
                    var e = o[t];
                    o[t] = function () {
                        a[t].apply(this, arguments);
                        if (e) {
                            e.apply(this, arguments);
                        }
                    };
                }
            });
            v.apply(void 0, [o].concat(t));
        };
        if (!s) {
            try {
                Page = App.Page;
            } catch (e) {
                console.log('CatchClause', e);
                console.log('CatchClause', e);
            }
        }
        App.Component = function () {
            for (var e = arguments.length, t = Array(1 < e ? e - 1 : 0), n = 1; n < e; n++) {
                t[n - 1] = arguments[n];
            }
            var o = 0 < arguments.length && void 0 !== arguments[0] ? arguments[0] : {};
            if (g(o)) {
                o.data = Object.assign(o.data || {}, {
                    $state: that.$state
                });
            }
            Object.keys(f).forEach(function (t) {
                'function' != typeof f[t] ||
                    y.some(function (e) {
                        return e === t;
                    }) ||
                    (o.methods || (o.methods = {}), (o.methods[t] = f[t]));
            });
            if (u) {
                o.behaviors = [u].concat(toConsumableArray(o.behaviors || []));
            }
            function r() {
                p(this, o);
                if (l) {
                    l.apply(this, arguments);
                }
            }
            function i() {
                h(this);
                if (c) {
                    c.apply(this, arguments);
                }
            }
            var a = o.lifetimes;
            var s = void 0 === a ? {} : a;
            var l = s.attached || o.attached;
            var c = s.detached || o.detached;
            _typeOf(o.lifetimes) === TYPE_OBJECT ? ((o.lifetimes.attached = r), (o.lifetimes.detached = i)) : ((o.attached = r), (o.detached = i));
            m.apply(void 0, [o].concat(t));
        };
        if (!s) {
            try {
                Component = App.Component;
            } catch (e) {
                console.log('CatchClause', e);
                console.log('CatchClause', e);
            }
        }
    }
    createClass(b, [
        {
            key: 'setState',
            value: function (e, t) {
                var n = 1 < arguments.length && void 0 !== t ? t : function () {};
                if (_typeOf(e) !== TYPE_OBJECT) {
                    throw new Error('setState的第一个参数须为object!');
                }
                if (this.debug && console.time) {
                    console.time('setState');
                }
                var o = this.$state;
                var r = setData(e, o);
                this.$state = r;
                if (0 < this.$r.length) {
                    var i = diff(r, o);
                    if (this.debug) {
                        console.log('diff后实际设置的值：', _deepClone(i));
                    }
                    var a = Object.keys(i);
                    if (0 < a.length) {
                        var s = {};
                        a.forEach(function (e) {
                            s['$state.' + e] = i[e];
                        });
                        var l = this.$r.map(function (t) {
                            if (t.$store.hasOwnProperty('useProp')) {
                                var n = _filterKey(s, t.$store.useProp, function (e, t) {
                                    return e === '$state.' + t || !!e.match(new RegExp('^[$]state.' + t + '[.|[]', 'g'));
                                });
                                return 0 < Object.keys(n).length
                                    ? new Promise(function (e) {
                                          t.setData(n, e);
                                      })
                                    : Promise.resolve();
                            }
                            return new Promise(function (e) {
                                t.setData(s, e);
                            });
                        });
                        Promise.all(l).then(n);
                    } else {
                        n();
                    }
                } else {
                    n();
                }
                if (this.debug && console.timeEnd) {
                    console.timeEnd('setState');
                }
            }
        },
        {
            key: 'getState',
            value: function () {
                return _deepClone(this.$state);
            }
        }
    ]);
    return b;
})();
var _filterKey = function (t, e, n) {
    var o = 1 < arguments.length && void 0 !== e ? e : [];
    var r = n;
    var i = {};
    Object.keys(t)
        .filter(function (t) {
            return o.some(function (e) {
                return r(t, e);
            });
        })
        .forEach(function (e) {
            i[e] = t[e];
        });
    return i;
};
var setData = function (e, t) {
    var n = _deepClone(t);
    var o = _deepClone(e);
    Object.keys(o).forEach(function (e) {
        dataHandler(e, o[e], n);
    });
    return n;
};
var dataHandler = function (e, t, n) {
    for (var o = pathHandler(e), r = n, i = 0; i < o.length - 1; i++) {
        keyToData(o[i], o[i + 1], r);
        r = r[o[i]];
    }
    r[o[o.length - 1]] = t;
};
var pathHandler = function (e) {
    for (var t = '', n = [], o = 0, r = e.length; o < r; o++) {
        if ('[' === e[0]) {
            throw new Error('key值不能以[]开头');
        }
        if (e[o].match(/\.|\[/g)) {
            cleanAndPush(t, n);
            t = '';
        }
        t += e[o];
    }
    cleanAndPush(t, n);
    return n;
};
var cleanAndPush = function (e, t) {
    var n = cleanKey(e);
    if ('' !== n) {
        t.push(n);
    }
};
var keyToData = function (e, t, n) {
    if ('' !== e) {
        var o = _typeOf(n[e]);
        'number' == typeof t && o !== TYPE_ARRAY ? (n[e] = []) : 'string' == typeof t && o !== TYPE_OBJECT && (n[e] = {});
    }
};
var cleanKey = function (e) {
    if (e.match(/\[\S+\]/g)) {
        var t = e.replace(/\[|\]/g, '');
        if (Number.isNaN(parseInt(t))) {
            throw new Error('[]中必须为数字');
        }
        return +t;
    }
    return e.replace(/\[|\.|\]| /g, '');
};
module.exports = Store;
//# sourceMappingURL=store.js.map
