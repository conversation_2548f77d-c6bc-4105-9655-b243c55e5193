<template>
    <view>
        <cu-custom :isCustom="true" bgColor="bg-white" :isSearch="false">
            <view slot="backText">返回</view>
            <view slot="content" style="color: #2c2b2b; font-weight: 600; font-size: 36rpx">内容点评</view>
        </cu-custom>
        <view class="bg-white" style="padding: 10px" v-if="open_user_dianping && list">
            <block v-for="(item, rr_index) in list" :key="rr_index">
                <view :class="'cu-list menu-avatar dasheds ' + (item.is_show == 0 ? 'border-orange' : 'border-blue')">
                    <view class="cu-item" @tap="open_comment_info" :data-index="rr_index">
                        <view class="cu-avatar round" :style="'width: 70rpx;height: 70rpx;background-image:url(' + item.user_head_sculpture + ');'"></view>
                        <view class="content" style="left: 120rpx; width: 70%">
                            <view class="text-grey">
                                <text class="text_num_1">{{ item.user_nick_name }}</text>
                                <text
                                    :class="'cicon-star ' + (item.assess_score > sss_index ? 'text-yellow' : 'text-grey')"
                                    style="margin-left: 5px; font-size: 20px; vertical-align: middle"
                                    v-for="(k_item, sss_index) in 5"
                                    :key="sss_index"
                                ></text>
                            </view>
                            <view class="text-gray text-sm flex">
                                <view class="text-cut">
                                    <text
                                        :class="'cu-tag ' + (item.is_show == 0 ? 'bg-orange' : 'bg-blue') + ' sm round'"
                                        style="vertical-align: middle; line-height: 30rpx; margin-left: 0rpx"
                                    >
                                        {{ item.is_show == 0 ? '私密点评' : '公开点评' }}
                                    </text>
                                    <text style="padding-left: 10rpx; vertical-align: middle">{{ item.assess_content }}</text>
                                </view>
                            </view>
                        </view>
                        <view class="action">
                            <view class="cicon-angle" style="font-size: 18px"></view>
                        </view>
                    </view>
                </view>
            </block>
            <view :class="'cu-load ' + (!di_msg ? 'loading' : 'over')"></view>
        </view>
        <view :class="'cu-modal ' + (CommentInfoMod ? 'show' : '')">
            <view class="cu-dialog">
                <view class="cu-bar bg-white justify-end">
                    <view class="content">
                        <text
                            :class="'cicon-star ' + (CommentInfo.assess_score > ss_index ? 'text-yellow' : 'text-grey')"
                            style="font-size: 25px; vertical-align: middle"
                            v-for="(item, ss_index) in 5"
                            :key="ss_index"
                        ></text>
                        <text style="text-align: center; font-size: 15px; vertical-align: middle; margin-left: 5px">{{ CommentName[CommentInfo.assess_score - 1] }}</text>
                    </view>
                    <view class="action" @tap="hideModal">
                        <text class="cuIcon-close text-red"></text>
                    </view>
                </view>
                <view class="padding">
                    <view style="letter-spacing: 0.5px; line-height: 25px">
                        {{ CommentInfo.assess_content }}
                    </view>
                    <view class="flex p-xs margin-bottom-sm mb-sm">
                        <view class="flex-sub" v-if="admin == 1">
                            <view @tap="DelComment" class="bg-red padding-sm margin-xs radius text-center shadow-blur">
                                <view class="text-lg text-white">删除</view>
                            </view>
                        </view>
                        <view class="flex-sub">
                            <view @tap="hideModal" class="bg-yellow padding-sm margin-xs radius text-center shadow-blur">
                                <view class="text-lg text-white">确定</view>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
var app = getApp();
import http from '../../../util/http.js';
export default {
    data() {
        return {
            id: 0,
            page: 1,
            list: [],
            open_user_dianping: false,
            CommentInfoMod: false,
            CommentInfo: {}
        };
    },
    onLoad(options) {
        this.id = options.id;
        this.get_v_list();
    },
    onShow() {
        var dianping = app.globalData.__PlugUnitScreen('6f7f2e87a00f58c6d00817b72dd332c9');
        this.open_user_dianping = dianping;
    },
    methods: {
        DelComment() {
            uni.showModal({
                title: '提示',
                content: '确定要删除这个点评吗？',
                success: (res) => {
                    if (res.confirm) {
                        this.DelCommentDo();
                    }
                }
            });
        },
        /**
         * 删除
         */
        DelCommentDo() {
            var info = this.CommentInfo;
            uni.showLoading({
                title: '提交中...'
            });
            var b = app.globalData.api_root + 'Polls/del_comment';
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.com_id = info.id;
            params.paper_id = this.id;
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    uni.showToast({
                        title: res.data.msg,
                        icon: 'none',
                        duration: 2000
                    });
                    if (res.data.status == 'success') {
                        this.CommentInfoMod = false;
                        this.CommentInfo = '';
                    }
                    this.list = [];
                    this.CommentInfoMod = false;
                    this.page = 1;
                    this.CommentInfo = {};
                    this.get_v_list();
                    uni.hideLoading();
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        },
        /**
         * 隐藏模态对话框
         */
        hideModal: function () {
            this.CommentInfoMod = false;
        },
        open_comment_info(d) {
            console.log(d);
            var index = d.currentTarget.dataset.index;
            this.CommentInfoMod = true;
            this.CommentInfo = this.list[index];
        },
        get_v_list() {
            var e = app.globalData.getCache('userinfo');
            var params = new Object();
            params.token = e.token;
            params.openid = e.openid;
            params.id = this.id;
            var b = app.globalData.api_root + 'Whisper/get_v_list';
            http.POST(b, {
                params: params,
                success: (res) => {
                    if (res.data.info.length == 0 || res.data.info.length < 10) {
                        this.di_msg = true;
                    }
                    var list = this.list;
                    list.push(...res.data.info);
                    this.list = list;
                    this.admin = res.data.admin;
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: function (res) {}
                    });
                }
            });
        }
    }
};
</script>
<style>
page {
    background-color: #ffffff;
}
.dasheds {
    border: 3rpx dashed rgba(119, 119, 119, 0.25);
    border-radius: 10rpx;
    background-color: #fff;
    margin-top: 10px;
}

.border-orange {
    border-color: #fbbd08 !important;
}

.border-blue {
    border-color: #37c0fe !important;
}
</style>
