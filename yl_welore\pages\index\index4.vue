<template>
    <view v-if="new_list" class="waterfall-container">
        <view class="waterfall-wrapper">
            <view id="left" class="waterfall-column">
                <view class="post-card" v-for="(item, leftIndex) in leftList" :key="leftIndex">
                    <view v-if="item.top_time" class="top-badge">
                        <text class="top-text">置顶</text>
                    </view>

                    <view class="post-content" @tap="home_url" :data-index="item.globalIndex" :data-type="item.study_type"
                        :data-id="item.id" data-k="3">
                        <view v-if="item.image_part[0] && item.study_type != 2" class="post-image-container">
                            <image :lazy-load="true" :src="item.image_part[0]" class="post-image" mode="widthFix">
                            </image>
                        </view>
                        <view v-if="item.study_type == 2" class="video-container">
                            <view>
                                <view v-if="item.image_part.length > 0" class="video-thumbnail">
                                    <image :src="item.image_part[0]" mode="aspectFill" class="video-cover"></image>
                                    <view class="video-play-icon">
                                        <text class="cuIcon-videofill"></text>
                                    </view>
                                </view>
                                <view v-if="item.image_part.length == null || item.image_part.length == 0"
                                    class="video-placeholder">
                                    <view class="video-play-icon">
                                        <text class="cuIcon-videofill"></text>
                                    </view>
                                </view>
                            </view>
                        </view>
                        <view class="post-info">
                            <view class="post-tags">
                                <view v-if="item.red == 1 && version == 0" class="tag tag-red">福利</view>
                                <view v-if="item.study_type == 3" class="tag tag-activity">活动</view>
                                <view v-if="item.is_buy == 1 && version == 0" class="tag tag-paid">付费</view>
                            </view>
                            <view :data-index="item.globalIndex" :data-type="item.study_type" class="post-title">
                                <text v-if="item.gambit_name" @click.stop="gambit_list" :data-id="item.gambit_id"
                                    class="topic-tag">
                                    #{{ item.gambit_name }}
                                </text>
                                <rich-text class="title-content"
                                    :nodes="item.study_title == '' ? item.study_content : item.study_title"></rich-text>
                            </view>
                        </view>
                    </view>

                    <view class="user-info">
                        <view class="user-avatar-container" @click.stop="home_url" :data-index="item.globalIndex" data-k="1" :data-user_id="item.user_id">
                            <view  class="user-avatar" :style="'background-image:url(' + item.user_head_sculpture + ');'">
                                <image v-if="item.user_id != 0" class="avatar-frame" :src="item.avatar_frame"></image>
                            </view>
                            <view class="user-details">
                                <view class="user-name-row">
                                    <text :class="'username ' + (item.user_id != 0 ? item.special : '')">
                                        {{ item.user_nick_name }}
                                    </text>
                                    <image v-if="item.attr != ''" class="user-badge" :src="item.attr.attest.at_icon">
                                    </image>
                                    <image v-if="item.user_vip == 1 && item.user_id != 0"
                                        :src="http_root + 'addons/yl_welore/web/static/applet_icon/vip.png'"
                                        class="vip-badge"></image>
                                    <image v-if="item.user_id != 0" mode="heightFix" class="level-badge"
                                        :src="item.level"></image>
                                    <image mode="heightFix" class="merit-badge"
                                        v-if="item.wear_merit && item.user_id != 0" :src="item.wear_merit"></image>
                                </view>
                            </view>
                        </view>
                    </view>

                    <view class="interaction-bar">
                        <view class="interaction-item"
                            @click.stop="parseEventDynamicCode($event, item.is_open == 0 ? 'check_share' : '')">
                            <button hover-class="none" :open-type="item.is_open == 0 ? '' : 'share'"
                                :data-key="item.globalIndex" class="interaction-btn">
                                <text class="cuIcon-share interaction-icon"></text>
                                <text class="interaction-text">转发</text>
                            </button>
                        </view>
                        <view class="interaction-item">
                            <button hover-class="none"
                                @click.stop="parseEventDynamicCode($event, item.is_buy == 1 ? '' : 'add_zan')"
                                :data-id="item.id" :data-key="item.globalIndex" class="interaction-btn">
                                <text
                                    :class="'interaction-icon ' + (item.is_info_zan ? 'cuIcon-favorfill liked' : 'cuIcon-favor')"
                                    :animation="item.animationData_zan"></text>
                                <text class="interaction-text">
                                    {{ item.info_zan_count_this > 10000 ? item.info_zan_count : item.info_zan_count_this
                                    }}
                                </text>
                            </button>
                        </view>
                        <view class="interaction-item">
                            <button hover-class="none"
                                @click.stop="parseEventDynamicCode($event, item.is_buy == 1 ? '' : 'home_pl')"
                                :data-id="item.id" :data-key="item.globalIndex" class="interaction-btn">
                                <text class="cuIcon-message interaction-icon"></text>
                                <text class="interaction-text">
                                    {{ item.study_repount }}
                                </text>
                            </button>
                        </view>
                    </view>

                    <view class="post-footer">
                        <view class="role-badges">
                            <text v-if="item.check_qq == 'da' && item.user_id != 0" class="role-badge role-admin">
                                {{ design.qq_name }}主
                            </text>
                            <text v-if="item.check_qq == 'xiao' && item.user_id != 0" class="role-badge role-manager">
                                管理
                            </text>
                        </view>
                        <view @click.stop="home_url" :data-index="item.globalIndex" data-k="2" :data-id="item.tory_id" class="post-meta">
                            <text class="meta-text">{{ item.adapter_time }} · {{ item.realm_name }}</text>
                        </view>
                    </view>
                </view>
            </view>
            <view id="right" class="waterfall-column">
                <view class="post-card" v-for="(item, rightIndex) in rightList" :key="rightIndex">
                    <view v-if="item.top_time" class="top-badge">
                        <text class="top-text">置顶</text>
                    </view>
                    <view class="post-content" @click.stop="home_url" :data-index="item.globalIndex"
                        :data-type="item.study_type" :data-id="item.id" data-k="3">
                        <view v-if="item.image_part[0] && item.study_type != 2" class="post-image-container">
                            <image :lazy-load="true" :src="item.image_part[0]" class="post-image" mode="widthFix">
                            </image>
                        </view>
                        <view v-if="item.study_type == 2" class="video-container">
                            <view>
                                <view v-if="item.image_part.length > 0" class="video-thumbnail">
                                    <image :src="item.image_part[0]" mode="aspectFill" class="video-cover"></image>
                                    <view class="video-play-icon">
                                        <text class="cuIcon-videofill"></text>
                                    </view>
                                </view>
                                <view v-if="item.image_part.length == null || item.image_part.length == 0"
                                    class="video-placeholder">
                                    <view class="video-play-icon">
                                        <text class="cuIcon-videofill"></text>
                                    </view>
                                </view>
                            </view>
                        </view>
                        <view class="post-info">
                            <view class="post-tags">
                                <view v-if="item.red == 1 && version == 0" class="tag tag-red">福利</view>
                                <view v-if="item.study_type == 3" class="tag tag-activity">活动</view>
                                <view v-if="item.is_buy == 1 && version == 0" class="tag tag-paid">付费</view>
                            </view>
                            <view @click.stop="home_url" :data-index="item.globalIndex" data-k="3" :data-type="item.study_type" :data-id="item.id"
                                class="post-title">
                                <text v-if="item.gambit_name" @click.stop="gambit_list" :data-id="item.gambit_id"
                                    class="topic-tag">
                                    #{{ item.gambit_name }}
                                </text>
                                <rich-text class="title-content"
                                    :nodes="item.study_title == '' ? item.study_content : item.study_title"></rich-text>
                            </view>
                        </view>
                    </view>

                    <view class="user-info">
                        <view class="user-avatar-container"  @click.stop="home_url" :data-index="item.globalIndex" data-k="1" :data-user_id="item.user_id">
                            <view class="user-avatar" :style="'background-image:url(' + item.user_head_sculpture + ');'">
                                <image v-if="item.user_id != 0" class="avatar-frame" :src="item.avatar_frame"></image>
                            </view>
                            <view class="user-details">
                                <view class="user-name-row">
                                    <text :class="'username ' + (item.user_id != 0 ? item.special : '')">
                                        {{ item.user_nick_name }}
                                    </text>
                                    <image v-if="item.attr != ''" class="user-badge" :src="item.attr.attest.at_icon">
                                    </image>
                                    <image v-if="item.user_vip == 1 && item.user_id != 0"
                                        :src="http_root + 'addons/yl_welore/web/static/applet_icon/vip.png'"
                                        class="vip-badge"></image>
                                    <image v-if="item.user_id != 0" mode="heightFix" class="level-badge"
                                        :src="item.level"></image>
                                    <image mode="heightFix" class="merit-badge"
                                        v-if="item.wear_merit && item.user_id != 0" :src="item.wear_merit"></image>
                                </view>
                            </view>
                        </view>
                    </view>

                    <view class="interaction-bar">
                        <view class="interaction-item"
                            @click.stop="parseEventDynamicCode($event, item.is_open == 0 ? 'check_share' : '')">
                            <button hover-class="none" :open-type="item.is_open == 0 ? '' : 'share'"
                                :data-key="item.globalIndex" class="interaction-btn">
                                <text class="cuIcon-share interaction-icon"></text>
                                <text class="interaction-text">转发</text>
                            </button>
                        </view>
                        <view class="interaction-item">
                            <button hover-class="none"
                                @click.stop="parseEventDynamicCode($event, item.is_buy == 1 ? '' : 'add_zan')"
                                :data-id="item.id" :data-key="item.globalIndex" class="interaction-btn">
                                <text
                                    :class="'interaction-icon ' + (item.is_info_zan ? 'cuIcon-favorfill liked' : 'cuIcon-favor')"
                                    :animation="item.animationData_zan"></text>
                                <text class="interaction-text">
                                    {{ item.info_zan_count_this > 10000 ? item.info_zan_count : item.info_zan_count_this
                                    }}
                                </text>
                            </button>
                        </view>
                        <view class="interaction-item">
                            <button hover-class="none"
                                @click.stop="parseEventDynamicCode($event, item.is_buy == 1 ? '' : 'home_pl')"
                                :data-id="item.id" :data-key="item.globalIndex" class="interaction-btn">
                                <text class="cuIcon-message interaction-icon"></text>
                                <text class="interaction-text">
                                    {{ item.study_repount }}
                                </text>
                            </button>
                        </view>
                    </view>

                    <view class="post-footer">
                        <view class="role-badges">
                            <text v-if="item.check_qq == 'da' && item.user_id != 0" class="role-badge role-admin">
                                {{ design.qq_name }}主
                            </text>
                            <text v-if="item.check_qq == 'xiao' && item.user_id != 0" class="role-badge role-manager">
                                管理
                            </text>
                        </view>
                        <view @click.stop="home_url" :data-index="item.globalIndex" data-k="2" :data-id="item.tory_id" class="post-meta">
                            <text class="meta-text">{{ item.adapter_time }} · {{ item.realm_name }}</text>
                        </view>
                    </view>
                </view>
            </view>
        </view>
        <view class="load-more" :class="!di_msg ? 'loading' : 'over'">
            <text v-if="!di_msg" class="load-text">加载中...</text>
            <text v-else class="load-text">没有更多了</text>
        </view>
    </view>
</template>

<script>
export default {
    props: ['data', 'compName'],
    computed: {
        leftList() {
            return this.$parent.$data.leftList;
        },
        rightList() {
            return this.$parent.$data.rightList;
        },
        new_list() {
            return this.$parent.$data.new_list;
        },
        dataListindex() {
            return this.$parent.$data.dataListindex;
        },
        item() {
            return this.$parent.$data.item;
        },
        http_root() {
            return this.$parent.$data.http_root;
        },
        $state() {
            return this.$parent.$data.$state;
        },
        order_time() {
            return this.$parent.$data.order_time;
        },
        version() {
            return this.$parent.$data.version;
        },
        img() {
            return this.$parent.$data.img;
        },
        img_index() {
            return this.$parent.$data.img_index;
        },
        vo_index() {
            return this.$parent.$data.vo_index;
        },
        vo_item() {
            return this.$parent.$data.vo_item;
        },
        voi_item() {
            return this.$parent.$data.voi_item;
        },
        index() {
            return this.$parent.$data.index;
        },
        ad_info() {
            return this.$parent.$data.ad_info;
        },
        di_msg() {
            return this.$parent.$data.di_msg;
        }
    },
    methods: {
        home_url(e) {
            this.$emit('home-url', e);
        },
        gambit_list(e) {
            this.$emit('gambit-list', e);
        },
        dian_option(e) {
            this.$emit('dian-option', e);
        },
        vote_do(e) {
            this.$emit('vote-do', e);
        },
        play(e) {
            this.$emit('play', e);
        },
        stop(e) {
            this.$emit('stop', e);
        },
        sliderChange(e) {
            this.$emit('slider-change', e);
        },
        home_pl(e) {
            this.$emit('home-pl', e);
        },
        parseEventDynamicCode(e, type) {
            
            this.$emit('dynamic-code', e, type);
        }
    }
};
</script>

<style scoped>
/* 小红书风格瀑布流样式 */
.waterfall-container {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    padding: 20rpx;
}

.waterfall-wrapper {
    display: flex;
    gap: 16rpx;
}

.waterfall-column {
    flex: 1;
    display: flex;
    flex-direction: column;
}

/* 帖子卡片 */
.post-card {
    background: #ffffff;
    border-radius: 16rpx;
    margin-bottom: 16rpx;
    overflow: hidden;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    position: relative;
}

.post-card:active {
    transform: scale(0.98);
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.12);
}

/* 置顶标签 */
.top-badge {
    position: absolute;
    top: 12rpx;
    right: 12rpx;
    background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
    border-radius: 12rpx;
    padding: 4rpx 12rpx;
    z-index: 10;
}

.top-text {
    color: #ffffff;
    font-size: 20rpx;
    font-weight: 600;
}

/* 帖子内容区域 */
.post-content {
    position: relative;
}

.post-image-container {
    position: relative;
    overflow: hidden;
    border-radius: 16rpx 16rpx 0 0;
}

.post-image {
    width: 100%;
    display: block;
    transition: transform 0.3s ease;
}

.post-image:active {
    transform: scale(1.02);
}

/* 视频容器 */
.video-container {
    position: relative;
}

.video-thumbnail {
    position: relative;
    border-radius: 16rpx 16rpx 0 0;
    overflow: hidden;
    height: 300rpx;
}

.video-cover {
    width: 100%;
    height: 100%;
}

.video-placeholder {
    height: 200rpx;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 16rpx 16rpx 0 0;
}

.video-play-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80rpx;
    height: 80rpx;
    background: rgba(0, 0, 0, 0.6);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10rpx);
}

.video-play-icon .cuIcon-videofill {
    color: #ffffff;
    font-size: 40rpx;
}

/* 帖子信息区域 */
.post-info {
    padding: 20rpx;
}

.post-tags {
    display: flex;
    gap: 8rpx;
    margin-bottom: 12rpx;
    flex-wrap: wrap;
}

.tag {
    padding: 4rpx 12rpx;
    border-radius: 20rpx;
    font-size: 20rpx;
    font-weight: 500;
    color: #ffffff;
}

.tag-red {
    background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
}

.tag-activity {
    background: linear-gradient(45deg, #4facfe, #00f2fe);
}

.tag-paid {
    background: linear-gradient(45deg, #f093fb, #f5576c);
}

.post-title {
    line-height: 1.4;
}

.topic-tag {
    color: #007aff;
    font-weight: 600;
    font-size: 24rpx;
    margin-right: 8rpx;
}

.title-content {
    font-size: 28rpx;
    color: #333333;
    font-weight: 500;
    line-height: 1.5;
    word-break: break-word;
}

/* 用户信息 */
.user-info {
    padding: 0 20rpx 16rpx;
}

.user-avatar-container {
    display: flex;
    align-items: center;
    gap: 16rpx;
}

.user-avatar {
    width: 60rpx;
    height: 60rpx;
    border-radius: 50%;
    background-size: cover;
    background-position: center;
    position: relative;
    border: 2rpx solid #ffffff;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.avatar-frame {
    width: 60rpx;
    height: 60rpx;
    position: absolute;
    top: -2rpx;
    left: -2rpx;
}

.user-details {
    flex: 1;
}

.user-name-row {
    display: flex;
    align-items: center;
    gap: 8rpx;
    flex-wrap: wrap;
}

.username {
    font-size: 24rpx;
    color: #333333;
    font-weight: 600;
    max-width: 200rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.user-badge,
.vip-badge,
.level-badge,
.merit-badge {
    height: 32rpx;
    width: auto;
}

/* 互动按钮 */
.interaction-bar {
    display: flex;
    padding: 16rpx 20rpx;
    border-top: 1rpx solid #f5f5f5;
    background: #fafafa;
}

.interaction-item {
    flex: 1;
    display: flex;
    justify-content: center;
}

.interaction-btn {
    background: none;
    border: none;
    padding: 8rpx 0rpx;
    display: flex;
    align-items: center;
    gap: 8rpx;
    transition: background-color 0.2s ease;
}

.interaction-btn:active {
    background-color: rgba(0, 0, 0, 0.05);
}

.interaction-icon {
    font-size: 30rpx;
    color: #666666;
}

.interaction-icon.liked {
    color: #ff6b6b;
}

.interaction-text {
    font-size: 24rpx;
    color: #666666;
    font-weight: 500;
}

/* 帖子底部 */
.post-footer {
    padding: 12rpx 20rpx 20rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.role-badges {
    display: flex;
    gap: 8rpx;
}

.role-badge {
    padding: 4rpx 8rpx;
    border-radius: 8rpx;
    font-size: 20rpx;
    font-weight: 500;
    color: #ffffff;
}

.role-admin {
    background: linear-gradient(45deg, #9966ff, #bb6bd9);
}

.role-manager {
    background: linear-gradient(45deg, #4facfe, #00f2fe);
}

.post-meta {
    flex: 1;
    text-align: right;
}

.meta-text {
    font-size: 20rpx;
    color: #999999;
}

/* 加载更多 */
.load-more {
    text-align: center;
    padding: 40rpx 0 120rpx;
    margin-top: 20rpx;
}

.load-text {
    font-size: 24rpx;
    color: #999999;
}

.load-more.loading .load-text::after {
    content: '';
    display: inline-block;
    width: 20rpx;
    height: 20rpx;
    border: 2rpx solid #999999;
    border-top-color: transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 8rpx;
    vertical-align: middle;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* 响应式优化 */
@media (max-width: 750rpx) {
    .waterfall-wrapper {
        gap: 12rpx;
    }

    .post-card {
        border-radius: 12rpx;
        margin-bottom: 12rpx;
    }

    .post-info {
        padding: 16rpx;
    }
}
</style>
