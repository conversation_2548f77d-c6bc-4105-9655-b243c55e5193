<template>
    <view class="page-container">
        <cu-custom bgColor="none" :isSearch="false" :isBack="true" style="color: #ffffff;">
            <view slot="backText">返回</view>
            <view slot="content" class="header-title">个性装扮</view>
        </cu-custom>

        <!-- 优化后的用户信息展示 -->
        <view class="user-profile-section">
            <view class="profile-card">
                <view class="card-background">
                    <view class="card-pattern"></view>
                    <view class="card-shine"></view>
                </view>

                <view class="profile-content">
                    <!-- 头像区域 -->
                    <view class="avatar-section">
                        <view class="avatar-wrapper" style="width: 160rpx;">
                            <view class="cu-avatar round home main-avatar" :style="'background-image:url(' + user_info.user_head_sculpture + ');'"></view>
                            <image class="avatar-frame" v-if="avatar_style" :src="avatar_style"></image>
                        </view>
                    </view>

                    <!-- 用户信息 -->
                    <view class="user-info">
                        <view class="username-section">
                            <text class="username" :class="text_style">{{ user_info.user_nick_name }}</text>
                            <image class="medal-icon" v-if="this_img" :src="this_img" mode="heightFix"></image>
                        </view>

                        <!-- 积分信息 -->
                        <view class="points-section">
                            <view class="points-item">
                                <view class="points-icon">🏆</view>
                                <view class="points-info">
                                    <view class="points-label">荣誉点</view>
                                    <view class="points-value">{{ user_info.honor_point }}</view>
                                </view>
                            </view>
                            <view class="points-item">
                                <view class="points-icon">💎</view>
                                <view class="points-info">
                                    <view class="points-label">{{ $state.diy.confer }}</view>
                                    <view class="points-value">{{ user_info.fraction }}</view>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
        </view>

        <!-- 优化后的导航和内容区域 -->
        <view class="content-section">
            <view class="section-container">
                <!-- 导航标签 -->
                <view class="nav-container">
                    <view class="nav-tabs">
                        <view
                            :class="'nav-tab ' + (current == 'tab4' ? 'active' : '')"
                            @tap="handleChange"
                            data-key="tab4"
                        >
                            <text class="nav-icon">🖼️</text>
                            <text class="nav-text">头像框</text>
                        </view>
                        <view
                            :class="'nav-tab ' + (current == 'tab1' ? 'active' : '')"
                            @tap="handleChange"
                            data-key="tab1"
                        >
                            <text class="nav-icon">🏅</text>
                            <text class="nav-text">勋章列表</text>
                        </view>
                        <view
                            :class="'nav-tab ' + (current == 'tab3' ? 'active' : '')"
                            @tap="handleChange"
                            data-key="tab3"
                        >
                            <text class="nav-icon">✨</text>
                            <text class="nav-text">昵称特效</text>
                        </view>
                        <view
                            :class="'nav-tab ' + (current == 'tab2' ? 'active' : '')"
                            @tap="handleChange"
                            data-key="tab2"
                        >
                            <text class="nav-icon">📋</text>
                            <text class="nav-text">解锁记录</text>
                        </view>
                    </view>
                    <view class="nav-indicator" :class="'pos-' + current"></view>
                </view>

                <!-- 内容区域 -->
                <!-- 勋章列表 -->
                <view v-if="current == 'tab1'" class="tab-content">
                    <view class="medal-list">
                        <view
                            class="medal-item"
                            v-for="(item, i_index) in info"
                            :key="i_index"
                            :style="{ 'animation-delay': (i_index * 0.05) + 's' }"
                        >
                            <view class="medal-card">
                                <!-- 状态标识 -->
                                <view class="status-badge" v-if="user_info.wear_merit == item.id">
                                    <text class="status-text">使用中</text>
                                </view>

                                <view class="medal-content">
                                    <!-- 勋章图标 -->
                                    <view class="medal-icon-container" @tap="yulan" :data-id="i_index">
                                        <image class="medal-icon" :src="item.merit_icon" mode="heightFix"></image>
                                        <view class="icon-ring" :class="item.unlock == 1 ? 'unlocked' : 'locked'"></view>
                                    </view>

                                    <!-- 勋章信息 -->
                                    <view class="medal-info">
                                        <view class="medal-name">{{ item.merit_name }}</view>
                                    </view>

                                    <!-- 操作按钮 -->
                                    <view class="medal-actions">
                                        <button
                                            v-if="item.unlock == 1 && user_info.wear_merit != item.id"
                                            @tap="on_unlock"
                                            :data-key="i_index"
                                            class="action-btn wear-btn"
                                        >
                                            <text class="btn-icon">✨</text>
                                            <text class="btn-text">立即佩戴</text>
                                        </button>

                                        <button
                                            v-if="item.unlock == 0"
                                            @tap="get_unlock"
                                            :data-key="i_index"
                                            class="action-btn unlock-btn"
                                        >
                                            <text class="btn-icon">🔓</text>
                                            <text class="btn-text">{{ item.unlock_outlay }}解锁</text>
                                        </button>

                                        <button
                                            v-if="user_info.wear_merit == item.id"
                                            class="action-btn using-btn"
                                            disabled
                                        >
                                            <text class="btn-icon">✅</text>
                                            <text class="btn-text">正在使用</text>
                                        </button>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>

                <!-- 昵称特效 -->
                <view v-if="current == 'tab3'" class="tab-content">
                    <view class="text-effect-list">
                        <view
                            class="text-effect-item"
                            v-for="(item, t_index) in text_list"
                            :key="t_index"
                            :style="{ 'animation-delay': (t_index * 0.1) + 's' }"
                        >
                            <view class="effect-card">
                                <!-- 状态标识 -->
                                <view class="status-badge" v-if="user_info.wear_special_id == item.id">
                                    <text class="status-text">使用中</text>
                                </view>

                                <view class="effect-content">
                                    <view class="effect-name">{{ item.special_name }}</view>
                                    <view class="effect-preview">
                                        <view :class="item.special_style + ' preview-text'">{{ user_info.user_nick_name }}</view>
                                    </view>
                                    <view class="effect-actions">
                                        <button
                                            v-if="item.unlock == 1 && user_info.wear_special_id != item.id"
                                            @tap="on_unlock_text"
                                            :data-key="t_index"
                                            class="action-btn wear-btn"
                                        >
                                            <text class="btn-icon">✨</text>
                                            <text class="btn-text">立即装扮</text>
                                        </button>

                                        <button
                                            v-if="item.unlock == 0"
                                            @tap="get_text_unlock"
                                            :data-key="t_index"
                                            class="action-btn unlock-btn"
                                        >
                                            <text class="btn-icon">🏆</text>
                                            <text class="btn-text">{{ item.unlock_outlay }}解锁</text>
                                        </button>

                                        <button
                                            v-if="user_info.wear_special_id == item.id"
                                            class="action-btn using-btn"
                                            disabled
                                        >
                                            <text class="btn-icon">✅</text>
                                            <text class="btn-text">正在使用</text>
                                        </button>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>

                <!-- 头像框 -->
                <view v-if="current == 'tab4'" class="tab-content">
                    <view class="avatar-frame-list">
                        <view
                            class="frame-item"
                            v-for="(item, a_index) in avatar_list"
                            :key="a_index"
                            :style="{ 'animation-delay': (a_index * 0.1) + 's' }"
                        >
                            <view class="frame-card">
                                <!-- 状态标识 -->
                                <view class="status-badge" v-if="user_info.wear_af == item.id">
                                    <text class="status-text">使用中</text>
                                </view>

                                <view>
                                    <view class="frame-name">{{ item.adorn_name }}</view>
                                </view>

                                <view class="frame-preview-section">
                                    <view class="preview-avatar-container" style="width: 160rpx;">
                                        <view class="cu-avatar round home preview-avatar" :style="'background-image:url(' + user_info.user_head_sculpture + ');'"></view>
                                        <image class="preview-frame" :src="item.adorn_icon"></image>
                                    </view>
                                </view>

                                <view class="frame-actions">
                                    <button
                                        v-if="item.unlock == 1 && user_info.wear_af != item.id"
                                        @tap="on_unlock_avatar"
                                        :data-key="a_index"
                                        class="action-btn wear-btn"
                                    >
                                        <text class="btn-icon">✨</text>
                                        <text class="btn-text">立即装扮</text>
                                    </button>

                                    <button
                                        v-if="item.unlock == 0"
                                        @tap="get_avatar_unlock"
                                        :data-key="a_index"
                                        class="action-btn unlock-btn"
                                    >
                                        <text class="btn-icon">💎</text>
                                        <text class="btn-text">{{ item.unlock_fraction }}解锁</text>
                                    </button>

                                    <button
                                        v-if="user_info.wear_af == item.id"
                                        class="action-btn using-btn"
                                        disabled
                                    >
                                        <text class="btn-icon">✅</text>
                                        <text class="btn-text">正在使用</text>
                                    </button>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>

                <!-- 解锁记录 -->
                <view v-if="current == 'tab2'" class="tab-content">
                    <view class="unlock-records">
                        <view
                            class="record-item"
                            v-for="(item, s_index) in list"
                            :key="s_index"
                            :style="{ 'animation-delay': (s_index * 0.05) + 's' }"
                        >
                            <view class="record-card">
                                <view class="record-icon">🎖️</view>
                                <view class="record-content">
                                    <view class="record-title">解锁：{{ item.merit_name }}</view>
                                    <view class="record-time">{{ item.unlock_time }}</view>
                                </view>
                                <view class="record-cost">-{{ item.unlock_outlay }}</view>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
        </view>

        <!-- 头像框解锁弹窗 -->
        <view :class="'unlock-modal ' + (mod_avatar ? 'show' : '')">
            <view class="modal-overlay" @tap="hideModal"></view>
            <view class="modal-dialog">
                <view class="modal-header">
                    <view class="modal-title">
                        <text class="title-icon">🖼️</text>
                        <text class="title-text">解锁头像框</text>
                    </view>
                    <view class="close-btn" @tap="hideModal">
                        <text class="close-icon">✕</text>
                    </view>
                </view>
                <view class="modal-content">
                    <view class="item-preview">
                        <view class="preview-name">「{{ this_info.adorn_name }}」</view>
                    </view>
                    <view class="cost-info">
                        <view class="cost-label">解锁需要</view>
                        <view class="cost-amount">
                            <text class="cost-number">{{ this_info.unlock_fraction }}</text>
                            <text class="cost-unit">{{ $state.diy.confer }}</text>
                        </view>
                    </view>
                </view>
                <view class="modal-actions">
                    <button class="modal-btn cancel-btn" @tap="hideModal">
                        <text class="btn-text">取消</text>
                    </button>
                    <button class="modal-btn confirm-btn" @tap="do_unlock_avatar">
                        <text class="btn-icon">💎</text>
                        <text class="btn-text">确定解锁</text>
                    </button>
                </view>
            </view>
        </view>

        <!-- 勋章解锁弹窗 -->
        <view :class="'unlock-modal ' + (mod_unlock ? 'show' : '')">
            <view class="modal-overlay" @tap="hideModal"></view>
            <view class="modal-dialog">
                <view class="modal-header">
                    <view class="modal-title">
                        <text class="title-icon">🏅</text>
                        <text class="title-text">解锁勋章</text>
                    </view>
                    <view class="close-btn" @tap="hideModal">
                        <text class="close-icon">✕</text>
                    </view>
                </view>
                <view class="modal-content">
                    <view class="item-preview">
                        <view class="preview-name">「{{ this_info.merit_name }}」</view>
                        <view class="preview-desc">{{ this_info.merit_annotate }}</view>
                    </view>
                    <view class="cost-info">
                        <view class="cost-label">解锁需要</view>
                        <view class="cost-amount">
                            <text class="cost-number">{{ this_info.unlock_outlay }}</text>
                            <text class="cost-unit">荣誉点</text>
                        </view>
                    </view>
                </view>
                <view class="modal-actions">
                    <button class="modal-btn cancel-btn" @tap="hideModal">
                        <text class="btn-text">取消</text>
                    </button>
                    <button class="modal-btn confirm-btn" @tap="do_unlock">
                        <text class="btn-icon">🏆</text>
                        <text class="btn-text">确定解锁</text>
                    </button>
                </view>
            </view>
        </view>

        <!-- 昵称特效解锁弹窗 -->
        <view :class="'unlock-modal ' + (mod_text_unlock ? 'show' : '')">
            <view class="modal-overlay" @tap="hideModal"></view>
            <view class="modal-dialog">
                <view class="modal-header">
                    <view class="modal-title">
                        <text class="title-icon">✨</text>
                        <text class="title-text">解锁昵称特效</text>
                    </view>
                    <view class="close-btn" @tap="hideModal">
                        <text class="close-icon">✕</text>
                    </view>
                </view>
                <view class="modal-content">
                    <view class="item-preview">
                        <view class="preview-name">「{{ this_text_info.special_name }}」</view>
                    </view>
                    <view class="cost-info">
                        <view class="cost-label">解锁需要</view>
                        <view class="cost-amount">
                            <text class="cost-number">{{ this_text_info.unlock_outlay }}</text>
                            <text class="cost-unit">荣誉点</text>
                        </view>
                    </view>
                </view>
                <view class="modal-actions">
                    <button class="modal-btn cancel-btn" @tap="hideModal">
                        <text class="btn-text">取消</text>
                    </button>
                    <button class="modal-btn confirm-btn" @tap="do_text_unlock">
                        <text class="btn-icon">🏆</text>
                        <text class="btn-text">确定解锁</text>
                    </button>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import http from '../../../util/http.js';
const app = getApp();
export default {
    data() {
        return {
            current: 'tab4',
            height: app.globalData.height,

            user_info: {
                user_head_sculpture: '',
                gender: 0,
                user_nick_name: '',
                honor_point: '',
                fraction: '',
                wear_merit: '',
                wear_special_id: '',
                wear_af: ''
            },

            this_img: '',
            mod_unlock: false,

            this_info: {
                adorn_name: '',
                unlock_fraction: '',
                merit_name: '',
                merit_annotate: '',
                unlock_outlay: ''
            },

            list: [],
            avatar_list: [],
            text_list: [],

            //文字样式列表
            text_style: '',

            avatar_style: '',
            mod_text_unlock: false,

            this_text_info: {
                special_name: '',
                unlock_outlay: ''
            },

            this_text_key: 0,
            list_a: '',
            this_key: '',
            mod_avatar: false,
            info: '',
            i_index: 0,
            t_index: 0,
            a_index: 0,
            s_index: 0
        };
    },
    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
        // this.id = options.id;
        this.get_user_medal();
        this.get_text_style();
        this.get_avatar_frame();
    },
    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {},
    methods: {
        /**
         * 使用
         */
        on_unlock(dd) {
            const key = dd.currentTarget.dataset.key;
            const info = this.info[key];
            if (info['id'] != 0) {
                this.user_info.wear_merit = info['id'];
                this.this_img = info['merit_icon'];
            } else {
                this.user_info.wear_merit = info['id'];
                this.this_img = '';
            }
            const b = app.globalData.api_root + 'Task/user_on_unlock';
            const e = app.globalData.getCache('userinfo');
            const params = {
                token: e.token,
                openid: e.openid,
                id: info['id']
            };
            http.POST(b, {
                params: params,
                success: (res) => {
                    if (res.data.status != 'success') {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => {}
                    });
                }
            });
        },

        /**
         * 解锁
         */
        get_unlock(dd) {
            const key = dd.currentTarget.dataset.key;
            this.mod_unlock = true;
            this.this_info = this.info[key];
            this.this_key = key;
        },

        get_avatar_unlock(dd) {
            const key = dd.currentTarget.dataset.key;
            this.mod_avatar = true;
            this.this_info = this.avatar_list[key];
            this.this_key = key;
        },

        /**
         * 解锁
         */
        get_text_unlock(dd) {
            const key = dd.currentTarget.dataset.key;
            this.mod_text_unlock = true;
            this.this_text_info = this.text_list[key];
            this.this_text_key = key;
        },

        hideModal() {
            this.mod_unlock = false;
            this.mod_text_unlock = false;
            this.mod_avatar = false;
        },

        do_unlock_avatar() {
            this.hideModal();
            const b = app.globalData.api_root + 'Task/user_do_unlock_avatar';
            const e = app.globalData.getCache('userinfo');
            const params = {
                token: e.token,
                openid: e.openid,
                id: this.this_info['id']
            };
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    if (res.data.status == 'success') {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                        this.get_user_medal();
                        this.get_avatar_frame();
                    } else {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => {}
                    });
                }
            });
        },

        /**
         * 解锁确定
         */
        do_unlock() {
            this.hideModal();
            const b = app.globalData.api_root + 'Task/user_do_unlock';
            const e = app.globalData.getCache('userinfo');
            const params = {
                token: e.token,
                openid: e.openid,
                id: this.this_info['id']
            };
            http.POST(b, {
                params: params,
                success: (res) => {
                    if (res.data.status == 'success') {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                        this.get_user_medal();
                    } else {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => {}
                    });
                }
            });
        },

        /**
         * 解锁文字确定
         */
        do_text_unlock() {
            this.hideModal();
            const b = app.globalData.api_root + 'Task/user_do_text_unlock';
            const e = app.globalData.getCache('userinfo');
            const params = {
                token: e.token,
                openid: e.openid,
                id: this.this_text_info['id']
            };
            http.POST(b, {
                params: params,
                success: (res) => {
                    if (res.data.status == 'success') {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                        this.get_user_medal();
                        this.get_text_style();
                    } else {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => {}
                    });
                }
            });
        },

        /**
         * 预览
         */
        yulan(key) {
            const key_val = key.currentTarget.dataset.id;
            if (key_val == 0) {
                this.this_img = '';
                return;
            }
            const this_img = this.info[key_val]['merit_icon'];
            this.this_img = this_img;
        },

        //立即装扮头像框
        on_unlock_avatar(dd) {
            const key = dd.currentTarget.dataset.key;
            const info = this.avatar_list[key];
            this.user_info.wear_af = info['id'];
            this.avatar_style = info['adorn_icon'];
            const b = app.globalData.api_root + 'Task/user_on_unlock_avatar';
            const e = app.globalData.getCache('userinfo');
            const params = {
                token: e.token,
                openid: e.openid,
                id: info['id']
            };
            http.POST(b, {
                params: params,
                success: (res) => {
                    if (res.data.status != 'success') {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => {}
                    });
                }
            });
        },

        /**
         * 使用
         */
        on_unlock_text(dd) {
            const key = dd.currentTarget.dataset.key;
            const info = this.text_list[key];
            this.user_info.wear_special_id = info['id'];
            this.text_style = info['special_style'];
            const b = app.globalData.api_root + 'Task/user_on_unlock_text';
            const e = app.globalData.getCache('userinfo');
            const params = {
                token: e.token,
                openid: e.openid,
                id: info['id']
            };
            http.POST(b, {
                params: params,
                success: (res) => {
                    if (res.data.status != 'success') {
                        uni.showToast({
                            title: res.data.msg,
                            icon: 'none',
                            duration: 2000
                        });
                    }
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => {}
                    });
                }
            });
        },

        //头像框特效
        get_avatar_frame() {
            const b = app.globalData.api_root + 'Task/get_avatar_frame';
            const e = app.globalData.getCache('userinfo');
            const params = {
                token: e.token,
                openid: e.openid
            };
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    this.avatar_list = res.data.info;
                    this.avatar_style = res.data.this_avatar;
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => {}
                    });
                }
            });
        },

        //昵称特效
        get_text_style() {
            const b = app.globalData.api_root + 'Task/get_text_style';
            const e = app.globalData.getCache('userinfo');
            const params = {
                token: e.token,
                openid: e.openid
            };
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    this.text_list = res.data;
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => {}
                    });
                }
            });
        },

        //勋章
        get_user_medal() {
            const b = app.globalData.api_root + 'Task/get_user_medal';
            const e = app.globalData.getCache('userinfo');
            const params = {
                token: e.token,
                openid: e.openid
            };
            http.POST(b, {
                params: params,
                success: (res) => {
                    console.log(res);
                    this.list = res.data.list;
                    this.info = res.data.info;
                    this.this_img = res.data.this_img;
                    this.text_style = res.data.this_text_style;
                    this.user_info = res.data.user_info;
                },
                fail: () => {
                    uni.showModal({
                        title: '提示',
                        content: '网络繁忙，请稍候重试！',
                        showCancel: false,
                        success: (res) => {}
                    });
                }
            });
        },

        handleChange(detail) {
            const key = detail.currentTarget.dataset.key;
            this.current = key;
        }
    }
};
</script>
<style>
page {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    min-height: 100vh;
}

.page-container {
    background: transparent;
    padding-bottom: 80rpx;
}

/* 头部标题样式 */
.header-title {
    color: #ffffff;
    font-weight: 600;
    font-size: 36rpx;
}

/* 用户信息展示 */
.user-profile-section {
    padding: 30rpx 20rpx;
}

.profile-card {
    position: relative;
    background: #ffffff;
    border-radius: 32rpx;
    overflow: hidden;
    box-shadow: 0 20rpx 60rpx rgba(102, 126, 234, 0.2);
}

.card-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    opacity: 0.05;
}

.card-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: radial-gradient(circle at 20% 50%, rgba(255, 255, 255, 0.1) 2rpx, transparent 2rpx),
                      radial-gradient(circle at 80% 50%, rgba(255, 255, 255, 0.1) 2rpx, transparent 2rpx);
    background-size: 60rpx 60rpx;
}

.card-shine {
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    animation: shine 3s infinite;
}

@keyframes shine {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.profile-content {
    position: relative;
    padding: 50rpx 40rpx;
    text-align: center;
    z-index: 2;
}

/* 头像区域 */
.avatar-section {
    margin-bottom: 40rpx;
}

.avatar-wrapper {
    position: relative;
    display: inline-block;
}

.main-avatar {
    width: 120rpx !important;
    height: 120rpx !important;
    position: relative;
    z-index: 3;
}

.gender-badge {
    position: absolute;
    top: -8rpx;
    right: -8rpx;
    width: 40rpx;
    height: 40rpx;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 4;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
}

.gender-badge.female {
    background: linear-gradient(135deg, #ff6b9d 0%, #ff8fab 100%);
}

.gender-badge.male {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.gender-icon {
    color: #ffffff;
    font-size: 24rpx;
    font-weight: 700;
}

.avatar-frame {
    position: absolute;
    top: -20rpx;
    left: 0rpx;
    right: 0;
    width: 160rpx;
    height: 160rpx;
    z-index: 10;
    margin: 0 auto;
}

.avatar-ring {
    position: absolute;
    top: -12rpx;
    left: -12rpx;
    width: 154rpx;
    height: 154rpx;
    border: 4rpx solid transparent;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea, #764ba2, #f093fb) border-box;
    mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
    z-index: 1;
}

.avatar-glow {
    position: absolute;
    top: -20rpx;
    left: -20rpx;
    width: 160rpx;
    height: 160rpx;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea, #764ba2, #f093fb);
    opacity: 0.3;
    animation: glow 2s ease-in-out infinite alternate;
    z-index: 0;
}

@keyframes glow {
    from { transform: scale(1); opacity: 0.3; }
    to { transform: scale(1.1); opacity: 0.1; }
}

/* 用户信息 */
.user-info {
    text-align: center;
}

.username-section {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 30rpx;
}

.username {
    font-size: 36rpx;
    font-weight: 700;
    color: #333333;
    margin-right: 16rpx;
}

.medal-icon {
    height: 40rpx;
    width: auto;
}

/* 积分信息 */
.points-section {
    display: flex;
    justify-content: space-around;
    gap: 20rpx;
}

.points-item {
    flex: 1;
    background: linear-gradient(135deg, #f8f9ff 0%, #fff0f8 100%);
    border-radius: 20rpx;
    padding: 24rpx 20rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2rpx solid rgba(102, 126, 234, 0.1);
}

.points-icon {
    font-size: 32rpx;
    margin-right: 12rpx;
}

.points-info {
    text-align: left;
}

.points-label {
    font-size: 22rpx;
    color: #999999;
    margin-bottom: 4rpx;
}

.points-value {
    font-size: 28rpx;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* 内容区域 */
.content-section {
    padding: 0 20rpx;
    margin-bottom: 40rpx;
}

.section-container {
    background: #ffffff;
    border-radius: 24rpx;
    overflow: hidden;
    box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.08);
}

/* 导航标签 */
.nav-container {
    background: linear-gradient(135deg, #f8f9ff 0%, #fff0f8 100%);
    padding: 20rpx;
    position: relative;
    overflow: hidden;
}

.nav-tabs {
    display: flex;
    position: relative;
    z-index: 2;
}

.nav-tab {
    flex: 1;
    padding: 20rpx 10rpx;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.nav-tab.active .nav-text {
    color: #ffffff;
    font-weight: 700;
}

.nav-tab.active .nav-icon {
    transform: scale(1.2);
}

.nav-icon {
    font-size: 24rpx;
    margin-bottom: 8rpx;
    transition: all 0.3s ease;
}

.nav-text {
    font-size: 24rpx;
    font-weight: 600;
    color: #666666;
    transition: all 0.3s ease;
}

/* 导航指示器 */
.nav-indicator {
    position: absolute;
    top: 20rpx;
    left: 20rpx;
    width: calc(25% - 10rpx);
    height: calc(100% - 40rpx);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16rpx;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1;
}

.nav-indicator.pos-tab1 {
    transform: translateX(calc(100%));
}

.nav-indicator.pos-tab2 {
    transform: translateX(calc(300%));
}

.nav-indicator.pos-tab3 {
    transform: translateX(calc(200%));
}

.nav-indicator.pos-tab4 {
    transform: translateX(0);
}

/* 标签页内容 */
.tab-content {
    padding: 30rpx 20rpx;
    min-height: 400rpx;
}

/* 勋章列表 */
.medal-list {
    display: flex;
    flex-direction: column;
    gap: 20rpx;
}

.medal-item {
    opacity: 0;
    animation: slideInUp 0.6s ease forwards;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30rpx);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.medal-card {
    background: #ffffff;
    border-radius: 20rpx;
    padding: 30rpx;
    position: relative;
    border: 2rpx solid #f0f2f5;
    transition: all 0.3s ease;
}

.medal-card:active {
    transform: translateY(2rpx) scale(0.98);
    box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
}

.medal-content {
    display: flex;
    align-items: center;
    gap: 30rpx;
}

.status-badge {
    position: absolute;
    top: -8rpx;
    right: -8rpx;
    background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
    border-radius: 20rpx;
    padding: 4rpx 12rpx;
    z-index: 2;
}

.status-text {
    font-size: 20rpx;
    color: #ffffff;
    font-weight: 600;
}

.medal-icon-container {
    position: relative;
    display: inline-block;
    flex-shrink: 0;
}

.medal-icon {
    height: 80rpx;
    width: 80rpx;
    object-fit: contain;
}

.icon-ring {
    position: absolute;
    top: -8rpx;
    left: -8rpx;
    width: 96rpx;
    height: 96rpx;
    border: 2rpx solid transparent;
    border-radius: 50%;
    background: linear-gradient(135deg, #d9d9d9, #f0f0f0) border-box;
    mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
}

.icon-ring.unlocked {
    background: linear-gradient(135deg, #52c41a, #73d13d) border-box;
    mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
}

.medal-info {
    flex: 1;
    min-width: 0;
}

.medal-name {
    font-size: 28rpx;
    font-weight: 600;
    color: #333333;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
/* 按钮样式 */
.action-btn {
    width: 100%;
    padding: 10rpx 24rpx;
    border-radius: 16rpx;
    border: none;
    font-size: 24rpx;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    min-height: 80rpx;
    box-sizing: border-box;
    max-width: 240rpx;
}

.wear-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #ffffff;
}

.unlock-btn {
    background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
    color: #ffffff;
}

.using-btn {
    background: #f5f5f5;
    color: #999999;
}

.btn-icon {
    font-size: 20rpx;
    margin-right: 8rpx;
}

.btn-text {
    font-size: 24rpx;
}

/* 昵称特效列表 */
.text-effect-list {
    display: flex;
    flex-direction: column;
    gap: 20rpx;
}

.text-effect-item {
    opacity: 0;
    animation: slideInUp 0.6s ease forwards;
}

.effect-card {
    background: #ffffff;
    border-radius: 20rpx;
    padding: 30rpx;
    position: relative;
    border: 2rpx solid #f0f2f5;
    transition: all 0.3s ease;
}

.effect-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.effect-name {
    flex: 1;
    font-size: 28rpx;
    font-weight: 600;
    color: #333333;
    min-width: 0;
}

.effect-preview {
    flex: 2;
    text-align: center;
    min-width: 0;
}

.preview-text {
    font-size: 32rpx;
    font-weight: 700;
}

/* 头像框列表 */
.avatar-frame-list {
    display: flex;
    flex-direction: column;
    gap: 20rpx;
}

.frame-item {
    opacity: 0;
    animation: slideInUp 0.6s ease forwards;
}

.frame-card {
    background: #ffffff;
    border-radius: 20rpx;
    padding: 30rpx;
    position: relative;
    border: 2rpx solid #f0f2f5;
    transition: all 0.3s ease;
    text-align: center;
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.frame-name {
    font-size: 28rpx;
    font-weight: 600;
    color: #333333;
}

.frame-preview-section {
    margin-bottom: 30rpx;
    display: flex;
    justify-content: center;
}

.preview-avatar-container {
    position: relative;
    display: inline-block;
}

.preview-avatar {
    width: 100rpx !important;
    height: 100rpx !important;
}

.preview-frame {
    position: absolute;
    top: -10rpx;
    left: 0;
    right: 0;
    width: 120rpx;
    height: 120rpx;
    object-fit: contain;
    margin: 0 auto;
}

.frame-actions {
    display: flex;
    justify-content: center;
}
/* 解锁记录 */
.unlock-records {
    display: flex;
    flex-direction: column;
    gap: 16rpx;
}

.record-item {
    opacity: 0;
    animation: slideInUp 0.6s ease forwards;
}

.record-card {
    background: #ffffff;
    border-radius: 16rpx;
    padding: 24rpx;
    display: flex;
    align-items: center;
    border: 2rpx solid #f0f2f5;
    transition: all 0.3s ease;
}

.record-icon {
    font-size: 32rpx;
    margin-right: 20rpx;
}

.record-content {
    flex: 1;
}

.record-title {
    font-size: 28rpx;
    font-weight: 600;
    color: #333333;
    margin-bottom: 8rpx;
}

.record-time {
    font-size: 24rpx;
    color: #999999;
}

.record-cost {
    font-size: 28rpx;
    font-weight: 700;
    color: #ff4d4f;
}

/* 弹窗样式（复用身份铭牌的样式） */
.unlock-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.unlock-modal.show {
    opacity: 1;
    visibility: visible;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(10rpx);
}

.modal-dialog {
    background: #ffffff;
    border-radius: 32rpx;
    margin: 40rpx;
    max-width: 600rpx;
    width: 100%;
    overflow: hidden;
    transform: scale(0.8);
    transition: all 0.3s ease;
    position: relative;
    z-index: 2;
}

.unlock-modal.show .modal-dialog {
    transform: scale(1);
}

.modal-header {
    padding: 40rpx 40rpx 20rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: linear-gradient(135deg, #f8f9ff 0%, #fff0f8 100%);
}

.modal-title {
    display: flex;
    align-items: center;
}

.title-icon {
    font-size: 32rpx;
    margin-right: 16rpx;
}

.title-text {
    font-size: 32rpx;
    font-weight: 700;
    color: #333333;
}

.close-btn {
    width: 60rpx;
    height: 60rpx;
    border-radius: 50%;
    background: rgba(255, 77, 79, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.close-btn:active {
    transform: scale(0.9);
    background: rgba(255, 77, 79, 0.2);
}

.close-icon {
    font-size: 24rpx;
    color: #ff4d4f;
    font-weight: 700;
}

.modal-content {
    padding: 40rpx;
}

.item-preview {
    text-align: center;
    margin-bottom: 40rpx;
}

.preview-name {
    font-size: 36rpx;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 16rpx;
}

.preview-desc {
    font-size: 24rpx;
    color: #666666;
    line-height: 1.5;
}

.cost-info {
    text-align: center;
    padding: 30rpx;
    background: linear-gradient(135deg, #f8f9ff 0%, #fff0f8 100%);
    border-radius: 20rpx;
    border: 2rpx solid rgba(102, 126, 234, 0.1);
}

.cost-label {
    font-size: 24rpx;
    color: #999999;
    margin-bottom: 16rpx;
}

.cost-amount {
    display: flex;
    align-items: baseline;
    justify-content: center;
}

.cost-number {
    font-size: 48rpx;
    font-weight: 700;
    background: linear-gradient(135deg, #ff4d4f, #ff7875);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-right: 8rpx;
}

.cost-unit {
    font-size: 28rpx;
    color: #666666;
    font-weight: 600;
}

.modal-actions {
    padding: 20rpx 40rpx 40rpx;
    display: flex;
    gap: 20rpx;
}

.modal-btn {
    flex: 1;
    padding: 24rpx;
    border-radius: 20rpx;
    border: none;
    font-size: 28rpx;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.cancel-btn {
    background: #f5f5f5;
    color: #666666;
}

.cancel-btn:active {
    transform: scale(0.98);
    background: #e8e8e8;
}

.confirm-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #ffffff;
    box-shadow: 0 8rpx 25rpx rgba(102, 126, 234, 0.3);
}

.confirm-btn:active {
    transform: scale(0.98);
    box-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.4);
}

.btn-icon {
    font-size: 24rpx;
    margin-right: 8rpx;
}

.btn-text {
    font-size: 28rpx;
}

/* 占位符样式保留 */
.placeholder {
    margin: 10px 0px;
}
</style>
